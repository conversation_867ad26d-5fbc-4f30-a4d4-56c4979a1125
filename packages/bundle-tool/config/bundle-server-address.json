{"DEV": {"WEB_MOBILE": "https://dev.example.com/game/h5/", "IOS": "https://dev.example.com/game/native/", "ANDROID": "https://dev.example.com/game/native/", "BUNDLES": {"common": {"version": "1.0", "md5": "", "url": "http://192.168.1.28:30002/"}, "common-portrait": {"version": "", "md5": "", "url": "http://192.168.1.28:30003/", "dependencies": ["common"]}, "common-landscape": {"version": "", "md5": "", "url": "http://192.168.1.28:30002/", "dependencies": ["common"]}, "lobby-common": {"version": "", "md5": "", "url": "http://192.168.1.28:30002/", "dependencies": ["common"]}, "lobby-portrait": {"version": "", "md5": "", "url": "http://192.168.1.28:30002/", "dependencies": ["common-portrait", "lobby-common"]}, "lobby-landscape": {"version": "", "md5": "", "url": "http://192.168.1.28:30002", "dependencies": ["common-landscape", "lobby-common"]}, "texas-common": {"version": "", "md5": "", "url": "http://192.168.1.28:30002", "dependencies": ["common", "jackpot"]}, "texas-portrait": {"version": "", "md5": "", "url": "http://192.168.1.28:30002", "dependencies": ["common-portrait", "texas-common"]}, "texas-landscape": {"version": "", "md5": "", "url": "http://192.168.1.28:30002", "dependencies": ["common-landscape", "texas-common"]}, "mtt-bridge": {"version": "", "md5": "", "url": "http://192.168.1.28:30002"}, "jackpot": {"version": "", "md5": "", "url": "http://192.168.1.28:30002", "dependencies": ["common"]}, "debug": {"version": "", "md5": "", "url": "http://192.168.1.28:30002"}}}, "STAGE": {"WEB_MOBILE": "https://stage.example.com/game/h5/", "IOS": "https://stage.example.com/game/native/", "ANDROID": "https://stage.example.com/game/native/"}, "PROD": {"WEB_MOBILE": "https://prod.example.com/game/h5/", "IOS": "https://prod.example.com/game/native/", "ANDROID": "https://prod.example.com/game/native/"}}
# Bundle Tool User Guide (English)

## Introduction
Bundle Tool is a visual tool for managing, packaging, configuring dependencies, and generating manifests for Cocos Creator project bundles. It simplifies bundle configuration management, automates tasks, and improves efficiency.

## Features
- Visual management of all bundle version, MD5, URL, and dependencies
- One-click build, export, and manifest generation
- Multi-platform (web-mobile, ios, android) and multi-environment (dev, stage, prod) support
- Auto-sync local bundle directories and config
- Auto-save and restore cache
- Batch UUID replacement to avoid resource conflicts

## Quick Start
### 1. Create a .env File
Create a `.env` file in the project root folder with your CocosCreator path and build parameters. For example:
```bash
cocosCreatorPath="/Applications/CocosCreator/Creator/2.4.5/CocosCreator.app/Contents/MacOS/CocosCreator"
xxteakey=""
packageNameAndroid=""
keystorePath=""
keystorePassword=""
keystoreAlias=""
keystoreAliasPassword=""
```

### 2. Install Dependencies and Compile
Run in the root folder:
```bash
npm install
```
For TypeScript compilation:
```bash
tsc
```

### 3. Open the Bundle Tool Editor
In Cocos Creator, open the Pack Bundle panel via `BundleTool` / `Pack Bundle` in the top menu, or use `BundleTool` / `Replace uuid` for UUID replacement.

## Bundle Configuration & Manifest Generation
- **Version**: Specify the bundle manifest version
- **Remote Manifest Url**: URL of the remote manifest
- **Bundle Server Address**: Address of the bundle server
- **Source Bundle Directory**: Directory of the original CocosCreator build asset
- **Export Bundle Directory**: Directory for exporting bundles

### Bundles Management
- Edit each bundle’s version, url, MD5, and dependencies (dependencies field is an array, e.g., ["common-portrait", "lobby-common"])
- Supports multi-level dependencies, auto-written to the final manifest
- Dependencies can be edited in the UI or config files
- Check Include to select bundles for export

### Build & Export
- **Build**: Build the project and update the source bundle directory
- **Pack**: Pack configured bundles from source to export directory
- **Export**: Build and export the packed bundles
- Supports web-mobile(H5), ios, android platforms

### Batch UUID Replacement
- Use `BundleTool` / `Replace uuid` in the top menu to automatically replace all UUIDs under the `assets/` directory, avoiding resource conflicts (useful for project duplication).

## FAQ
- If errors occur, try deleting cache.json and restarting the tool
- If dependencies are not written correctly, ensure the dependencies field is an array
- For custom manifest structure, modify the bundle_manifest_generator.js script
- If the window is abnormal, delete `cache.json` and `dist` then run `npm install` again

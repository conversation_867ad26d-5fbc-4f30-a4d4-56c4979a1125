# Bundle Tool 使用说明（中文）

## 简介
Bundle Tool 是一个用于 Cocos Creator 项目资源包（bundle）管理、打包、依赖配置和 Manifest 生成的可视化工具。它简化了 bundle 配置管理流程，自动化多项任务，提高效率并减少出错概率。

## 主要功能
- 可视化管理所有 bundle 的版本、MD5、URL、依赖关系
- 一键打包、导出、生成 Manifest 文件
- 支持多平台（web-mobile、ios、android）和多环境（dev、stage、prod）
- 自动同步本地 bundle 目录与配置
- 支持缓存自动保存与恢复
- 支持 UUID 批量替换，防止资源冲突

## 快速开始
### 1. 创建 .env 文件
在项目根目录创建 `.env` 文件，配置 CocosCreator 路径及构建参数。例如：
```bash
cocosCreatorPath="/Applications/CocosCreator/Creator/2.4.5/CocosCreator.app/Contents/MacOS/CocosCreator"
xxteakey=""
packageNameAndroid=""
keystorePath=""
keystorePassword=""
keystoreAlias=""
keystoreAliasPassword=""
```

### 2. 安装依赖并编译
在根目录执行：
```bash
npm install
```
如需 TypeScript 编译：
```bash
tsc
```

### 3. 打开 Bundle Tool 编辑器
在 Cocos Creator 顶部菜单点击 `BundleTool` / `Pack Bundle` 打开打包面板，或点击 `BundleTool` / `Replace uuid` 进行 UUID 替换。

## Bundle 配置与 Manifest 生成
- **Version**：指定 bundle manifest 版本号
- **Remote Manifest Url**：远程 manifest 地址
- **Bundle Server Address**：bundle 服务器地址
- **Source Bundle Directory**：CocosCreator 构建产物目录
- **Export Bundle Directory**：导出 bundle 目录

### Bundles 管理
- 可编辑每个 bundle 的 version、url、MD5、依赖（dependencies 字段为数组，如 ["common-portrait", "lobby-common"]）
- 支持多级依赖，自动写入最终 manifest
- 依赖可在界面或配置文件维护
- 选择 Include 导出所需 bundle

### 构建与导出
- **Build**：构建项目并更新源 bundle 目录
- **Pack**：将配置的 bundle 从源目录打包到导出目录
- **Export**：构建并导出打包好的 bundle
- 支持 web-mobile(H5)、ios、android 平台

### UUID 批量替换
- 在顶部菜单点击 `BundleTool` / `Replace uuid`，可自动替换 `assets/` 目录下所有 .meta 文件的 UUID，避免资源冲突，适合项目复制场景。

## 常见问题
- 配置或打包出错时，可删除 cache.json 后重启工具
- 依赖未正确写入？请确保 dependencies 字段为数组格式
- 如需自定义 manifest 结构，可修改 bundle_manifest_generator.js 脚本
- 如遇窗口异常，可删除 `cache.json` 和 `dist` 目录后重新 `npm install`

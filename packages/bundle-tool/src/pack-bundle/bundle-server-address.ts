import * as path from 'path';
import * as fs from 'fs';

// The JSON config should be placed at packages/bundle-tool/config/bundle-server-address.json
// Example JSON:
// {
//   "DEV": {
//     "WEB_MOBILE": "https://dev.example.com/game1/h5/",
//     "IOS": "https://dev.example.com/game1/native/",
//     "ANDROID": "https://dev.example.com/game1/native/"
//   },
//   "STAGE": { ... },
//   "PROD": { ... }
// }

export default class BundleServerAddress {
    private static configPath = path.resolve(__dirname, '../../config/bundle-server-address.json');
    private static urlMapping: Record<string, Record<string, string>> | null = null;

    private static loadConfig() {
        if (!this.urlMapping) {
            const raw = fs.readFileSync(this.configPath, 'utf-8');
            this.urlMapping = JSON.parse(raw);
        }
    }

    /**
     * Get bundle server address for a specific game.
     * @param env Environment (e.g. DEV, STAGE, PROD)
     * @param platform DevicePlatform (e.g. WEB_MOBILE, IOS, ANDROID)
     * @param gameKey Optional, if you want to support multiple games in one config, pass the game key.
     */
    static getBundleServerAddress(env: string, platform: string, gameKey?: string): string {
        this.loadConfig();
        env = env.toUpperCase();
        platform = platform.toUpperCase().replace('-', '_');
        if (!this.urlMapping) throw new Error('Bundle server address config not loaded');
        if (gameKey) {
            // If config is { gameKey: { ENV: { PLATFORM: url } } }
            const gameConfig = (this.urlMapping as any)[gameKey];
            if (!gameConfig) throw new Error(`No config for gameKey: ${gameKey}`);
            if (!gameConfig[env] || !gameConfig[env][platform]) throw new Error(`No url for env: ${env}, platform: ${platform}, game: ${gameKey}`);
            return gameConfig[env][platform];
        } else {
            // If config is { ENV: { PLATFORM: url } }
            if (!this.urlMapping[env] || !this.urlMapping[env][platform]) throw new Error(`No url for env: ${env}, platform: ${platform}`);
            return this.urlMapping[env][platform];
        }
    }
}

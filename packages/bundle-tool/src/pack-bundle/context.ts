import * as path from 'path';
import { BuiltPlatformDirectoryName, CocosNativeBuildTemplate, DevicePlatform, Environment, Project } from '../type';
import BundleServerAddress from './bundle-server-address';

export default class ContextConfig {
    static get(projectPath: string) {
        const cocosCreator = {
            EnvVariablesJSONPath: path.join(__dirname, '..', '..', '..', '..', 'temp', 'env_variables.json'),
            ProjectPath: projectPath,
            getBuiltPlatformPath: (platform: BuiltPlatformDirectoryName) => path.join(projectPath, 'build', platform),
            getBuiltPlatformPathAsset: (platform: BuiltPlatformDirectoryName) =>
                path.join(projectPath, 'build', platform, 'assets'),
            DefaultTemplateOption: CocosNativeBuildTemplate.LINK,
            DefaultTemplateOptions: [
                { value: CocosNativeBuildTemplate.LINK, label: CocosNativeBuildTemplate.LINK },
                { value: CocosNativeBuildTemplate.DEFAULT, label: CocosNativeBuildTemplate.DEFAULT }
            ]
        };

        const bundleTool = {
            getEnvFilePath: () => {
                const envPath = path.join(__dirname, '..', '..', '..', '..', '.env');
                Editor.warn(envPath);
                return envPath;
            },
            build: {
                DefaultProjectOption: Project.PKW,
                DefaultProjectOptions: [
                    { value: Project.PKW, label: Project.PKW },
                    { value: Project.WPK, label: Project.WPK }
                ],
                DefaultEnvironmentOption: Environment.DEV,
                DefaultEnvironmentOptions: [
                    { value: Environment.DEV, label: Environment.DEV },
                    { value: Environment.STAGE, label: Environment.STAGE },
                    { value: Environment.PROD, label: Environment.PROD }
                ],
                DefaultPlatformOption: DevicePlatform.WEB_MOBILE,
                DefaultPlatformOptions: [
                    { value: DevicePlatform.WEB_MOBILE, label: DevicePlatform.WEB_MOBILE },
                    { value: DevicePlatform.IOS, label: DevicePlatform.IOS },
                    { value: DevicePlatform.ANDROID, label: DevicePlatform.ANDROID }
                ]
            },
            packConfiguration: {
                getSourcePath: (cocosExportDirectory: BuiltPlatformDirectoryName) =>
                    path.join(projectPath, 'build', cocosExportDirectory, 'assets'),
                DefaultExportPath: path.join(projectPath, 'build', 'bundles'),
                getBundleJSONPath: (targetPath: string) => path.join(targetPath, '/bundle.json')
            },
            pack: {
                DefaultSourceData: {
                    version: '1.0.0',
                    remoteManifestUrl: '',
                    bundleServerAddress: BundleServerAddress.getBundleServerAddress(
                        Environment.DEV,
                        DevicePlatform.WEB_MOBILE
                    ),
                    bundles: {}
                }
            },
            generateVersionManifest: {
                ScriptPath: path.join(projectPath, 'packages', 'bundle-tool', 'tools', 'bundle_manifest_generator.js'),
                SourcePath: path.join(projectPath, 'build', BuiltPlatformDirectoryName.JSB_LINK),
                DefaultDestinationPath: path.join(projectPath, 'manifest'),
                DefaultVersion: '1.0.0'
            }
        };

        return { cocosCreator, bundleTool };
    }
}

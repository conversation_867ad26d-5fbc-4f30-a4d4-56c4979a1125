import { EmittableService } from '../core/core-index';
import type { IGetEventStatusResponse, ISocket, IEventStatusClient, Rebate} from '../poker-client/poker-client-index';
//import * as pf from '../pf';
import { ErrorMessageService } from './error-message-service';
import * as core from '../core/core';

export interface RebateEvents {
    eventStatusResult: (result: IEventStatusClient) => void;
    eventStatusStop: () => void;
    rebateRewardResult: (result: Rebate.IRebateNoticeMessage) => void;
    refreshEventStatus: () => void;
    eventLeaderboardResult: (result: Rebate.IGetLeaderboardResponse) => void;
}

export class RebateService extends EmittableService<RebateEvents> {
    static readonly serviceName = 'RebateService';

    private _socket: ISocket;

    private _rebateEvents: IEventStatusClient[] = [];
    get rebateEvents() {
        return this._rebateEvents;
    }

    private _rebateEventStatus: IGetEventStatusResponse = null;
    get rebateEventStatus() {
        return this._rebateEventStatus;
    }

    private _rebateLeaderboard: { [key: number]: Rebate.ILeaderboard };
    get rebateLeaderboards() {
        return this._rebateLeaderboard;
    }

    private _errorMessageService: ErrorMessageService = null;

    constructor(socket: ISocket) {
        super(RebateService.serviceName);
        this._socket = socket;
        this._socket.notification.on('rebateEventStatus', this.onEventStatusNotify.bind(this));
        this._socket.notification.on('rebateNotice', this.rebateClaimNotify.bind(this));

        this._errorMessageService = core.serviceManager.get(ErrorMessageService);
    }

    async getEventStatus(): Promise<IGetEventStatusResponse> {
        const response = await this._socket.getEventStatus();
        this._rebateEvents = response.events;
        return response;
    }

    async getLeaderBoard(): Promise<Rebate.IGetLeaderboardResponse> {
        const response = await this._socket.getRebateLeaderboard();
        this._rebateLeaderboard = response.leaderboards;
        return response;
    }

    onEventStatusNotify() {
        cc.log('rebate event status notify received');
        this.emit('refreshEventStatus');
    }

    rebateClaimNotify(data) {
        cc.log('rebate claim notify received');
        this.emit('rebateRewardResult', data);
    }
}

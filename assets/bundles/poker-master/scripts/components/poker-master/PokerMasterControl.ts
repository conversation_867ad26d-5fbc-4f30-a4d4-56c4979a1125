import * as pf from '../../../../../poker-framework/scripts/pf';
import * as cr from '../../../../common-resource/scripts/common-resource';
import { macros } from '../../common/poker-master-macros';
import * as domain from '../../domain/poker-master-domain-index';
import * as network from '../../network/poker-master-network-index';
import { CardSuit } from '../../domain/poker-master-domain-index';
import { PokerMasterReviewControl } from './PokerMasterReviewControl';
import { PokerMasterSquintCardControl } from './PokerMasterSquintCardControl';
import { PokerMasterChartControl } from './PokerMasterChartControl';
import { PokerMasterDef } from '../../common/poker-master-define';
import PlayerOneBet = pf.services.PlayerOneBet;
import Deque = pf.Deque;
import AvatarControl = cr.components.AvatarControl;
import PokerCardControl = cr.components.PokerCardControl;
import LuckTurntableButtonControl = cr.components.LuckTurntableButtonControl;
import MiniGameExchangeControl = cr.components.MiniGameExchangeControl;
import MiniGameRuleControl = cr.components.MiniGameRuleControl;
import MiniGameAudioSettingControl = cr.components.MiniGameAudioSettingControl;
import HumanboyDialogControl = cr.components.HumanboyDialogControl;
import MiniGameAdvancedAuto = cr.components.MiniGameAdvancedAuto;
import MiniGameGuideControl = cr.components.MiniGameGuideControl;
import HeadPointsAniControl = cr.components.HeadPointsAniControl;
import MiniGamePlayerListControl = cr.components.MiniGamePlayerListControl;
import MiniGameMenuControl = cr.components.MiniGameMenuControl;
import HumanboyToastControl = cr.components.HumanboyToastControl;
import HumanboyBetCoinControl = cr.components.HumanboyBetCoinControl;
import HumanboyRewardTipsControl = cr.components.HumanboyRewardTipsControl;
import HumanboyFlutterScoreControl = cr.components.HumanboyFlutterScoreControl;
import HumanboyAdvancedAutoControl = cr.components.HumanboyAdvancedAutoControl;
import MiniGameAdvancedSettingControl = cr.components.MiniGameAdvancedSettingControl;
import MiniGameCommonDef = cr.MiniGameCommonDef;
import MiniGameDialog = cr.components.MiniGameDialog;
import ConcreteAdvancedAuto = cr.components.ConcreteAdvancedAuto;
import IMiniGameDialog = cr.components.IMiniGameDialog;
import IMiniGameDialogConfig = cr.components.IMiniGameDialogConfig;
import ConcreteMiniGameDialog = cr.components.ConcreteMiniGameDialog;
import ThemeSystemType = cr.components.ThemeSystemType;
import PushNotification = pf.services.PushNotification;
// import CalmDownParams = pf.services.CalmDownParams;
import ConsumingPromptControl = cr.components.ConsumingPromptControl;
import RebateFloatingButtonControl = cr.components.RebateFloatingButtonControl;
import RebateRewardPopupControl = cr.components.RebateRewardPopupControl;
import type { DialogHubControl } from '../../../../common-resource/scripts/components/DialogHubControl';
import MiniGameDialogPopupId = cr.components.MiniGameDialogPopupId;
import TagControl = cr.components.TagControl;

import RebateCoinsFlyControl from '../../../../common-resource/scripts/components/rebate-promotion/RebateCoinsFlyControl';
import BettingRebateEventControl from '../../../../common-resource/scripts/components/rebate-promotion/BettingRebateEventControl';
/**
 * 下注区UI结构信息
 */
export class PokerMasterAreaInfo {
    zoneIndex: number = 0; // 区域索引
    zoneOption: network.BetZoneOption = network.BetZoneOption.BetZoneOption_DUMMY; // 区域枚举
    wayOutStyle: MiniGameCommonDef.eGameboyWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE; // 路子显示风格
    wayOutLoseLimitCount: number = 0; // 路单描述文本"xxx局"未出上限(超过上限显示: "xxx+ 局未出", 默认0表示无上限)
    panelArea: cc.Node = null; // 区域根节点
    panelCoin: cc.Node = null; // 金币层
    panelWayOut: cc.Node = null; // 路单层
    txtSelfBetNum: cc.Label = null; // 自己下注文本节点
    txtTotalBetNum: cc.Label = null; // 总下注文本节点
    rtxtWayOut: cc.Label = null; // 路子描述文本
    txtOdds: cc.Label = null; // 赔率
    coinQueue: Deque<HumanboyBetCoinControl> = new Deque(); // 复用金币双向队列
    wayOutImgArray: cc.Sprite[] = []; // 路子精灵数组
    wayOutImgSrcPosArray: cc.Vec2[] = []; // 路子精灵原始位置数组
}

/**
 * 百人德州 金币优化结构体
 */
export class HumanboyCoinOptimization {
    nAreaIdx: number = 0;
    nGold: number = 0;
    nUid: number = 0;
    bAnim: boolean = false;
    bHeadAnim: boolean = false;
    bPlaySound: boolean = false;
}

/**
 * 百人德州主界面8位玩家UI专属结构信息
 */
export class HumanboyPlayerInfo {
    uid: number = 0;
    imgBg: cc.Sprite = null;
    nodeHead: cc.Node = null;
    txtCoin: cc.Label = null;
    imgFlag: cc.Sprite = null; // 富豪/神算子
    avatarControl: AvatarControl = null;
}

/**
 * "扑克大师"主逻辑类
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class PokerMasterControl extends cc.Component {
    // @property(cc.Prefab) prefabCbWinPlayerLight: cc.Prefab = null; // 牛仔玩家赢亮框 预制件
    // @property(cc.Prefab) prefabCbExchange: cc.Prefab = null;
    // @property(cc.Prefab) prefabCbRule: cc.Prefab = null;                                                                      // 规则面板 预制件
    // @property(cc.Prefab) prefabCbSoundSetting: cc.Prefab = null;                                                              // 设置面板 预制件
    // @property(cc.Prefab) prefabCbExit: cc.Prefab = null;                                                                      // 退出面板 预制件

    @property(cc.Prefab) prefabCbRoundStart: cc.Prefab = null; // 开局动画 预制件
    @property(cc.Prefab) prefabPmShowOdds: cc.Prefab = null; // 显示赔率动画 预制件
    @property(cc.Prefab) prefabPmSquidCard: cc.Prefab = null; // 眯牌面板 预制件
    @property(cc.Prefab) prefabPmChart: cc.Prefab = null; // 路单面板 预制件
    @property(cc.Prefab) prefabPmReview: cc.Prefab = null; // 投注回顾面板

    // @property(cc.Prefab) prefabHbWinFlag: cc.Prefab = null;                                                                  // win 旗子动画 预制件
    @property(cc.Prefab) specialCardTypePrefab: cc.Prefab = null; // 金刚等牌型动画 预制件
    // @property(cc.Prefab) prefabHbStartBets: cc.Prefab = null;                                                                // 开始下注动画 预制件
    // @property(cc.Prefab) prefabHbEndBets: cc.Prefab = null;                                                                  // 停止下注动画 预制件
    @property(cc.Prefab) prefabSharkWin: cc.Prefab = null;
    @property(cc.Prefab) prefabSharkLose: cc.Prefab = null;
    @property(cc.Prefab) prefabDashiWin: cc.Prefab = null;
    @property(cc.Prefab) prefabDashiLose: cc.Prefab = null;
    // @property(cc.Prefab) prefabHbWayOut: cc.Prefab = null;                                                                   // 路单闪光动画 预制件

    // @property(cc.Prefab) prefabHbFlutterScore: cc.Prefab = null;                                                              // 飘分 预制件
    // @property(cc.Prefab) prefabHbBetCoin: cc.Prefab = null;                                                                   // 下注金币 预制件
    // @property(cc.Prefab) prefabHbToast: cc.Prefab = null;                                                                     // 游戏提示 预制件
    // @property(cc.Prefab) prefabHbGuid: cc.Prefab = null;                                                                      // 新手引导 预制件
    // @property(cc.Prefab) prefabHbMenu: cc.Prefab = null;                                                                      // 游戏菜单 预制件
    // @property(cc.Prefab) prefabHbAdvancedSetting: cc.Prefab = null;                                                           // 高级设置 预制件
    // @property(cc.Prefab) prefabHbAdvancedAuto: cc.Prefab = null;                                                              // 高级续投 预制件
    // @property(cc.Prefab) prefabHbAddAdvancedAuto: cc.Prefab = null;                                                           // mini-game advance auto
    // @property(cc.Prefab) prefabHbDialog: cc.Prefab = null;                                                                    // 对话框 预制件

    // @property(cc.Prefab) prefabHbPlayerList: cc.Prefab = null;                                                                // 玩家列表 预制件
    // @property(cc.Prefab) prefabHbRewardTips: cc.Prefab = null;                                                                // 通用奖励提示 预制件
    // @property(cc.Prefab) prefabLuckButton: cc.Prefab = null;                                                                   // 红包节 预制件

    // @property(cc.SpriteAtlas) specialCardTypePlist: cc.SpriteAtlas = null;
    // @property(cc.SpriteAtlas) enAnimationPlist: cc.SpriteAtlas = null;
    // @property(cc.Prefab) pointsAniPrefab: cc.Prefab = null;
    // @property(cc.Prefab) popSilencePre: cc.Prefab = null;   // 冷静预制件
    // @property(cc.Prefab) consumingNotifyPrefab: cc.Prefab = null;
    @property(cc.Node) consumingNotifyHolder: cc.Node = null;

    // 從humanboy抄來的
    // @property(cc.Prefab) prefabAvatar: cc.Prefab = null;
    // @property(cc.Prefab) prefabCard: cc.Prefab = null;
    // @property(cc.SpriteFrame) cardbackSpriteFrame: cc.SpriteFrame = null;

    @property(cc.Node) nbFlagLeft: cc.Node = null;
    @property(cc.Node) nbFlagRight: cc.Node = null;
    @property(cc.Node) ringTip: cc.Node = null;

    // private consumingNotify: ConsumingPrompt = null
    pointsNode: cc.Node = null;

    static gFullScreenOffset: cc.Vec2 = cc.Vec2.ZERO; // 全面屏偏移量
    static gClassName: string = 'PokerMasterControl'; // 类名

    private _msInterval: number = 1; // 定时器间隔(单位: 秒)
    private _msNowTime: number = 0; // 当前时间
    private _msLastTime: number = 0; // 上次时间
    private _nLeftTime: number = 0; // 剩余时间
    private _panelGame: cc.Node = null; // 游戏面板
    private _panelCard: cc.Node = null; // 牌面板
    private _panelTop: cc.Node = null; // 顶栏面板
    private _panelBottom: cc.Node = null; // 底栏面板
    protected _panelSelf: cc.Node = null; // 玩家信息面板
    private _panelBetBtn: cc.Node = null; // 下注按钮面板
    private _vBottomBetBtns: MiniGameCommonDef.GameNodeScale[] = []; // 底部下注按钮数组, 用于适配位置(k - 节点, v - 原缩放比例)

    protected _panelLeftPlayerlist: cc.Node = null;
    protected _panelRightPlayerlist: cc.Node = null;

    private _vTopWayOutImg: cc.Sprite[] = []; // "顶栏"路单精灵
    private _vTopWayOutImgSrcPos: cc.Vec2[] = []; // "顶栏"路单精灵 原始位置数组

    private _vLeftHandCards: PokerCardControl[] = []; // 左手牌
    private _vRightHandCards: PokerCardControl[] = []; // 右手牌
    private _vPublicHoleCards: PokerCardControl[] = []; // 公共牌

    private _vLeftHandCardsSrcPos: cc.Vec2[] = []; // 左手牌 原始位置
    private _vRightHandCardsSrcPos: cc.Vec2[] = []; // 右手牌 原始位置
    private _vPublicHoleCardsSrcPos: cc.Vec2[] = []; // 公共牌 原始位置

    private _imgLeftCardType: cc.Sprite = null; // 左边牌型
    private _imgRightCardType: cc.Sprite = null; // 右边牌型
    private _imgLeftCardTypeBg: cc.Sprite = null; // 左边牌型背景
    private _imgRightCardTypeBg: cc.Sprite = null; // 右边牌型背景

    private _squintCard: PokerMasterSquintCardControl = null; // 眯牌牌信息

    protected _txtSelfName: cc.Label = null; // 个人昵称
    protected _txtSelfGold: cc.Label = null; // 个人金币
    protected _imgSelfGold: cc.Sprite = null; // 个人金币精灵(用于飞金币的起始位置)
    protected _imgSelfHead: cc.Sprite = null; // 个人头像

    private _imgBetClock: cc.Node = null; // 下注计时器精灵
    private _imgBetClockSrcPos: cc.Vec2 = cc.Vec2.ZERO; // 下注计时器原始位置

    private _imgCountDown: cc.Node = null; // 等待下一局倒计时精灵
    private _imgCountDownSrcPos: cc.Vec2 = cc.Vec2.ZERO; // 等待下一局倒计时精灵 原始位置

    private _btnReview: cc.Node = null; // 牌局路单记录按钮
    private _btnPlayerList: cc.Node = null; // 玩家列表按钮
    private _btnBetAuto: cc.Button = null; // 续投按钮
    private _btnBetClean: cc.Button = null; // 清屏按钮(清理下注区域金币)
    private _btnRedpacketFestival: cc.Node = null; // 红包节按钮
    private _btnRedpacketFestivalLayer: cc.Node = null; // 红包节按钮提示层
    private _luckButton: LuckTurntableButtonControl = null; // 红包节实例

    protected _vAreasInfo: PokerMasterAreaInfo[] = []; // 当前下注区域
    protected _vOtherPlayerInfo: HumanboyPlayerInfo[] = []; // 其他玩家列表
    private _vBetButtons: HumanboyBetCoinControl[] = []; // 下注按钮数组
    private _mapSounds: Map<string, boolean> = new Map(); // 声音容器(名称 - id)

    private _nodeAnim: cc.Node = null; // 动态动画根节点
    private _nodeCoinPool: cc.Node = null; // 动态金币池节点
    private _llCoinPoolZOrderCount: number = 0; // 动态金币池节点深度计数
    private _mapCoinQueue: Map<network.BetZoneOption, Deque<HumanboyBetCoinControl>> = new Map(); // 金币区域(从区域结构中剥离, 因为不同桌布都公用一套金币队列)
    private _vCoinOptimizationDeque: Deque<HumanboyCoinOptimization> = new Deque(); // 金币最优队列

    private _mapAnimWinFlags: Map<network.BetZoneOption, cc.Animation> = new Map(); // win 动画容器
    private _mapAnimShowOdds: Map<network.BetZoneOption, cc.Animation> = new Map(); // 赔率 动画容器
    private _animRoundStart: cc.Animation = null; // 开局动画
    private _animStartBet: cc.Animation = null; // 开始下注动画
    private _animStopBet: cc.Animation = null; // 停止下注动画
    private _animWayoutLight: cc.Animation = null; // 路单闪光动画

    private _animDashiWin: cc.Animation = null;
    private _animDashiLose: cc.Animation = null;
    private _animSharkWin: cc.Animation = null;
    private _animSharkLose: cc.Animation = null;
    private _nBetBtnNum: Readonly<number> = 5; // 下注按钮数量
    // private _fBetBtnSrcScaleRate: Readonly<number> = 0.75; // 下注筹码原始缩放比例
    // private _fBetBtnTarScaleRate: Readonly<number> = 1.0; // 下注筹码目标缩放比例
    // private _fFlyCoinScaleRate: Readonly<number> = 0.5; // 创建的金币缩放比例
    @property({
        type: cc.Float,
        tooltip: '下注筹码原始缩放比例'
    })
    fBetBtnSrcScaleRate: number = 0.75;
    @property({
        type: cc.Float,
        tooltip: '下注筹码目标缩放比例'
    })
    fBetBtnTarScaleRate: number = 1.0;
    @property({
        type: cc.Float,
        tooltip: '创建的金币缩放比例'
    })
    fFlyCoinScaleRate: number = 0.5;
    @property({
        tooltip: '調整Flag位置'
    })
    bFlagReposition: boolean = true;

    private _nCurBetBtnIndex: number = -1; // 当前下注按钮索引
    private _nAreaCoinLimitCountMin: number = 100; // 单个区域金币精灵上限最小数量
    private _nAreaCoinLimitCountMax: Readonly<number> = 200; // 单个区域金币精灵上限最大数量

    private _bWaitting: boolean = false; // 是否正在等待开局倒计时
    private _nWaittingTime: number = 0; // 等待时间

    private _fActDelayedRoundStart: Readonly<number> = 0; // 开局动画 延时时间
    private _fActExecuteRoundStart: Readonly<number> = 1.3; // 开局动画 执行时间

    private _fActDelayedSendCard: Readonly<number> = 0.0; // 发牌动画 延时时间
    private _fActExecuteSendCard: Readonly<number> = 1.5; // 发牌动画 执行时间

    private _fActDelayedShowHandCard: Readonly<number> = 0.0; // 开手牌动画 延时时间
    private _fActExecuteShowHandCard: Readonly<number> = 1.2; // 开手牌动画 执行时间

    private _fActDelayedShowTurnCard: Readonly<number> = 0.0; // 开turn牌动画 延时时间
    private _fActExecuteShowTurnCard: Readonly<number> = 0.5; // 开turn牌动画 执行时间

    private _fActDelayedShowRiverCard: Readonly<number> = 0.5; // 开river牌动画 延时时间
    private _fActExecuteShowRiverCard: Readonly<number> = 0.5; // 开river牌动画 执行时间

    private _fActDelayedShowOdds: Readonly<number> = 0; // 显示赔率动画 延时时间
    private _fActExecuteShowOdds: Readonly<number> = 1.0; // 显示赔率动画 执行时间

    private _fActDelayedStartBet: Readonly<number> = 0.0; // 开始下注动画 延时时间
    private _fActExecuteStartbet: Readonly<number> = 1.0; // 开始下注动画 执行时间

    private _fActExecuteBetClock: Readonly<number> = 0.5; // 下注闹钟动画 执行时间
    private _fActExecuteTableTrans: Readonly<number> = 0.5; // 桌布翻转动画 执行时间

    private _fActDelayedStopBet: Readonly<number> = 0.5; // 停止下注动画 延时时间
    private _fActExecuteStopBet: Readonly<number> = 1.0; // 停止下注动画 执行时间

    private _fActDelayedSkipInsure: Readonly<number> = 0.5; // 跳过保险投注提示动画 延时时间
    private _fActExecuteSkipInsure: Readonly<number> = 2.0; // 跳过保险投注提示动画 执行时间

    private _fActDelayedSquintCard: Readonly<number> = 0.5; // 眯牌动画 延时时间
    private _fActExecuteSquintCard: Readonly<number> = 6; // 眯牌动画 执行时间

    private _fActDelayedShowCardType: Readonly<number> = 0; // 显示牌型动画 延时时间
    private _fActExecuteShowCardType: Readonly<number> = 0.5; // 显示牌型动画 执行时间

    private _fActDelayedShowWinFlag: Readonly<number> = 0.5; // 显示win动画 延时时间
    private _fActExecuteWinFlag: Readonly<number> = 2.5; // win动画 执行时间
    private _fActDelayedHideWinFlag: Readonly<number> = 1.0; // 隐藏win动画 延时时间

    private _fActExecuteWayOut: Readonly<number> = 1.0; // 显示路子动画 执行时间
    private _fActExecuteWayOutLight: Readonly<number> = 1.2; // 显示路子动画闪光 执行时间

    private _fActDelayedFlyWinCoin: Readonly<number> = 0.5; // win飞金币 延时时间
    private _fActExecuteFlyWinCoin: Readonly<number> = 1.5; // win飞金币 执行时间
    private _fActExecuteFlyWinCoinEnd: Readonly<number> = 2.0; // win飞金币 总时间
    private _miPaiTime: Readonly<number> = 6.0; // 眯牌总时长

    private _specialTypeTime: Readonly<number> = 8.0; // 特殊牌型时间

    private _bTrueFullScreen: boolean = false; // 是否是 iphonex 版的真实的全面屏
    private _bOpenNarrowAdapter: Readonly<boolean> = true; // 窄屏适配开关

    private _eAutoBtnStyle: MiniGameCommonDef.eGameboyAutoBtnStyle =
        MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NONE; // 续投按钮样式

    private _humanboyGuid: cc.Node = null; // 路单引导 实例
    private _humanboyMenu: MiniGameMenuControl = null; // 游戏菜单 实例
    private _humanboyAdvancedSetting: MiniGameAdvancedSettingControl = null; // 高级设置 实例
    private _humanboyAdvancedAuto: HumanboyAdvancedAutoControl = null; // 高级续投 实例

    private _pokerMasterChart: PokerMasterChartControl = null; // 走势图 实例
    private _pokerMasterReview: PokerMasterReviewControl = null; // 投注回顾 实例

    private _humanboyPlayerList: MiniGamePlayerListControl = null; // 玩家列表 实例
    private _humanboyExchange: MiniGameExchangeControl = null;
    private _humanboyRule: MiniGameRuleControl = null; // 规则 实例
    private _humanboySetting: MiniGameAudioSettingControl = null; // 设置 实例
    private _humanboyRewardTips: HumanboyRewardTipsControl = null; // 通用奖励提示 实例

    private _atlasCbLanguage: cc.SpriteAtlas = null; // 牛仔语言图集
    private _atlasHbLanguage: cc.SpriteAtlas = null; // 百人语言图集
    protected _atlasHbHumanboy: cc.SpriteAtlas = null; // 百人其它图集
    private _atlasPmPokermaster: cc.SpriteAtlas = null; // 扑克大师图集

    private flyCoinToPlayerArr: cc.Node[] = [];
    private isIphoneXArea: boolean = false;
    private trendAnim: cc.AnimationClip = null;
    // @property(cc.SpriteAtlas) cowboyTrendAnimPlist: cc.SpriteAtlas = null;
    private isSquintCard: boolean = false;
    private mttbeginMsg: string = '';
    protected ispad: boolean = false;
    private _bSwitchTable: boolean = false;
    private isWayOutAnimPlaying: boolean = false;

    private _pokerMasterRoom: pf.Nullable<domain.PokerMasterRoom> = null;
    private _walletService: pf.services.WalletService = null;
    private _pushNotificationService: pf.services.PushNotificationService = null;
    private _authService: pf.services.AuthService = null;

    private _luckTurntableService: pf.services.LuckTurntableService = null;
    // private _calmDownService: pf.services.CalmDownService = null;
    private _platform: string = '';

    private _isEnterBackground: boolean = false;

    private _boundLuckTurntableStartOrEnd = this._onMsgShowLuckButton.bind(this);
    private _boundLuckTurntableResult = this._onMsgTurntableResultNotice.bind(this);

    private _boundUpdateGoldHandler = this._onMsgUpdateWorldServerGold.bind(this);

    private _boundEnterBackgroundHandler = this.OnAppEnterBackground.bind(this);
    private _boundEnterForegroundHandler = this.OnAppEnterForeground.bind(this);

    private _boundPushNotification = this._onPushNotification.bind(this);

    // private _boundCalmDown = this.onCalmDownShowTip.bind(this);

    protected _selfAvatar: AvatarControl = null;

    private consumingNotify: ConsumingPromptControl = null;

    private _eGameboyScreenType: MiniGameCommonDef.eGameboyScreenType =
        MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW; // 屏幕类型

    private _rebateEventStatus: pf.client.IEventStatusClient = null;

    private _lastSoundTime: number = 0;
    private _lastSoundName: string;
    private _rebateActivity: RebateFloatingButtonControl = null;
    private _rebateService: pf.services.RebateService = null;
    private _boundRebateEventStop = this.hideRebateActivity.bind(this);
    private _boundGetRebateEventStatus = this._getRebateEventStatus.bind(this);
    private _boundRebateClaimNotify = this._onResponseRebateReceiveReward.bind(this);
    private _boundRebateEventStatusNotify = this._onRebateStatusNotify.bind(this);
    private _rebateCoinsFly: cc.Node = null;
    private _boundBarMaxAnimationEnd = this.onBarMaxAnimationEnd.bind(this);

    private _rebateClaimToast: Function = null;

    private _lastTimeGetRebateEventStatus: number = 0;
    private _lastSystemTime: number = 0;
    private _lastTimeClick = 0;

    protected onLoad(): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._pokerMasterRoom = context.room as domain.PokerMasterRoom;

        this._walletService = pf.serviceManager.get(pf.services.WalletService);

        this._pushNotificationService = pf.serviceManager.get(pf.services.PushNotificationService);

        this._authService = pf.serviceManager.get(pf.services.AuthService);

        this._luckTurntableService = pf.serviceManager.get(pf.services.LuckTurntableService);

        // this._calmDownService = pf.serviceManager.get(pf.services.CalmDownService);

        this._platform = context.platform || 'pkw';

        // pf.app.setCurrentScene(macros.SCENE_NANE);
        // pf.system.view.adaptScreenHen (this.node);
        // cv.config.setCurrentScene(cv.Enum.SCENE.POKERMASTER_SCENE);
        // cv.config.adaptScreenHen(this.node);
        // pf.UIUtil.adaptWidget(this.node, true);

        pf.app.setCurrentScene(macros.SCENE_NANE);
        this._rebateService = pf.serviceManager.get(pf.services.RebateService);
    }

    protected start(): void {
        pf.UIUtil.adaptWidget(this.node, true);

        this._init();

        // 进入房间数据同步
        if (this._pokerMasterRoom.isDataSynced) {
            this._onMsgGameDataSyn();
            // this.onCalmDownShowTip(this._pokerMasterRoom.syncCalmDownParams);
        } /* else {
            this._pokerMasterRoom.addListener('dataSync', this._onMsgGameDataSyn.bind(this));
        } */

        // this._pokerMasterRoom.addListener('calmDown', this._boundCalmDown);
        this.initDialogHub();
        if (this._pokerMasterRoom.isDataSynced) {
            this.OnGameDataSynNotify();
        }
    }

    protected onDestroy(): void {
        pf.bundleManager.releaseAll(macros.BUNDLE_NAME);
        this.GetDialogHub().processClose();
    }

    protected update(dt: number): void {
        // 已流逝的时间
        this._msNowTime += dt;

        // 时间差
        let msDuration: number = this._msNowTime - this._msLastTime;

        // 判断调用定时器后的时间（可能调用了几次定时器）是否与调用定时器前的时间相差1s
        if (msDuration >= this._msInterval) {
            // 弥补帧误差
            this._msLastTime = this._msNowTime - (msDuration - this._msInterval);
            --this._nLeftTime;
        }

        this._updateCoinOptimization(dt);
    }

    /**
     * 注册监听事件
     */
    private _addObserver(): void {
        this._pokerMasterRoom.addListener('dataSync', this._onMsgGameDataSyn.bind(this));
        if (this._platform === 'pkw') {
            this._luckTurntableService.addListener('luckTurntableStart', this._boundLuckTurntableStartOrEnd);
            this._luckTurntableService.addListener('luckTurntableEnd', this._boundLuckTurntableStartOrEnd);
            this._luckTurntableService.addListener('luckTurntableResult', this._boundLuckTurntableResult);
        }

        this._walletService.addListener('userGoldNum', this._boundUpdateGoldHandler);
        this._pokerMasterRoom.addListener('kicked', this._onMsgKick.bind(this)); // 服务器踢人

        // TODO: testing
        pf.app.addListener('appEnterBackground', this._boundEnterBackgroundHandler);
        pf.app.addListener('appEnterForeground', this._boundEnterForegroundHandler);

        this._pokerMasterRoom.addListener('serverError', this._onMsgGameError.bind(this)); // 游戏错误提示
        this._pokerMasterRoom.addListener('roomParamChange', this._onMsgRoomParamChange.bind(this)); // 房间状态变更

        this._pokerMasterRoom.addListener('bet', this._onMsgBet.bind(this)); // 下注
        this._pokerMasterRoom.addListener('betCoinOptionsChange', this._onMsgBetAmountLevelChange.bind(this)); // 下注级别变更
        this._pokerMasterRoom.addListener('advanceAutoBetCountSet', this._onMsgAdvanceAutobetSet.bind(this)); // 设置高级续投次数成功
        this._pokerMasterRoom.addListener('advanceAutoBet', this._onMsgAdvanceAutobet.bind(this)); // 高级续投
        this._pokerMasterRoom.addListener('advanceAutoBetCancel', this._onMsgAdvanceAutobetCancel.bind(this)); // 取消高级续投成功
        this._pokerMasterRoom.addListener(
            'advanceAutoBetLimitReached',
            this._onMsgAdvanceAutobetLimitReached.bind(this)
        ); // 高级续投接近或者已达上限
        this._pushNotificationService.addListener('pushNotification', this._boundPushNotification);

        // this._calmDownService.addListener('calmDown', this._boundCalmDown);

        this._pokerMasterRoom.addListener('advanceAutoBetCountAdd', this.onMsgAdvanceAutobetAdd.bind(this));

        this._pokerMasterRoom.addListener('deal', this._onMsgGameStatusDeal.bind(this)); // 新开一局

        this._pokerMasterRoom.addListener('startBet', this._onMsgGameStartBet.bind(this)); // 开始下注
        this._pokerMasterRoom.addListener('stopBet', this._onMsgGameStatusStopBet.bind(this)); // 停止下注
        this._pokerMasterRoom.addListener('readyGame', this._onMsgGameStatusReady.bind(this)); // 清屏准备
        this._pokerMasterRoom.addListener('showOdds', this._onMsgGameStatusShowOdds.bind(this)); // 显示赔率

        this._pokerMasterRoom.addListener('gameRoundEnd', this._onMsgGameStatusRoundEnd.bind(this)); // 一局结束

        this._pokerMasterRoom.addListener('autoBet', this._onMsgMergeAutoBet.bind(this)); // 合并续投动作结束

        this._pokerMasterRoom.addListener('leftGameCoin', this._onMsgConsumingNotify.bind(this));

        this._pokerMasterRoom.addListener('leaveRoom', this.exitGame.bind(this));

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        context.exitCallback = this.tryLeaveRoom.bind(this);

        // ????? 沒看到有發送事件，看humanboy直接拿掉
        // cv.MessageCenter.register(PokerMasterDef.LocalMsg().SWITCH_SCENEB_EGAN, this._onMsgSwitchSceneBegan.bind(this), this.node);                             // 切出该场景

        // let MsgPrefix: string = PokerMasterDef.LocalMsg().MsgPrefix;

        // ????? 沒看到有發送事件，看humanboy cowboy直接拿掉
        // cv.MessageCenter.register("goldViewShop", this.onGoldViewShop.bind(this), this.node);

        // ????? 看不出來怎麼串發送事件，也沒看到pb.CMD裡有相關
        // cv.MessageCenter.register("NoticeMTT_MatchBegin", this.NoticeMttMatchBegin.bind(this), this.node);
        this._rebateService.addListener('eventStatusStop', this._boundRebateEventStop);
        this._rebateService.addListener('refreshEventStatus', this._boundRebateEventStatusNotify);
        this._rebateService.addListener('rebateRewardResult', this._boundRebateClaimNotify);
        this._pokerMasterRoom.addListener('dataSync', this.OnGameDataSynNotify.bind(this));
    }

    /**
     * 移除监听事件
     */
    private _removeObserver(): void {
        if (this._platform === 'pkw') {
            this._luckTurntableService.removeListener('luckTurntableStart', this._boundLuckTurntableStartOrEnd);
            this._luckTurntableService.removeListener('luckTurntableEnd', this._boundLuckTurntableStartOrEnd);
            this._luckTurntableService.removeListener('luckTurntableResult', this._boundLuckTurntableResult);
        }

        this._walletService.removeListener('userGoldNum', this._boundUpdateGoldHandler);

        // TODO: testing
        pf.app.removeListener('appEnterBackground', this._boundEnterBackgroundHandler);
        pf.app.removeListener('appEnterForeground', this._boundEnterForegroundHandler);

        this._pushNotificationService.removeListener('pushNotification', this._boundPushNotification);

        // this._calmDownService.removeListener('calmDown', this._boundCalmDown);

        // ?????
        // cv.MessageCenter.unregister("goldViewShop", this.node);
        // ?????
        // cv.MessageCenter.unregister("NoticeMTT_MatchBegin", this.node);
        this._rebateService.removeListener('eventStatusStop', this._boundRebateEventStop);
        this._rebateService.removeListener('refreshEventStatus', this._boundRebateEventStatusNotify);
        this._rebateService.removeListener('rebateRewardResult', this._boundRebateClaimNotify);
        this._rebateActivity?.node?.off('barMaxAnimationEnd', this._boundBarMaxAnimationEnd);
    }

    /**
     * 重置所有UI
     */
    private _resetAllUI(): void {
        this.clearSpecialCardTypeAnim();
        this.resetResultAni();
        this._resetGameView();
        this._resetOtherView();
        this._updateGameView();
    }

    /**
     * 重置游戏视图
     */
    private _resetGameView(): void {
        this._resetCardPos();
        this._resetLeftTime();
        this._resetCardTypeOrRoleLead();
        this._setAllCardsFace(false);
        this._setAllCardsVisible(false);

        this._resetAllBetAreas();
        this._resetAllBetAreaCoins();
        this._restAllTimelineAnims();

        this._resetSquintCardInfo();

        this._stopTimeBetClock();
        this._stopWaittingNextRound();
        this._stopPrepareNextRound();
        this.resetTempPaixing();
        this.resetFlyCoinToPlayerArr();
        this.hideTrendChangeAnim();

        this.mttbeginMsg = '';

        this.resetPointAni();
    }

    /**
     * 重置其他视图
     */
    private _resetOtherView(): void {}

    /**
     * 更新游戏视图
     */
    private _updateGameView(): void {
        this._updateRoleFortune();
        this._updateBetAmountLevel();
        this._updateBetBtnState();
        this._updateBetAreaTouchEnabled();

        this._updateSelfInfo();
        this._updateOtherPlayersInfo();
        this._updateAllPlayerWinCount();
    }

    /**
     * 初始化
     */
    private _init(): void {
        // 隐藏充值web页面 ????? 看humanboy cowboy這些都被改掉了?
        // cv.SHOP.msgNode.active = false;
        // cv.viewAdaptive.isselfchange = false;
        // cv.viewAdaptive.pokerMasterRoomID = 0;

        // 设置跑马灯类型 ????? 看humanboy cowboy這些都被改掉了?
        // cv.pushNotice.setPushNoticeType(PushNoticeType.PUSH_POKERMASTER);

        // 计算全面屏偏移量
        if (pf.system.view.isFullScreen()) {
            const offset = pf.system.view.iphoneXOffset;
            PokerMasterControl.gFullScreenOffset.x = pf.system.view.isScreenLandscape() ? offset : 0;
            PokerMasterControl.gFullScreenOffset.y = pf.system.view.isScreenLandscape() ? 0 : offset;
        }

        this._initAtlasList();
        this._initUI();
        this._initTopWayOut();
        this._initBtnsEvents();
        this.initGuide();
        this._initPlayersInfo();
        this._initCardInfo();
        this._initBetAreas();
        this._initCoinPool();
        this.initTrendChangeAnim();
        this._initTimelineAnims();
        this._initSquintCardInfo();
        this._initBetButtons();
        // this._initGuid();
        // this._initBtnTest();                                             // 初始化测试按钮

        this._adaptiveScreen(); // 适配刘海屏相关控件
        this._fitSafeArea();
        this._initRedPackage(); // 初始化红包按钮入口
        this._adaptiveBetBtnPanel(); // 适配下注按钮面板布局
        this._adaptBtnMenu();

        this._addObserver(); // 添加监听事件
        this._onMsgSoundSwitch(); // 播放背景音乐

        this._resetAllUI(); // 重置UI
    }

    /**
     * 初始化图集
     */
    private _initAtlasList(): void {
        this._atlasPmPokermaster = pf.addressableAssetManager.getAsset(macros.Assets.POKER_MASTER_ATLAS);
        // this._atlas_hb_humanboy = cv.resMgr.getSpriteAtlas("zh_CN/game/humanboyPlist/humanboy");
        // this._atlas_cb_language = cv.resMgr.getSpriteAtlas(cv.config.getLanguagePath("game/cowboyPlist/language"));
        // this._atlas_hb_language = cv.resMgr.getSpriteAtlas(cv.config.getLanguagePath("game/humanboyPlist/language"));
        this._atlasHbHumanboy = pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_ATLAS);
        this._atlasCbLanguage = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_LANGUAGE_ATLAS);
        this._atlasHbLanguage = pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_LANGUAGE_ATLAS);
    }

    /**
     * 初始化UI
     */
    private _initUI(): void {
        // let panelGame: cc.Node = this.node.getChildByName('panel_game');
        // let panelGameNarrow: cc.Node = this.node.getChildByName('panel_game_x');
        // let panelGameIpad: cc.Node = this.node.getChildByName('panel_game_ipad');
        this._panelLeftPlayerlist = this.node.getChildByName('panel_left_playerlist');
        this._panelRightPlayerlist = this.node.getChildByName('panel_right_playerlist');
        // this._eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL;
        this.ispad = false;
        this._panelTop = this.node.getChildByName('panel_top');
        this._panelBottom = this.node.getChildByName('panel_bottom');
        const canvas = this.getComponent(cc.Canvas);
        // canvas.fitWidth = false;
        const isPad = pf.system.view.height / pf.system.view.width > 0.71;
        const fitScale = isPad
            ? pf.system.view.width / canvas.designResolution.width
            : pf.system.view.height / canvas.designResolution.height;
        do {
            let panelGame: cc.Node = this.node.getChildByName('panel_game');
            let panelGameBroad: cc.Node = this.node.getChildByName('panel_game_ipad');
            let panelGameNarrow: cc.Node = this.node.getChildByName('panel_game_x');
            this._eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL;

            // 窄屏检测

            if (pf.system.view.isNarrowScreen()) {
                // let panelLeftPlayerList: cc.Node = this.node.getChildByName('panel_left_playerlist');
                // let panelRightPlayerList: cc.Node = this.node.getChildByName('panel_right_playerlist');
                // 左右列表宽度 + 桌布宽度 + 两边刘海宽度 + 缓冲宽度
                let totalW = 0;
                let offsetW = 0;
                totalW += 2 * PokerMasterControl.gFullScreenOffset.x;
                totalW += panelGameNarrow.width;
                totalW += this._panelLeftPlayerlist.width - 50;
                totalW += this._panelRightPlayerlist.width - 50;
                totalW += offsetW;
                if (totalW * fitScale <= pf.system.view.width) {
                    this._eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW;
                }
            }
            // 宽屏检测
            else if (pf.system.view.isWideScreen()) {
                // 顶栏面板高度 + 桌布高度 + 底栏面板高度 + 缓冲高度
                let totalH = 0;
                let offsetH = 0;

                totalH += this._panelTop.height;
                totalH += panelGameBroad.height;
                totalH += this._panelBottom.height;
                totalH += offsetH;

                if (isPad) {
                    this._eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD;
                    this.ispad = true;
                }
            }

            switch (this._eGameboyScreenType) {
                case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD:
                    panelGame.removeFromParent(true);
                    pf.UIUtil.destroyNode(panelGame);
                    panelGameNarrow.removeFromParent(true);
                    pf.UIUtil.destroyNode(panelGameNarrow);
                    this._panelGame = panelGameBroad;

                    pf.addressableAssetManager
                        .loadAsset(macros.Dynamic_Assets.POKER_MASTER_TABLE_IPAD_SPRITE)
                        .then((asset: cc.SpriteFrame) => {
                            this._panelGame.getComponent(cc.Sprite).spriteFrame = asset;
                        });
                    break;

                case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW:
                    panelGame.removeFromParent(true);
                    panelGameBroad.removeFromParent(true);
                    pf.UIUtil.destroyNode(panelGame);
                    pf.UIUtil.destroyNode(panelGameBroad);
                    this._panelGame = panelGameNarrow;
                    pf.addressableAssetManager
                        .loadAsset(macros.Dynamic_Assets.POKER_MASTER_TABLE_FULL_SCREEN_SPRITE)
                        .then((asset: cc.SpriteFrame) => {
                            this._panelGame.getComponent(cc.Sprite).spriteFrame = asset;
                        });
                    break;

                case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL:
                default:
                    panelGameBroad.removeFromParent(true);
                    panelGameNarrow.removeFromParent(true);
                    pf.UIUtil.destroyNode(panelGameBroad);
                    pf.UIUtil.destroyNode(panelGameNarrow);
                    this._panelGame = panelGame;
                    pf.addressableAssetManager
                        .loadAsset(macros.Dynamic_Assets.POKER_MASTER_TABLE_NORMAL_SPRITE)
                        .then((asset: cc.SpriteFrame) => {
                            this._panelGame.getComponent(cc.Sprite).spriteFrame = asset;
                        });
                    break;
            }

            this._panelGame.active = true;
        } while (false);

        // 计算目标设备分辨率用哪一套UI(目前一套通用, 一套 iphonex )
        // 这里的窄屏只针对 iphonex 设计, 但部分android分辨率虽满足窄屏 h : w >= 2 : 1 的条件, 但是又略比iphonex的 h : w >= 2.165 : 1 的短,
        // 因此这里要检测处理下是否是 iphonex 版的窄屏了
        // 这里就出现了特殊情况, 若不是标准的窄屏, 游戏主逻辑UI区以 _bTrueFullScreen 为判断条件, 外围的边边框框则以 g_pkViewDataManager.isfullScreen 为判断条件
        // do {
        //     if (pf.system.view.isFullScreen()) {
        //         // 横屏
        //         if (pf.system.view.isScreenLandscape()) {
        //             let fTotalWidth = 0;
        //             fTotalWidth += 2 * PokerMasterControl.gFullScreenOffset.x;
        //             fTotalWidth += panelGameNarrow.width;
        //             fTotalWidth += (panelLeftPlayerlist.width - 50);
        //             fTotalWidth += (panelRightPlayerlist.width - 50);
        //             this._bTrueFullScreen = fTotalWidth <= cc.winSize.width;
        //         }
        //         // 竖屏(暂无逻辑)
        //         else {
        //             // let fTotalHeight: number = 0;
        //             // this._bTrueFullScreen = fTotalHeight <= cc.winSize.height;
        //         }
        //     }
        //     else {
        //         let size = cc.winSize;
        //         if (size.width / size.height <= 1920 / 1439) {
        //             this.ispad = true;
        //         }
        //     }
        // } while (false);
        // if (this._bOpenNarrowAdapter && this._bTrueFullScreen) {
        //     panelGame.removeFromParent(true);
        //     panelGame.destroy();
        //     panelGameIpad.removeFromParent(true);
        //     panelGameIpad.destroy();
        //     this._panelGame = panelGameNarrow;
        //     if (pf.LANGUAGE_GROUPS.zh_CN !== pf.languageManager.currentLanguage) {
        //         // cv.resMgr.setSpriteFrame(this._panelGame, cv.config.getLanguagePath("game/pokermaster/Bg_X"));
        //         // this._panelGame.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset("game/pokermaster/Bg_X");
        //         pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.POKER_MASTER_TABLE_FULL_SCREEN_SPRITE).then((asset: cc.SpriteFrame) => {
        //             this._panelGame.getComponent(cc.Sprite).spriteFrame = asset;
        //         });

        //     }
        //     this.setNodePosByIphoneX();
        //     this.isIphoneXArea = true;
        // }
        // else if (this.ispad) {
        //     panelGameNarrow.removeFromParent(true);
        //     panelGameNarrow.destroy();
        //     panelGame.removeFromParent(true);
        //     panelGame.destroy();
        //     this._panelGame = panelGameIpad;
        //     if (pf.LANGUAGE_GROUPS.zh_CN !== pf.languageManager.currentLanguage) {
        //         // cv.resMgr.setSpriteFrame(this._panelGame, cv.config.getLanguagePath("game/pokermaster/table_ipad"));
        //         // this._panelGame.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset("game/pokermaster/table_ipad");
        //         pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.POKER_MASTER_TABLE_IPAD_SPRITE).then((asset: cc.SpriteFrame) => {
        //             this._panelGame.getComponent(cc.Sprite).spriteFrame = asset;
        //         });
        //     }
        // }
        // else {
        //     panelGameNarrow.removeFromParent(true);
        //     panelGameNarrow.destroy();
        //     panelGameIpad.removeFromParent(true);
        //     panelGameIpad.destroy();
        //     this._panelGame = panelGame;
        //     if (pf.LANGUAGE_GROUPS.zh_CN !== pf.languageManager.currentLanguage) {
        //         // cv.resMgr.setSpriteFrame(this._panelGame, cv.config.getLanguagePath("game/pokermaster/table_normal"));
        //         // this._panelGame.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset("game/pokermaster/table_normal");
        //         pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.POKER_MASTER_TABLE_NORMAL_SPRITE).then((asset: cc.SpriteFrame) => {
        //             this._panelGame.getComponent(cc.Sprite).spriteFrame = asset;
        //         });
        //     }
        // }

        let lab0 = this._panelGame.getChildByName('lab_0');
        let lab1 = this._panelGame.getChildByName('lab_1');
        lab0.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_tips_refund_money_txt');
        lab1.getComponent(cc.Label).string = pf.languageManager.getString('PokerMaster_tips_refund_money_txt');

        // this._panelGame.active = true;
        // this._panelTop = this.node.getChildByName('panel_top');
        this._panelCard = this.node.getChildByName('panel_card');
        // this._panelBottom = this.node.getChildByName('panel_bottom');

        this._panelSelf = this._panelBottom.getChildByName('panel_self');
        this._panelBetBtn = this._panelBottom.getChildByName('panel_bet_btn');

        // 等待下局倒计时
        this._imgCountDown = this.node.getChildByName('img_count_down');
        this._imgCountDown.zIndex = PokerMasterDef.LayerZorder.Z_IDX_PANEL_COUNT_DOWN;
        this._imgCountDown.active = false;

        // 闹钟
        this._imgBetClock = this.node.getChildByName('img_bet_clock');
        this._imgBetClock.active = false;

        let tempPaixing = this.node.getChildByName('temp_paixing');

        if (this.ispad) {
            let EX_NUM = 174 + 11;
            this._panelGame.setPosition(0, -50);
            this._panelCard.setPosition(this._panelCard.x, 536);
            this._imgBetClock.setPosition(this._imgBetClock.x, 505);
            tempPaixing.setPosition(tempPaixing.x, tempPaixing.y + EX_NUM);

            let btnMenu: cc.Node = this.node.getChildByName('btn_menu');
            // btnMenu.getComponent(cc.Widget).top = 68;
            let btnReview = this.node.getChildByName('btn_review');
            // btnReview.getComponent(cc.Widget).top = 68;
            pf.UIUtil.adaptWidget(btnMenu);
            pf.UIUtil.adaptWidget(btnReview);
        }

        // this.setLeftAndRightList();

        this._imgCountDownSrcPos = cc.v2(this._imgCountDown.position);
        this._imgBetClockSrcPos = cc.v2(this._imgBetClock.position);

        if (this.nbFlagLeft !== null) {
            pf.addressableAssetManager
                .loadAsset(macros.Dynamic_Assets.POKER_MASTER_NB_FLAG_RICH_MAN)
                .then((asset: cc.SpriteFrame) => {
                    this.nbFlagLeft.getComponent(cc.Sprite).spriteFrame = asset;
                });
        }

        if (this.nbFlagRight !== null) {
            pf.addressableAssetManager
                .loadAsset(macros.Dynamic_Assets.POKER_MASTER_NB_FLAG_SKILL)
                .then((asset: cc.SpriteFrame) => {
                    this.nbFlagRight.getComponent(cc.Sprite).spriteFrame = asset;
                });
        }

        if (this.ringTip !== null) {
            pf.addressableAssetManager
                .loadAsset(macros.Dynamic_Assets.POKER_MASTER_RING_TIP)
                .then((asset: cc.SpriteFrame) => {
                    this.ringTip.getComponent(cc.Sprite).spriteFrame = asset;
                });
        }
    }

    /**
     * 初始化"顶栏"路单
     */
    private _initTopWayOut(): void {
        let panelRecord: cc.Node = this._panelTop.getChildByName('panel_record');
        for (let i = 0; i < panelRecord.childrenCount; ++i) {
            let node: cc.Node = panelRecord.getChildByName(`img_dot_${i}`);
            if (node) {
                this._vTopWayOutImg.push(node.getComponent(cc.Sprite));
                this._vTopWayOutImgSrcPos.push(cc.v2(node.position));
            }
        }
    }

    /**
     * 初始化按钮事件
     */
    private _initBtnsEvents(): void {
        // 菜单按钮
        do {
            let btnMenu: cc.Node = this.node.getChildByName('btn_menu');
            // btn_menu.zIndex = PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING;
            btnMenu.on(
                'click',
                (event: cc.Event): void => {
                    if (this.isSquintCard) return;
                    if (!this._humanboyMenu) {
                        if (this._platform === 'pkw') {
                            this._humanboyMenu = cc
                                .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_MENU))
                                .getComponent(MiniGameMenuControl);
                        } else if (this._platform === 'wpk') {
                            this._humanboyMenu = cc
                                .instantiate(
                                    pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_MENU_WITHOUT_EXCHANGE)
                                )
                                .getComponent(MiniGameMenuControl);
                        }
                        this.node.addChild(this._humanboyMenu.node, PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING);

                        // 菜单 - 兑换
                        this._humanboyMenu.getBtnExchange().node.on('click', (event: cc.Event): void => {
                            this._playSoundEffect(macros.Audio.Button);
                            this._humanboyMenu.hide(false);

                            if (this._walletService.getWallet().usdt <= 0) {
                                cr.commonResourceAgent.toastMessage.showMsg(
                                    pf.languageManager.getString('USDTView_ex_coin_error_0_usdt')
                                );
                                return;
                            }
                            if (!this._humanboyExchange) {
                                // this._humanboyExchange = cc
                                //     .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_EXCHANGE))
                                //     .getComponent(MiniGameExchangeControl);
                                pf.addressableAssetManager
                                    .loadAsset(macros.Dynamic_Assets.MINI_GAME_EXCHANGE)
                                    .then((asset: cc.Prefab) => {
                                        this._humanboyExchange = cc
                                            .instantiate(asset)
                                            .getComponent(MiniGameExchangeControl);
                                        this.node.addChild(
                                            this._humanboyExchange.node,
                                            PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING
                                        );
                                    });
                            } else {
                                this._humanboyExchange.openView();
                            }
                        });

                        // 菜单 - 规则
                        this._humanboyMenu.getBtnRule().node.on(
                            'click',
                            (event: cc.Event): void => {
                                this._playSoundEffect(macros.Audio.Button);
                                this._humanboyMenu.hide(false);

                                if (!this._humanboyRule) {
                                    // this._humanboyRule = cc
                                    //     .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_RULE))
                                    //     .getComponent(MiniGameRuleControl);
                                    pf.addressableAssetManager
                                        .loadAsset(macros.Dynamic_Assets.MINI_GAME_RULE)
                                        .then((asset: cc.Prefab) => {
                                            this._humanboyRule = cc
                                                .instantiate(asset)
                                                .getComponent(MiniGameRuleControl);
                                            this.node.addChild(
                                                this._humanboyRule.node,
                                                PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING
                                            );
                                            this._humanboyRule.openView(macros.RULE_URL);
                                        });
                                } else {
                                    this._humanboyRule.openView(macros.RULE_URL);
                                }
                            },
                            this
                        );

                        // 菜单 - 音效设置
                        this._humanboyMenu.getBtnSoundSetting().node.on('click', (event: cc.Event): void => {
                            this._playSoundEffect(macros.Audio.Button);
                            this._humanboyMenu.hide(false);

                            if (!this._humanboySetting) {
                                // this._humanboySetting = cc
                                //     .instantiate(
                                //         pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_AUDIO_SETTING)
                                //     )
                                //     .getComponent(MiniGameAudioSettingControl);
                                pf.addressableAssetManager
                                    .loadAsset(macros.Dynamic_Assets.MINI_GAME_AUDIO_SETTING)
                                    .then((asset: cc.Prefab) => {
                                        this._humanboySetting = cc
                                            .instantiate(asset)
                                            .getComponent(MiniGameAudioSettingControl);
                                        this.node.addChild(
                                            this._humanboySetting.node,
                                            PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING
                                        );
                                    });
                            } else {
                                this._humanboySetting.initSwitch();
                                this._humanboySetting.node.active = true;
                            }
                        });

                        // 菜单 - 高级设置
                        this._humanboyMenu.getBtnAdvancedSetting().node.on('click', (event: cc.Event): void => {
                            this._playSoundEffect(macros.Audio.Button);
                            this._humanboyMenu.hide(false);

                            if (!this._humanboyAdvancedSetting) {
                                // this._humanboyAdvancedSetting = cc
                                //     .instantiate(
                                //         pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_ADVANCED_SETTING)
                                //     )
                                //     .getComponent(MiniGameAdvancedSettingControl);
                                pf.addressableAssetManager
                                    .loadAsset(macros.Dynamic_Assets.MINI_GAME_ADVANCED_SETTING)
                                    .then((asset: cc.Prefab) => {
                                        this._humanboyAdvancedSetting = cc
                                            .instantiate(asset)
                                            .getComponent(MiniGameAdvancedSettingControl);
                                        this.node.addChild(
                                            this._humanboyAdvancedSetting.node,
                                            PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING
                                        );
                                        this._humanboyAdvancedSetting.show();
                                    });
                            } else this._humanboyAdvancedSetting.show();
                        });

                        // 菜单 - 退出
                        this._humanboyMenu.getBtnExit().node.on('click', (event: cc.Event): void => {
                            this._playSoundEffect(macros.Audio.Button);
                            this._humanboyMenu.hide(false);

                            let iUsedAutoBetCount: number = this._pokerMasterRoom.betSettings.usedAutoBetCount;
                            let iSelectAutoBetCount: number = this._pokerMasterRoom.betSettings.selectAutoBetCount;
                            if (iSelectAutoBetCount > 0) {
                                let dialogNode = cc.instantiate(
                                    pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_DIALOG)
                                );
                                dialogNode.name = 'PokerMaster_Dialog';
                                const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                                this.node.addChild(dialogNode, PokerMasterDef.LayerZorder.Z_IDX_PANEL_SERVER_TOAST);

                                const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                                const stringContent = pf.StringUtil.formatC(
                                    pf.languageManager.getString('Cowboy_auto_bet_exit_tips'),
                                    iUsedAutoBetCount,
                                    iSelectAutoBetCount
                                );
                                const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_exit_game');
                                const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_resume_game');
                                const cbLeftBtn = (dialog: IMiniGameDialog) => {
                                    // this.exitGame();
                                    this.tryLeaveRoom();
                                };
                                const cbRightBtn = (dialog: IMiniGameDialog) => {
                                    miniGameDialog?.close();
                                };
                                const stringCenter = pf.languageManager.getString('MiniGame_AddAutoBet_Text');
                                const cbCenterBtn = (dialog: MiniGameDialog) => {
                                    this.showAutoAddBetList(dialog);
                                };

                                const _onUpdateContent = (dialog: IMiniGameDialog) => {
                                    if (legacyDialog) {
                                        legacyDialog.txt_content.string = pf.StringUtil.calculateAutoWrapString(
                                            legacyDialog.txt_content.node,
                                            pf.StringUtil.formatC(
                                                pf.languageManager.getString('Cowboy_auto_bet_exit_tips'),
                                                this._pokerMasterRoom.betSettings.usedAutoBetCount,
                                                this._pokerMasterRoom.betSettings.selectAutoBetCount
                                            )
                                        );
                                    }
                                    if (this._pokerMasterRoom.betSettings.reachLimitBet) {
                                        miniGameDialog?.blockCenterButton();
                                    }
                                };
                                const miniGameDialogConfig: IMiniGameDialogConfig = {
                                    miniDialog: miniGameDialog,
                                    stringContent,
                                    stringLeftBtn,
                                    stringRightBtn,
                                    cbLeftBtn,
                                    cbRightBtn,
                                    isReachedMax: this._pokerMasterRoom.betSettings.reachLimitBet,
                                    legacyDialog,
                                    isShowBtnCenter: true,
                                    stringCenterButton: stringCenter,
                                    cbCenterBtn,
                                    onUpdateContent: _onUpdateContent
                                };

                                ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                            } else {
                                let PokerMasterNodeExit = this.node.getChildByName('PokerMaster_nodeExit');
                                if (PokerMasterNodeExit) {
                                    PokerMasterNodeExit.active = true;
                                } else {
                                    // const nodeExit: cc.Node = cc.instantiate(
                                    //     pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_EXIT)
                                    // );
                                    pf.addressableAssetManager
                                        .loadAsset(macros.Dynamic_Assets.MINI_GAME_EXIT)
                                        .then((asset: cc.Prefab) => {
                                            const nodeExit: cc.Node = cc.instantiate(asset);
                                            nodeExit.name = 'PokerMaster_nodeExit';
                                            this.node.addChild(
                                                nodeExit,
                                                PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING
                                            );
                                        });
                                }
                            }
                        });
                    }
                    pf.audioManager.playSoundEffect(macros.Audio.Button);
                    this._humanboyMenu.show(true);
                    this._humanboyMenu.setMenuPosition(
                        cc.v2(btnMenu.getPosition().x, btnMenu.getPosition().y - btnMenu.getContentSize().height / 2)
                    );
                },
                this
            );
        } while (0);

        // 玩家列表按钮
        do {
            this._btnPlayerList = this._panelBottom.getChildByName('btn_playerlist');
            this._btnPlayerList.zIndex = PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING;
            this._btnPlayerList.on('click', (event: cc.Event): void => {
                if (this.isSquintCard) return;
                this._playSoundEffect(macros.Audio.Button);
                this._pokerMasterRoom.getPlayerList().then((resp) => {
                    this._onMsgPlayerList(resp.players, resp.playerNum);
                });
                // this._playSoundEffect(macros.Audio.Button);
                // cv.pokerMasterNet.requestPlayerList();
            });
        } while (0);

        // 投注回顾记录按钮
        do {
            this._btnReview = this.node.getChildByName('btn_review');
            // this._btn_review.zIndex = PokerMasterDef.LayerZorder.Z_IDX_PANEL_SETTING;
            this._btnReview.on('click', (event: cc.Event): void => {
                if (this.isSquintCard) return;
                this._playSoundEffect(macros.Audio.Button);
                this._showReview();
            });
        } while (0);

        // 顶栏路单
        do {
            this._panelTop.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
                if (this.isSquintCard) return;
                this._playSoundEffect(macros.Audio.Button);
                this._showChart();
            });
        } while (false);
    }

    private advanceAutoAddBet: cc.Node = null;
    private showAutoAddBetList(dialog: MiniGameDialog) {
        if (!this.advanceAutoAddBet) {
            this.advanceAutoAddBet = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_ADVANCED_AUTO)
            );
            this.node.addChild(this.advanceAutoAddBet, PokerMasterDef.LayerZorder.Z_IDX_ADVANCE_AUTO_ADD_SELECT);
        }
        const miniGameAdvanceAuto = this.advanceAutoAddBet.getComponent(MiniGameAdvancedAuto);
        const advanceAuto = new ConcreteAdvancedAuto(miniGameAdvanceAuto);
        advanceAuto.adaptSelectPanelPos(dialog.btn_center.node);
        advanceAuto.showSelectPanel(true);
        advanceAuto.setCountUpdateCallback(() => {
            dialog.updateCenterButton();
        });
    }

    onGoldViewShop() {
        this._playSoundEffect(macros.Audio.Button);
        // cv.MessageCenter.send(PokerMasterDef.LocalMsg().MsgPrefix + PokerMasterDef.LocalMsg().RECHARGE);
        this.recharge();
    }
    /**
     * 初始化玩家列表信息
     */
    protected _initPlayersInfo(): void {
        // 自己
        do {
            this._txtSelfName = this._panelSelf.getChildByName('txt_name').getComponent(cc.Label);
            this._txtSelfGold = this._panelSelf.getChildByName('txt_gold').getComponent(cc.Label);
            this._imgSelfGold = this._panelSelf.getChildByName('img_gold').getComponent(cc.Sprite);
            this._imgSelfHead = this._panelSelf.getChildByName('img_head').getComponent(cc.Sprite);
            this._selfAvatar = this._panelSelf.getChildByName('Avatar').getComponent(AvatarControl);

            // 设置默认头像框
            // cv.resMgr.setSpriteFrame(this._img_self_head.node, 'zh_CN/game/humanboy/head/head_player_box_circle');

            // 充值
            let btnRecharge: cc.Node = this._panelSelf.getChildByName('btn_recharge');
            btnRecharge.on('click', (event: cc.Event): void => {
                this._playSoundEffect(macros.Audio.Button);
                // cv.MessageCenter.send(PokerMasterDef.LocalMsg().MsgPrefix + PokerMasterDef.LocalMsg().RECHARGE);
                this.recharge();
            });
        } while (false);
        this.setLeftAndRightList();
        // 其他玩家
        do {
            // let panelLeftPlayerlist: cc.Node = this.node.getChildByName('panel_left_playerlist');
            // let panelRightPlayerlist: cc.Node = this.node.getChildByName('panel_right_playerlist');
            let listLen: number = this.ispad ? 5 : 4;
            for (let i = 0; i < listLen; ++i) {
                // 左列表(富豪榜)
                do {
                    let player: HumanboyPlayerInfo = new HumanboyPlayerInfo();
                    player.imgBg = this._panelLeftPlayerlist
                        .getChildByName(pf.StringUtil.formatC('img_bg_%d', i))
                        .getComponent(cc.Sprite);
                    player.nodeHead = this._panelLeftPlayerlist.getChildByName(
                        pf.StringUtil.formatC('node_head_%d', i)
                    );
                    player.txtCoin = this._panelLeftPlayerlist
                        .getChildByName(pf.StringUtil.formatC('text_coin_%d', i))
                        .getComponent(cc.Label);
                    const avatar = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.AVATAR));
                    player.nodeHead.addChild(avatar);
                    player.avatarControl = avatar.getComponent(AvatarControl);

                    if (i === 0) {
                        player.imgFlag = this._panelLeftPlayerlist.getChildByName('nb_flag').getComponent(cc.Sprite);
                        let nbFlagDesc: cc.Label = player.imgFlag.node
                            .getChildByName('nb_flag_desc')
                            .getComponent(cc.Label);
                        nbFlagDesc.string = pf.StringUtil.formatC(
                            pf.languageManager.getString('Cowboy_fuhao_no_text'),
                            1
                        );
                    }

                    if (this.ispad) {
                        if (i === listLen - 1) {
                            player.imgBg.node.active = true;
                            player.nodeHead.active = true;
                            player.txtCoin.node.active = true;
                        }
                    }

                    this._vOtherPlayerInfo.push(player);
                } while (false);

                // 右列表(神算子)
                do {
                    let player: HumanboyPlayerInfo = new HumanboyPlayerInfo();
                    player.imgBg = this._panelRightPlayerlist
                        .getChildByName(pf.StringUtil.formatC('img_bg_%d', i))
                        .getComponent(cc.Sprite);
                    player.nodeHead = this._panelRightPlayerlist.getChildByName(
                        pf.StringUtil.formatC('node_head_%d', i)
                    );
                    player.txtCoin = this._panelRightPlayerlist
                        .getChildByName(pf.StringUtil.formatC('text_coin_%d', i))
                        .getComponent(cc.Label);
                    const avatar = cc.instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.AVATAR));
                    player.nodeHead.addChild(avatar);
                    player.avatarControl = avatar.getComponent(AvatarControl);

                    if (i === 0) {
                        player.imgFlag = this._panelRightPlayerlist.getChildByName('nb_flag').getComponent(cc.Sprite);
                        let nbFlagDesc: cc.Label = player.imgFlag.node
                            .getChildByName('nb_flag_desc')
                            .getComponent(cc.Label);
                        nbFlagDesc.string = pf.languageManager.getString('Cowboy_shensuanzi_text');
                    }

                    if (this.ispad) {
                        if (i === listLen - 1) {
                            player.imgBg.node.active = true;
                            player.nodeHead.active = true;
                            player.txtCoin.node.active = true;
                        }
                    }

                    this._vOtherPlayerInfo.push(player);
                } while (false);
            }

            // 头像
            for (let playerInfo of this._vOtherPlayerInfo) {
                playerInfo.imgBg.spriteFrame = this._atlasHbHumanboy.getSpriteFrame('humanboy_icon_seat_bg_1');
                playerInfo.nodeHead.getChildByName('img').getComponent(cc.Sprite).spriteFrame = null;
                playerInfo.txtCoin.node.zIndex = PokerMasterDef.LayerZorder.Z_IDX_IMG_HEAD_TXT;
                if (playerInfo.imgFlag) playerInfo.imgFlag.node.zIndex = PokerMasterDef.LayerZorder.Z_IDX_IMG_HEAD_FLAG;
            }
        } while (false);
    }

    /**
     * 更新个人信息
     */
    private _updateSelfInfo(): void {
        // 昵称
        pf.StringUtil.setShrinkString(this._txtSelfName.node, this._pokerMasterRoom.selfPlayer.name, true);

        // 金币
        let llCurCoin: number = this._pokerMasterRoom.selfPlayer.curCoin;
        this._txtSelfGold.string = this._pokerMasterRoom.transGoldShortString(llCurCoin);

        // 头像
        this._selfAvatar.loadHeadImage(this._pokerMasterRoom.selfPlayer.head, this._pokerMasterRoom.selfPlayer.plat);
        this._pokerMasterRoom.setPlayerBeforeSettlementGold(this._authService.currentUser.userId, llCurCoin);

        // if (this._platform === 'wpk') {
        //     const walletService = pf.serviceManager.get(pf.services.WalletService);
        //     const wallet = walletService.getWallet();
        //     wallet.totalAmount = llCurCoin;
        // }
        // let headUrl: string = this._pokerMasterRoom.selfPlayer.head;
        // CircleSprite.setCircleSprite(this._imgSelfHead.node, headUrl);
    }

    /**
     * 更新其他人信息
     */
    private _updateOtherPlayersInfo(): void {
        // 这里按照服务器发的gamePlayers顺序放
        for (let i = 0; i < this._vOtherPlayerInfo.length; ++i) {
            let vOtherPlayerInfo: Readonly<pf.services.GamePlayer[]> = this._pokerMasterRoom.otherPlayers;
            if (i < vOtherPlayerInfo.length) {
                let info: Readonly<pf.services.GamePlayer> = vOtherPlayerInfo[i];
                this._vOtherPlayerInfo[i].uid = info.uid;

                // 头像更新
                const headUrl: string = cr.CommonUtil.getHeadPath(info, this._authService.currentUser.userId);
                this._vOtherPlayerInfo[i].imgBg.getComponent(cc.Sprite).spriteFrame =
                    this._atlasHbHumanboy.getSpriteFrame('humanboy_icon_seat_bg_1');
                this._vOtherPlayerInfo[i].nodeHead.active = true;
                this._vOtherPlayerInfo[i].avatarControl.loadHeadImage(headUrl, info.plat);
                // CircleSprite.setCircleSprite(this._vOtherPlayerInfo[i].nodeHead.getChildByName('img'), headUrl, info.plat, true, Head_Mode.IRREGULAR, true, true, false);
                this._vOtherPlayerInfo[i].txtCoin.string = this._pokerMasterRoom.transGoldShortString(info.curCoin);
                if (this._vOtherPlayerInfo[i].imgFlag) this._vOtherPlayerInfo[i].imgFlag.node.active = true;
            } else {
                this._vOtherPlayerInfo[i].uid = 0;
                this._vOtherPlayerInfo[i].imgBg.getComponent(cc.Sprite).spriteFrame =
                    this._atlasHbHumanboy.getSpriteFrame('humanboy_icon_seat_bg_2');
                this._vOtherPlayerInfo[i].nodeHead.active = false;
                this._vOtherPlayerInfo[i].txtCoin.string = '';
                if (this._vOtherPlayerInfo[i].imgFlag) this._vOtherPlayerInfo[i].imgFlag.node.active = false;

                // 移除连胜节点
                let strTag: string = 'win_player_win_count_' + this._vOtherPlayerInfo[i].nodeHead.uuid;
                let strNode: cc.Node = cc.find(strTag, this.node);
                if (strNode && cc.isValid(strNode, true)) {
                    this.node.removeChild(strNode);
                    strNode.destroy();
                }
            }
        }
    }

    /**
     * 更新玩家金币信息(同步服务器最新数据)
     * @param uid
     */
    private _updatePlayerGold(uid: number): void {
        if (uid === this._authService.currentUser.userId) {
            let llCurCoin: number = this._pokerMasterRoom.selfPlayer.curCoin;
            this._txtSelfGold.string = this._pokerMasterRoom.transGoldShortString(llCurCoin);
        }

        for (let playerInfo of this._vOtherPlayerInfo) {
            if (playerInfo.uid === uid) {
                // 神算子/富豪是自己的情況
                if (uid === this._authService.currentUser.userId) {
                    playerInfo.txtCoin.string = this._txtSelfGold.string;
                } else {
                    const player = this._pokerMasterRoom.getOtherPlayer(uid);
                    if (player) {
                        let llCurCoin: number = player.curCoin;
                        playerInfo.txtCoin.string = this._pokerMasterRoom.transGoldShortString(llCurCoin);
                    }
                }
            }
        }
    }

    /**
     * 更新玩家临时金币显示(动画临时变化)
     * @param uid
     * @param amount
     */
    private _updatePlayerTempGold(uid: number, amount: number): void {
        if (uid === this._authService.currentUser.userId) {
            this._txtSelfGold.string = this._pokerMasterRoom.transGoldShortString(amount);
        }

        for (let playerInfo of this._vOtherPlayerInfo) {
            if (playerInfo.uid === uid) {
                // 神算子/富豪是自己的情況
                if (uid === this._authService.currentUser.userId) {
                    playerInfo.txtCoin.string = this._txtSelfGold.string;
                } else {
                    const player = this._pokerMasterRoom.getOtherPlayer(uid);
                    if (player) {
                        playerInfo.txtCoin.string = this._pokerMasterRoom.transGoldShortString(amount);
                    }
                }
            }
        }
    }

    /**
     * 更新所有玩家临时金币显示(动画临时变化)
     */
    private _updateAllPlayerGold(): void {
        // let vPlayerSettles: pokermaster_proto.PlayerSettle[] = pokerMasterDataMgr.getPokerMasterRoom().vPlayerSettles;
        // for (let i = 0; i < vPlayerSettles.length; ++i) {
        //     this._updatePlayerGold(vPlayerSettles[i].uid);
        // }
        this._pokerMasterRoom.roundInfo.getAllPlayerSettles().forEach((playerSettle) => {
            this._updatePlayerGold(playerSettle.uid);
        });
    }

    /**
     * 更新指定玩家连胜状态
     * @param uid
     * @param bAnim
     */
    private _updatePlayerWinCount(uid: number, bAnim: boolean = false): void {
        let vPlayerHeadBgNode: cc.Node[] = this._getPlayerHeadNodesByUid(uid);

        for (let jeadBgNode of vPlayerHeadBgNode) {
            let head: cc.Node = jeadBgNode;

            // 富豪No1 和 神算子 不显示连胜
            if (this._vOtherPlayerInfo[0].nodeHead === head || this._vOtherPlayerInfo[1].nodeHead === head) {
                continue;
            }

            let nodeName: string = 'win_player_win_count_' + head.uuid;
            let winNode: cc.Node = cc.find(nodeName, this.node);
            if (winNode && cc.isValid(winNode, true)) {
                this.node.removeChild(winNode);
                winNode.destroy();
            }
            const player = this._pokerMasterRoom.getPlayer(uid);
            let keepWinCount = player ? player.keepWinCount : 0;
            if (keepWinCount >= 3) {
                keepWinCount = keepWinCount > 10 ? 11 : keepWinCount;

                let offsetY: number = head === this._imgSelfHead.node ? 40 : 70;
                let tmpPos: cc.Vec2 = cc.Vec2.ZERO;
                head.convertToWorldSpaceAR(cc.v2(0, offsetY), tmpPos);

                let sprWinCount: cc.Sprite = new cc.Node().addComponent(cc.Sprite);
                this.node.addChild(sprWinCount.node, PokerMasterDef.LayerZorder.Z_IDX_IMG_WIN_COUNT);
                sprWinCount.node.active = true;
                sprWinCount.node.name = nodeName;
                sprWinCount.node.setPosition(sprWinCount.node.parent.convertToNodeSpaceAR(tmpPos));
                sprWinCount.spriteFrame = this._atlasCbLanguage.getSpriteFrame(`win_count_${keepWinCount}`);

                // animation
                if (bAnim) {
                    let targetPos: cc.Vec2 = cc.v2(sprWinCount.node.position);
                    let bornPos: cc.Vec2 = cc.v2(targetPos);
                    let headMidWorldPos = cc.Vec3.ZERO;
                    head.parent.convertToWorldSpaceAR(head.position, headMidWorldPos);
                    if (headMidWorldPos.x < cc.winSize.width / 2) {
                        let startLeftX: number = (0 - sprWinCount.node.parent.anchorX) * sprWinCount.node.parent.width;
                        bornPos.x = startLeftX - sprWinCount.node.width;
                    } else {
                        let startRightX: number = (1 - sprWinCount.node.parent.anchorX) * sprWinCount.node.parent.width;
                        bornPos.x = startRightX + sprWinCount.node.width;
                    }
                    sprWinCount.node.setPosition(bornPos);

                    let mt: cc.ActionInterval = cc.moveTo(0.8, targetPos);
                    let ebo: cc.ActionInterval = mt.easing(cc.easeBackOut());
                    sprWinCount.node.runAction(ebo);
                }
            }
        }
    }

    /**
     * 更新所有玩家连胜状态
     * @param bAnim
     */
    private _updateAllPlayerWinCount(bAnim: boolean = false): void {
        this._updatePlayerWinCount(this._authService.currentUser.userId, bAnim);

        // 这里按照服务器发的gamePlayers顺序放
        for (let i = 0; i < this._vOtherPlayerInfo.length; ++i) {
            let vOtherPlayer: pf.services.GamePlayer[] = this._pokerMasterRoom.otherPlayers;
            if (i < vOtherPlayer.length) {
                this._updatePlayerWinCount(vOtherPlayer[i].uid, bAnim);
            }
        }
    }

    private createPokerCard(): PokerCardControl {
        const ctrl = cc
            .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.POKER_CARD))
            .getComponent(PokerCardControl);
        ctrl.init();
        ctrl.setCardBackSpriteFrame(pf.addressableAssetManager.getAsset(macros.Assets.CARD_BACK));
        return ctrl;
    }

    /**
     * 初始化牌信息
     */
    private _initCardInfo(): void {
        let nodeCard: cc.Node = this._panelCard.getChildByName('node_card');

        // 牌
        for (let i = 0; i < nodeCard.childrenCount; ++i) {
            let leftCard: cc.Node = nodeCard.getChildByName(`left_card_${i}`);
            if (leftCard) {
                let card: PokerCardControl = this.createPokerCard(); // PokerCardControl.create(this._strCardFacePath, this._strCardBackPath);
                card.ResetFromNode(leftCard);
                this._vLeftHandCards.push(card);
                this._vLeftHandCardsSrcPos.push(cc.v2(card.node.position));
            }

            let rightCard: cc.Node = nodeCard.getChildByName(`right_card_${i}`);
            if (rightCard) {
                let card: PokerCardControl = this.createPokerCard(); // PokerCardControl.create(this._strCardFacePath, this._strCardBackPath);
                card.ResetFromNode(rightCard);
                this._vRightHandCards.push(card);
                this._vRightHandCardsSrcPos.push(cc.v2(card.node.position));
            }

            let pubCard: cc.Node = nodeCard.getChildByName(`pub_card_${i}`);
            if (pubCard) {
                let card: PokerCardControl = this.createPokerCard(); // PokerCardControl.create(this._strCardFacePath, this._strCardBackPath);
                card.ResetFromNode(pubCard);
                this._vPublicHoleCards.push(card);
                this._vPublicHoleCardsSrcPos.push(cc.v2(card.node.position));
            }
        }

        // 牌型
        this._imgLeftCardType = nodeCard.getChildByName('img_left_card_type').getComponent(cc.Sprite);
        this._imgRightCardType = nodeCard.getChildByName('img_right_card_type').getComponent(cc.Sprite);
        this._imgLeftCardTypeBg = nodeCard.getChildByName('img_left_card_type_bg').getComponent(cc.Sprite);
        this._imgRightCardTypeBg = nodeCard.getChildByName('img_right_card_type_bg').getComponent(cc.Sprite);

        let zIdx = 1;
        this._imgLeftCardType.node.zIndex = zIdx;
        this._imgRightCardType.node.zIndex = zIdx;
        this._imgLeftCardTypeBg.node.zIndex = zIdx;
        this._imgRightCardTypeBg.node.zIndex = zIdx;
    }

    /**
     * 设置所有牌显影状态
     * @param visible
     */
    private _setAllCardsVisible(visible: boolean): void {
        for (let handCard of this._vLeftHandCards) {
            handCard.node.stopAllActions();
            handCard.node.active = visible;
        }

        for (let handCard of this._vRightHandCards) {
            handCard.node.stopAllActions();
            handCard.node.active = visible;
        }

        for (let handCard of this._vPublicHoleCards) {
            handCard.node.stopAllActions();
            handCard.node.active = visible;
        }
    }

    /**
     *  显示所有牌正面/背面
     * @param isFace
     * @param isRiver
     */
    private _setAllCardsFace(isFace: boolean): void {
        this._setAllHandsCardsFace(isFace);
        this._setAllPublicHoleCardsFace(isFace, false);
    }

    /**
     * 显示所有手牌正面/背面
     * @param isFace
     */
    private _setAllHandsCardsFace(isFace: boolean): void {
        let vLeftHandCards: domain.CardItem[] = this._pokerMasterRoom.roundInfo.blueHandCards; // pokerMasterDataMgr.getPokerMasterRoom().vLeftHandCards;
        let vRightHandCards: domain.CardItem[] = this._pokerMasterRoom.roundInfo.redHandCards; // pokerMasterDataMgr.getPokerMasterRoom().vRightHandCards;

        for (let i = 0; i < this._vLeftHandCards.length; ++i) {
            this._vLeftHandCards[i].Gray(false);

            if (i < vLeftHandCards.length) {
                this._vLeftHandCards[i].SetContent(vLeftHandCards[i].number, vLeftHandCards[i].suit);
                this._vLeftHandCards[i].SetFace(isFace);
            } else {
                this._vLeftHandCards[i].SetFace(false);
            }
        }

        for (let i = 0; i < this._vRightHandCards.length; ++i) {
            this._vRightHandCards[i].Gray(false);

            if (i < vRightHandCards.length) {
                this._vRightHandCards[i].SetContent(vRightHandCards[i].number, vRightHandCards[i].suit);
                this._vRightHandCards[i].SetFace(isFace);
            } else {
                this._vRightHandCards[i].SetFace(false);
            }
        }
    }

    /**
     * 显示所有公牌正面/背面
     * @param isFace        是否显示正/反面
     * @param exceptRiver   是否排除"river"牌
     */
    private _setAllPublicHoleCardsFace(isFace: boolean, exceptRiver: boolean): void {
        let vPublicHoleCards = this._pokerMasterRoom.roundInfo.publicCards;
        for (let i = 0; i < this._vPublicHoleCards.length; ++i) {
            this._vPublicHoleCards[i].Gray(false);

            if (i < vPublicHoleCards.length) {
                this._vPublicHoleCards[i].SetContent(vPublicHoleCards[i].number, vPublicHoleCards[i].suit);

                // 显示, 则检测"exceptRiver"
                if (isFace) {
                    if (exceptRiver && i === this._vPublicHoleCards.length - 1) {
                        this._vPublicHoleCards[i].SetFace(false);
                    } else {
                        this._vPublicHoleCards[i].SetFace(true);
                    }
                }
                // 不显示, 则不检测
                else {
                    this._vPublicHoleCards[i].SetFace(false);
                }
            } else {
                this._vPublicHoleCards[i].SetFace(false);
            }
        }
    }

    /**
     * 初始化眯牌信息
     */
    private _initSquintCardInfo(): void {
        if (!this._squintCard) {
            this._squintCard = cc.instantiate(this.prefabPmSquidCard).getComponent(PokerMasterSquintCardControl);
            this.node.addChild(this._squintCard.node, PokerMasterDef.LayerZorder.Z_IDX_PANEL_SQUINT);
        }
    }

    /**
     * 重置眯牌信息
     */
    private _resetSquintCardInfo(): void {
        this.isSquintCard = false;
        if (this._squintCard) this._squintCard.hide();
    }

    /**
     * 初始化下注区域
     */
    private _initBetAreas(): void {
        // 区域映射
        let vBetOptionArea: network.BetZoneOption[] = [];
        vBetOptionArea.push(network.BetZoneOption.FISHER_WIN); // 渔夫
        vBetOptionArea.push(network.BetZoneOption.SHARK_WIN); // 鲨鱼
        vBetOptionArea.push(network.BetZoneOption.FIVE_NONE_1DUI); // 高牌/一对
        vBetOptionArea.push(network.BetZoneOption.FIVE_2DUI); // 两对
        vBetOptionArea.push(network.BetZoneOption.FIVE_SAN_SHUN_TONG); // 三条/顺子/同花
        vBetOptionArea.push(network.BetZoneOption.FIVE_GOURD); // 葫芦
        vBetOptionArea.push(network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4); // 皇铜/同花顺/金刚

        let panelArea: cc.Node = this._panelGame.getChildByName('panel_area');
        let panelCoin: cc.Node = this._panelGame.getChildByName('panel_coin');
        let panelTxt: cc.Node = this._panelGame.getChildByName('panel_txt');
        let panelWayOut: cc.Node = this._panelGame.getChildByName('panel_way_out');

        for (let i = 0; i < vBetOptionArea.length; ++i) {
            let areaInfo: PokerMasterAreaInfo = new PokerMasterAreaInfo();
            areaInfo.zoneIndex = i;
            areaInfo.zoneOption = vBetOptionArea[i];
            areaInfo.panelArea = panelArea.getChildByName(`area_${i}`);
            areaInfo.panelCoin = panelCoin.getChildByName(`coin_${i}`);
            areaInfo.txtOdds = panelTxt.getChildByName(`txt_bet_odd_${i}`).getComponent(cc.Label);
            areaInfo.txtSelfBetNum = panelTxt.getChildByName(`txt_self_bet_num_${i}`).getComponent(cc.Label);
            areaInfo.txtTotalBetNum = panelTxt.getChildByName(`txt_total_bet_num_${i}`).getComponent(cc.Label);
            areaInfo.txtTotalBetNum.enableBold = true;
            areaInfo.txtSelfBetNum.enableBold = true;
            areaInfo.txtSelfBetNum.node.color = new cc.Color(232, 201, 147);
            areaInfo.panelArea.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
                this._onClickAreaCoinPanel(areaInfo.zoneIndex);
            });

            // 初始化路子面板
            areaInfo.panelWayOut = panelWayOut.getChildByName(`way_out_${i}`);
            if (areaInfo.panelWayOut) {
                // areaInfo.panelWayOut.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
                //     this._showChart();
                // });

                switch (areaInfo.zoneOption) {
                    // 高牌/一对
                    case network.BetZoneOption.FIVE_NONE_1DUI:
                        areaInfo.wayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                        break;

                    // 两对
                    case network.BetZoneOption.FIVE_2DUI:
                        areaInfo.wayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                        break;

                    // 三条/顺子/同花
                    case network.BetZoneOption.FIVE_SAN_SHUN_TONG:
                        areaInfo.wayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                        break;

                    // 葫芦
                    case network.BetZoneOption.FIVE_GOURD:
                        areaInfo.wayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                        areaInfo.wayOutLoseLimitCount = 200;
                        break;

                    // 皇铜/同花顺/金刚
                    case network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4:
                        areaInfo.wayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                        areaInfo.wayOutLoseLimitCount = 300;
                        break;
                }

                // 路子球状图片
                let count: number = areaInfo.panelWayOut.childrenCount;
                for (let iWayoutIndex = 0; iWayoutIndex < count; ++iWayoutIndex) {
                    let img: cc.Node = areaInfo.panelWayOut.getChildByName(`img_${iWayoutIndex}`);
                    if (img) {
                        img.active = false;
                        areaInfo.wayOutImgArray.push(img.getComponent(cc.Sprite));
                        areaInfo.wayOutImgSrcPosArray.push(cc.v2(img.position));
                    }
                }

                // 文本
                let txtWayOut: cc.Node = panelTxt.getChildByName(`txt_way_out_${i}`);
                if (txtWayOut) {
                    areaInfo.rtxtWayOut = txtWayOut.getComponent(cc.Label);
                    areaInfo.rtxtWayOut.node.active = false;
                    // areaInfo.rtxtWayOut.handleTouchEvent = false;
                }
            }
            // push 区域数组
            this._vAreasInfo.push(areaInfo);
        }
    }

    /**
     * 更新下注区域是否可触摸
     */
    private _updateBetAreaTouchEnabled(): void {
        for (let areasInfo of this._vAreasInfo) {
            let pArea: PokerMasterAreaInfo = areasInfo;
            let bEnabled = true;
            bEnabled = bEnabled && this._pokerMasterRoom.gameState.roundState === network.RoundState.BET;
            bEnabled = bEnabled && this._getLeftTime() > 0;
            if (bEnabled) {
                pArea.panelArea.resumeSystemEvents(false);
            } else {
                pArea.panelArea.pauseSystemEvents(false);
            }
        }
    }

    /**
     * 重置所有下注区域
     */
    private _resetAllBetAreas(): void {
        for (let areasInfo of this._vAreasInfo) {
            this._resetBetArea(areasInfo.zoneOption);
        }
    }

    /**
     * 重置指定下注区域
     * @param betOption
     */
    private _resetBetArea(betOption: network.BetZoneOption): void {
        // 填充下注文本默认值
        this._updateBetAreaBetsNum(betOption, 0, 0);
    }

    /**
     * 更新下注区域下注数量
     * @param betOption
     * @param llTotalAmount
     * @param llSelfAmount
     */
    private _updateBetAreaBetsNum(
        betOption: network.BetZoneOption,
        llTotalAmount: number = -1,
        llSelfAmount: number = -1
    ): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let totalBet = 0;
        let selfBet = 0;
        let zoneData = this._pokerMasterRoom.betZones.get(betOption);
        if (zoneData) {
            totalBet = zoneData.optionInfo.totalBet;
            selfBet = zoneData.optionInfo.selfBet;
        }

        let _llTotalAmount = llTotalAmount < 0 ? totalBet : llTotalAmount;
        let _llSelfAmount = llSelfAmount < 0 ? selfBet : llSelfAmount;

        // 自己下注
        if (_llSelfAmount > 0) {
            let strAmount: string = this._pokerMasterRoom.transGoldShortString(_llSelfAmount, 5);
            this._vAreasInfo[nAreaIdx].txtSelfBetNum.string = strAmount;
        } else {
            this._vAreasInfo[nAreaIdx].txtSelfBetNum.string = '';
        }

        // 总注
        let strTotal: string = this._pokerMasterRoom.transGoldShortString(_llTotalAmount, 5);
        this._vAreasInfo[nAreaIdx].txtTotalBetNum.string = strTotal;
    }

    /**
     * 初始化金币池
     */
    private _initCoinPool(): void {
        this._nodeCoinPool = new cc.Node();
        this._nodeCoinPool.setContentSize(cc.winSize);
        this.node.addChild(this._nodeCoinPool, PokerMasterDef.LayerZorder.Z_IDX_COIN_POOL);

        for (let areasInfo of this._vAreasInfo) {
            let zoneOption: network.BetZoneOption = areasInfo.zoneOption;
            let deque: Deque<HumanboyBetCoinControl> = this._mapCoinQueue.get(zoneOption);
            if (!deque) {
                deque = new Deque();
                this._mapCoinQueue.set(zoneOption, deque);
            }
        }
    }

    /**
     * 重置所有下注区域的金币节点
     */
    private _resetAllBetAreaCoins(): void {
        this._mapCoinQueue.forEach((deque: Deque<HumanboyBetCoinControl>, option: network.BetZoneOption): any => {
            this._resetBetAreaCoins(option);
        });

        // 重置金币池节点深度计数
        this._llCoinPoolZOrderCount = 0;

        // 清理"金币最优队列"
        this._vCoinOptimizationDeque.clear();
    }

    private _clearCurrentAreaCoinsBeyondFly(): void {
        this._mapCoinQueue.forEach((deque: Deque<HumanboyBetCoinControl>, option: network.BetZoneOption): any => {
            this._resetBetAreaCoins(option);
        });

        // 重置金币池节点深度计数
        this._llCoinPoolZOrderCount = 0;
        // this._mapCoinQueue.forEach((option: network.BetZoneOption, deque: Deque<HumanboyBetCoinControl>): any => {
        //     if (deque) {
        //         for (let i = 0; i < deque.size(); ++i) {
        //             let coin: HumanboyBetCoinControl = deque.at(i);
        //             if (!coin || !cc.isValid(coin, true)) return null;
        //             if (cc.director.getActionManager().getNumberOfRunningActionsInTarget(coin.node) <= 0) {
        //                 this._resetCoin(coin);
        //             }
        //         }
        //     }
        // });
    }

    /**
     * 重置指定下注区域的所有金币节点
     * @param betOption
     */
    private _resetBetAreaCoins(betOption: network.BetZoneOption): void {
        let deque: Deque<HumanboyBetCoinControl> = this._mapCoinQueue.get(betOption);
        if (deque) {
            for (let i = 0; i < deque.size(); ++i) {
                let coin: HumanboyBetCoinControl = deque.at(i);
                this._resetCoin(coin);
            }
        }
    }

    /**
     * 重置指定金币节点
     * @param coin
     */
    private _resetCoin(coin: HumanboyBetCoinControl): HumanboyBetCoinControl {
        if (!coin || !cc.isValid(coin, true)) return null;

        coin.node.zIndex = 0;
        coin.node.opacity = 0xff;
        coin.node.angle = 0;
        coin.node.setPosition(cc.Vec2.ZERO);
        coin.node.stopAllActions();
        coin.node.active = false;

        coin.txtBetNode.opacity = 0xff;
        coin.txtBetNode.active = true;

        coin.btn.enabled = false;
        coin.imgMask.node.active = false;

        return coin;
    }

    /**
     * 重新生成"所有下注区域"金币位置(因为桌布改变了, 即下注区域改变了, 需要重新生成位置)
     */
    // private _regenerateAllBetAreaCoinsPos(): void {
    //     this._mapCoinQueue.forEach((option: network.BetZoneOption, deque: Deque<HumanboyBetCoinControl>): any => {
    //         this._regenerateAreaCoinsPos(option);
    //     });
    // }

    /**
     * 重新生成"指定下注区域"金币位置
     */
    private _regenerateAreaCoinsPos(betOption: network.BetZoneOption): void {
        let deque: Deque<HumanboyBetCoinControl> = this._mapCoinQueue.get(betOption);
        if (deque) {
            let nAreaIdx = this._getAreaIdxByBetOption(betOption);
            for (let i = 0; i < deque.size(); ++i) {
                let coin: HumanboyBetCoinControl = deque.at(i);
                if (!coin.node.active) continue;

                let pos = this._getCoinRandomPos(coin.node, nAreaIdx, true);
                coin.node.parent.convertToNodeSpaceAR(pos, pos);
                coin.node.setPosition(pos);
            }
        }
    }

    /**
     * 恢复所有区域的金币显示与否
     * @param bShowCoin
     */
    private _recoverAreasCoin(bShowCoin: boolean): void {
        this._pokerMasterRoom.betZones.forEach((betZone: domain.BetZone, option: network.BetZoneOption) => {
            let nAreaIdx: number = this._getAreaIdxByBetOption(option);
            if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return 'continue';

            let vTotalBetDetail: number[] = betZone.optionInfo.amounts;

            for (let detail of vTotalBetDetail) {
                if (bShowCoin) {
                    this._showCoinAnim(nAreaIdx, detail, 0, false, false, false);
                }
            }

            // 更新下注区域
            this._updateBetAreaBetsNum(option);
        });
    }

    /**
     * 创建金币
     * @param gold
     */
    private _createFlyCoin(gold: number): HumanboyBetCoinControl {
        let node: cc.Node = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.BET_COIN));
        node.zIndex = 0;
        node.setAnchorPoint(0.5, 0.5);
        node.setScale(this.fFlyCoinScaleRate);
        node.setPosition(cc.Vec2.ZERO);

        let coin: HumanboyBetCoinControl = node.getComponent(HumanboyBetCoinControl);
        coin.setShape(this._getBetCoinShapeByAmount(gold));
        coin.setTxtNum(pf.StringUtil.serverGoldToShowNumber(gold));
        coin.btn.enabled = false;

        return coin;
    }

    /**
     * 获取金币外形枚举值
     * @param gold
     */
    private _getBetCoinShapeByAmount(gold: number): number {
        let llRealGold: number = pf.StringUtil.clientGoldByServer(gold);
        let shape: number =
            llRealGold < this._pokerMasterRoom.llCoinUICritical
                ? HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_COIN
                : HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_BLOCK;
        return shape;
    }

    /**
     * 获取指定区域金币的随机位置
     * @param coin
     * @param nAreaIdx
     * @param bWorldPos
     */
    protected _getCoinRandomPos(coin: cc.Node, nAreaIdx: number, bWorldPos: boolean): cc.Vec2 {
        if (!coin || nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return cc.Vec2.ZERO;
        let szPanel: cc.Size = this._vAreasInfo[nAreaIdx].panelCoin.getContentSize();
        let szCoin: cc.Size = cc.size(cc.Size.ZERO);
        szCoin.width = coin.width * coin.scaleX;
        szCoin.height = coin.height * coin.scaleY;

        let halfW: number = szPanel.width / 2;
        let halfH: number = szPanel.height / 2;

        let offsetX: number = Math.floor(halfW - szCoin.width / 2);
        let offsetY: number = Math.floor(halfH - szCoin.height / 2);

        // 以中心锚点为原点, 按照方圆随机位置
        let signX: number = pf.StringUtil.randomRange(0, 2) < 1 ? -1 : 1;
        let signY: number = pf.StringUtil.randomRange(0, 2) < 1 ? -1 : 1;
        let x: number = signX * pf.StringUtil.randomRange(0, offsetX);
        let y: number = signY * pf.StringUtil.randomRange(0, offsetY);

        let retPos: cc.Vec2 = cc.v2(x, y);
        if (bWorldPos) {
            this._vAreasInfo[nAreaIdx].panelCoin.convertToWorldSpaceAR(retPos, retPos);
        }
        return retPos;
    }

    /**
     * 从对应区域的金币池中获取金币节点
     * @param nAreaIdx
     * @param gold
     */
    private _getCoinFromPool(option: network.BetZoneOption, gold: number): HumanboyBetCoinControl {
        let coin: HumanboyBetCoinControl = null;
        let deque: Deque<HumanboyBetCoinControl> = this._mapCoinQueue.get(option);
        if (deque) {
            // 未达区域金币精灵上限, 则创建新的精灵
            if (deque.size() < this._nAreaCoinLimitCountMin) {
                coin = this._createFlyCoin(gold);
                coin.node.zIndex = ++this._llCoinPoolZOrderCount;
                coin.node.setPosition(cc.Vec2.ZERO);

                this._nodeCoinPool.addChild(coin.node);
                deque.push_back(coin);
            }
            // 达到上限, 从金币精灵池中取(重复利用)
            else {
                // 出队
                coin = deque.pop_front();
                coin = this._resetCoin(coin);
                if (coin) {
                    coin.node.zIndex = ++this._llCoinPoolZOrderCount;

                    coin.setShape(this._getBetCoinShapeByAmount(gold));
                    coin.setTxtNum(pf.StringUtil.serverGoldToShowNumber(gold));

                    // 入队
                    deque.push_back(coin);
                }
            }
        }
        return coin;
    }

    /**
     * 获取对应区域空闲的金币节点数量
     * @param nAreaIdx
     */
    private _getFreeCoinCountFromPool(option: network.BetZoneOption): number {
        let nRet = 0;
        let deque: Deque<HumanboyBetCoinControl> = this._mapCoinQueue.get(option);
        if (deque) {
            for (let i = 0; i < deque.size(); ++i) {
                if (!deque.at(i).node.active) ++nRet;
            }
        }
        return nRet;
    }

    /**
     * 刷新"金币最优队列"(每帧创建, 稳定帧率)
     */
    private _updateCoinOptimization(dt: number): void {
        let nTotalCount: number = this._vCoinOptimizationDeque.size();
        if (nTotalCount <= 0) return;

        let eCurState: network.RoundState = this._pokerMasterRoom.gameState.roundState;
        if (eCurState === network.RoundState.BET && this._getLeftTime() >= 0) {
            let nCount = 0;

            // 剩余时间 > 1s 逐帧喷吐金币
            if (this._getLeftTime() > 1) {
                nCount = nTotalCount / cc.game.getFrameRate();
                nCount = Math.ceil(nCount);
            }
            // 否则, 一次性喷吐剩余金币(弥补金币数量多、卡帧导致喷吐金币不完整的情况)
            else {
                nCount = nTotalCount;
            }

            // console.log(pf.StringUtil.formatC("PokerMaster_Coin: sec = %02d, dt = %05f, total = %05f, count = %05f", this._getLeftTime(), dt, nTotalCount, nCount))

            for (let i = 0; i < nCount; ++i) {
                let t: HumanboyCoinOptimization = this._vCoinOptimizationDeque.pop_front();

                // 投金币动画
                this._showCoinAnim(t.nAreaIdx, t.nGold, t.nUid, t.bAnim, t.bHeadAnim, t.bPlaySound);

                // 更新下注区域
                this._updateBetAreaBetsNum(this._getBetOptionByAreaIdx(t.nAreaIdx));

                // 更新玩家金币
                this._updatePlayerGold(t.nUid);

                // 自己筹码变化后判断一下下注筹码状态
                if (this._pokerMasterRoom.selfPlayer.uid === t.nUid) {
                    this._updateBetBtnState();
                }
            }
        } else {
            // 更新剩余的金币数等(在卡帧情况下, 计时误差等情况下, 飞金币被强行停止, 但数据要保持最新, 因为这是一个逐帧队列, 不是及时更新)
            for (let i = 0; i < nTotalCount; ++i) {
                let t: HumanboyCoinOptimization = this._vCoinOptimizationDeque.pop_front();

                // 更新下注区域
                this._updateBetAreaBetsNum(this._getBetOptionByAreaIdx(t.nAreaIdx));

                // 更新玩家金币
                this._updatePlayerGold(t.nUid);

                // 自己筹码变化后判断一下下注筹码状态
                if (this._pokerMasterRoom.selfPlayer.uid === t.nUid) {
                    this._updateBetBtnState();
                }
            }

            // 清除队列
            this._vCoinOptimizationDeque.clear();
        }
    }

    /**
     * 初始化下注按钮
     */
    private _initBetButtons(): void {
        for (let i = 0; i < this._nBetBtnNum; ++i) {
            let betCoin: HumanboyBetCoinControl = this._panelBetBtn
                .getChildByName(`btn_bet_${i}`)
                .getComponent(HumanboyBetCoinControl);
            betCoin.node.setScale(this.fBetBtnSrcScaleRate);
            betCoin.node.on('click', (event: cc.Event): void => {
                this._playSoundEffect(macros.Audio.Button);
                this._setBetBtnSelected(i);
            });
            this._vBetButtons.push(betCoin);
        }

        // 初始化高级续投面板
        if (!this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto = cc
                .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_ADVANCED_AUTO))
                .getComponent(HumanboyAdvancedAutoControl);
            this.node.addChild(
                this._humanboyAdvancedAuto.node,
                PokerMasterDef.LayerZorder.Z_IDX_PANEL_ADVANCE_AUTO_SELECT
            );
        }

        // 续投按钮
        this._btnBetAuto = this._panelBetBtn.getChildByName('btn_bet_auto').getComponent(cc.Button);
        this._btnBetAuto.node.on('click', (event: cc.Event): void => {
            this._playSoundEffect(macros.Audio.Button);

            switch (this._eAutoBtnStyle) {
                // 常规续投点击
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                    this._pokerMasterRoom.autoBet().then(() => {
                        this._onMsgAutoBet();
                    });
                    // cv.pokerMasterNet.requestAutoBet();
                    break;

                // 高级续投已激活(再次点击 弹出高级续投选项面板)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                    // if (this._pokerMasterRoom.gameState.roundState === network.RoundState.BET) {
                    this._humanboyAdvancedAuto.adaptSelectPanelPos(this._btnBetAuto.node);
                    this._humanboyAdvancedAuto.showSelectPanel(true);
                    // }
                    break;

                // 高级续投中(再次点击取消)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                    {
                        let iUsedAutoBetCount: number = this._pokerMasterRoom.betSettings.usedAutoBetCount;
                        let iSelectAutoBetCount: number = this._pokerMasterRoom.betSettings.selectAutoBetCount;

                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_DIALOG)
                        );
                        dialogNode.name = 'GAB_STYLE_ADVANCE_USING_tips';
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, PokerMasterDef.LayerZorder.Z_IDX_PANEL_SERVER_TOAST);

                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const stringContent = pf.StringUtil.formatC(
                            pf.languageManager.getString('Cowboy_auto_bet_stop_tips'),
                            iUsedAutoBetCount,
                            iSelectAutoBetCount
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_stop_auto_bet');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_resume_auto_bet');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            this._pokerMasterRoom.cancelAdavnceAutoBet();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            miniGameDialog?.close();
                        };
                        const stringCenter = pf.languageManager.getString('MiniGame_AddAutoBet_Text');
                        const cbCenterBtn = (dialog: MiniGameDialog) => {
                            this.showAutoAddBetList(dialog);
                        };

                        const _onUpdateContent = (dialog: IMiniGameDialog) => {
                            if (legacyDialog) {
                                legacyDialog.txt_content.string = pf.StringUtil.calculateAutoWrapString(
                                    legacyDialog.txt_content.node,
                                    pf.StringUtil.formatC(
                                        pf.languageManager.getString('Cowboy_auto_bet_stop_tips'),
                                        this._pokerMasterRoom.betSettings.usedAutoBetCount,
                                        this._pokerMasterRoom.betSettings.selectAutoBetCount
                                    )
                                );
                            }
                            if (this._pokerMasterRoom.betSettings.reachLimitBet) {
                                miniGameDialog?.blockCenterButton();
                            }
                        };
                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: this._pokerMasterRoom.betSettings.reachLimitBet,
                            legacyDialog,
                            isShowBtnCenter: true,
                            stringCenterButton: stringCenter,
                            cbCenterBtn,
                            onUpdateContent: _onUpdateContent
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                    }
                    break;

                default:
                    break;
            }
        });

        // 清屏按钮
        this._btnBetClean = this._panelBetBtn.getChildByName('btn_bet_clean').getComponent(cc.Button);
        this._btnBetClean.normalSprite = this._atlasCbLanguage.getSpriteFrame('clean_screen_normal');
        this._btnBetClean.pressedSprite = this._atlasCbLanguage.getSpriteFrame('clean_screen_normal');
        this._btnBetClean.hoverSprite = this._atlasCbLanguage.getSpriteFrame('clean_screen_normal');
        this._btnBetClean.disabledSprite = this._atlasCbLanguage.getSpriteFrame('clean_screen_gray');
        this._btnBetClean.node.on('click', (event: cc.Event): void => {
            this._clearCurrentAreaCoinsBeyondFly();
        });

        // 默认选中第一个下注按钮
        this._setBetBtnSelected(0, false);
    }

    /**
     * 更新续投按钮状态
     */
    private _updateAutoBetBtnStatus(): void {
        switch (this._eAutoBtnStyle) {
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                if (this._pokerMasterRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0) {
                    // 当前一局下过注
                    if (this._pokerMasterRoom.roundInfo.hasBetInCurRound) {
                        this._btnBetAuto.interactable = false;
                    } else {
                        let canAuto: boolean = this._pokerMasterRoom.betSettings.canAutoBet;
                        this._btnBetAuto.interactable = canAuto;
                    }
                } else {
                    this._btnBetAuto.interactable = false;
                }
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                // 当前一局下过注
                if (this._pokerMasterRoom.roundInfo.hasBetInCurRound) {
                    this._btnBetAuto.interactable = true;
                } else {
                    let canAuto = this._pokerMasterRoom.betSettings.canAutoBet;
                    this._btnBetAuto.interactable = canAuto;
                }
                break;
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                this._btnBetAuto.interactable = true;
                break;

            default:
                break;
        }
    }

    /**
     * 更新清屏按钮状态
     */
    private _updateCleanBtnStatus(): void {
        let bEnable = false;
        if (this._pokerMasterRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0) {
            bEnable = true;
        }
        this._btnBetClean.getComponent(cc.Button).interactable = bEnable;
    }

    /**
     * 设置筹码单选按钮选中状态
     * @param betBtnIdx
     * @param isCheckCoin
     */
    private _setBetBtnSelected(betBtnIdx: number, isCheckCoin: boolean = true): void {
        if (betBtnIdx < 0 || betBtnIdx >= this._nBetBtnNum) return;

        this._resetAllBetBtn(true);
        this._updateBetBtnState();

        let vBetCoinOption: number[] = this._pokerMasterRoom.betSettings.betCoinOptions; // 房间下注级别
        let curCoin: number = this._pokerMasterRoom.selfPlayer.curCoin; // 当前自身携带金币

        if (betBtnIdx >= 0 && betBtnIdx < this._nBetBtnNum) {
            // 钱是否够按钮上的金额
            if (isCheckCoin) {
                let llAmountLevel = 0;
                if (betBtnIdx < vBetCoinOption.length) {
                    llAmountLevel = vBetCoinOption[betBtnIdx];
                }

                if (curCoin > 0 && curCoin >= llAmountLevel) {
                    this._nCurBetBtnIndex = betBtnIdx;
                    this._vBetButtons[betBtnIdx].node.setScale(this.fBetBtnTarScaleRate);
                }
            } else {
                this._nCurBetBtnIndex = betBtnIdx;
                this._vBetButtons[betBtnIdx].node.setScale(this.fBetBtnTarScaleRate);
            }
        }
    }

    /**
     * 重置指定下注按钮
     * @param index
     * @param enabled
     */
    private _resetBetBtn(index: number, enabled: boolean): void {
        if (index < 0 || index >= this._vBetButtons.length) return;

        this._vBetButtons[index].btn.enabled = enabled;
        this._vBetButtons[index].node.setScale(this.fBetBtnSrcScaleRate);
        this._vBetButtons[index].txtBetNode.active = true;
        this._vBetButtons[index].imgMask.node.active = false;

        if (this._nCurBetBtnIndex === index) this._nCurBetBtnIndex = -1;
    }

    /**
     * 重置下注按钮
     * @param enabled
     */
    private _resetAllBetBtn(enabled: boolean): void {
        for (let i = 0; i < this._vBetButtons.length; ++i) {
            this._resetBetBtn(i, enabled);
        }
    }

    /**
     * 更新下注按钮状态
     * @param bCheckCoin 是否检测金币数量(默认:true, 之所以提出来一个参数, 是因为一局结束通知也调用了该函数, 为了避免一局结束提前通过按钮状态知道输赢结果, 显示更友好)
     */
    private _updateBetBtnState(bCheckCoin: boolean = true): void {
        // 检测下注按钮禁用与否
        let vBetCoinOption: number[] = this._pokerMasterRoom.betSettings.betCoinOptions; // 房间下注级别
        let curCoin: number = this._pokerMasterRoom.selfPlayer.curCoin; // 当前自身携带金币
        for (let i = 0; i < vBetCoinOption.length; ++i) {
            // 钱是否够按钮上的金额
            if (curCoin >= vBetCoinOption[i]) {
                this._vBetButtons[i].btn.enabled = true;
                this._vBetButtons[i].btn.interactable = true;
                this._vBetButtons[i].setTxtColor(HumanboyBetCoinControl.eHumanboyBetCoinTxtColor.YELLOW);
            } else {
                this._vBetButtons[i].btn.enabled = false;
                this._vBetButtons[i].btn.interactable = false;
                this._vBetButtons[i].setTxtColor(HumanboyBetCoinControl.eHumanboyBetCoinTxtColor.GRAY);
            }
        }

        // 检测下注按钮可触摸与否
        let bEffective: boolean =
            this._pokerMasterRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0;

        for (let button of this._vBetButtons) {
            button.imgMask.node.active = !bEffective;
            button.imgMask.getComponent(cc.BlockInputEvents).enabled = true;
        }

        // 更新续投按钮状态
        this._updateAutoBetBtnStatus();

        // 更新清屏按钮状态
        this._updateCleanBtnStatus();
    }

    /**
     * 设置续投按钮样式
     * @param eAutoBtnStyle
     */
    private _setAutoBetBtnStytle(eAutoBtnStyle: MiniGameCommonDef.eGameboyAutoBtnStyle) {
        // 隐藏高级续投子面板
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.hideAdvanceAutoTips();
            this._humanboyAdvancedAuto.hideAdvanceAutoCount();
            this._humanboyAdvancedAuto.hideSelectPanel(false);
        }

        // 更新续投按钮样式
        this._eAutoBtnStyle = eAutoBtnStyle;
        switch (this._eAutoBtnStyle) {
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NONE:
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                this._btnBetAuto.normalSprite = this._atlasCbLanguage.getSpriteFrame('autobet_normal');
                this._btnBetAuto.pressedSprite = this._atlasCbLanguage.getSpriteFrame('autobet_normal');
                this._btnBetAuto.hoverSprite = this._atlasCbLanguage.getSpriteFrame('autobet_normal');
                this._btnBetAuto.disabledSprite = this._atlasCbLanguage.getSpriteFrame('autobet_gray');
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                this._btnBetAuto.normalSprite = this._atlasCbLanguage.getSpriteFrame('autobet_block_normal');
                this._btnBetAuto.pressedSprite = this._atlasCbLanguage.getSpriteFrame('autobet_block_normal');
                this._btnBetAuto.hoverSprite = this._atlasCbLanguage.getSpriteFrame('autobet_block_normal');
                this._btnBetAuto.disabledSprite = this._atlasCbLanguage.getSpriteFrame('autobet_block_gray');
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                this._btnBetAuto.normalSprite = this._atlasCbLanguage.getSpriteFrame('autobet_block_using');
                this._btnBetAuto.pressedSprite = this._atlasCbLanguage.getSpriteFrame('autobet_block_using');
                this._btnBetAuto.hoverSprite = this._atlasCbLanguage.getSpriteFrame('autobet_block_using');
                this._btnBetAuto.disabledSprite = this._atlasCbLanguage.getSpriteFrame('autobet_block_gray');

                if (this._humanboyAdvancedAuto) {
                    this._humanboyAdvancedAuto.adaptAdvanceAutoCountPos(this._btnBetAuto.node);
                    this._humanboyAdvancedAuto.showAdvanceAutoCount();
                }
                break;

            default:
                break;
        }

        let imgBetAuto: cc.Sprite = this._btnBetAuto.getComponent(cc.Sprite);
        imgBetAuto.type = cc.Sprite.Type.SIMPLE;
        imgBetAuto.sizeMode = cc.Sprite.SizeMode.RAW;
    }

    /**
     * 获取续投按钮样式
     */
    private _getAutoBetBtnStytle(): MiniGameCommonDef.eGameboyAutoBtnStyle {
        return this._eAutoBtnStyle;
    }

    /**
     * 加载时间轴动画文件
     */
    private _initTimelineAnims(): void {
        this._nodeAnim = new cc.Node();
        this._nodeAnim.setContentSize(cc.winSize);
        this._nodeAnim.setAnchorPoint(cc.v2(0.5, 0.5));
        this._nodeAnim.setPosition(cc.Vec2.ZERO);
        this.node.addChild(this._nodeAnim, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE);

        // 动画数组
        do {
            for (let i = 0; i < this._vAreasInfo.length; ++i) {
                let zoneOption: network.BetZoneOption = this._vAreasInfo[i].zoneOption;
                // win
                do {
                    let anim: cc.Node = cc.instantiate(
                        pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.WIN_FLAG)
                    );
                    anim.active = false;
                    this._nodeAnim.addChild(anim, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);
                    this._mapAnimWinFlags.set(zoneOption, anim.getComponent(cc.Animation));

                    let pos: cc.Vec2 = cc.Vec2.ZERO;
                    this._vAreasInfo[i].panelCoin.convertToWorldSpaceAR(cc.v2(0, -170), pos);
                    anim.parent.convertToNodeSpaceAR(pos, pos);
                    anim.setPosition(pos);
                } while (false);

                // 赔率
                do {
                    let anim: cc.Node = cc.instantiate(this.prefabPmShowOdds);
                    anim.active = false;
                    if (i >= 2) {
                        let txtOdds = anim.getChildByName('txt_odds');
                        if (txtOdds) {
                            txtOdds.getComponent(cc.Label).fontSize = 30;
                        }
                    }
                    this._nodeAnim.addChild(anim, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);
                    this._mapAnimShowOdds.set(zoneOption, anim.getComponent(cc.Animation));
                    let pos = cc.Vec3.ZERO;
                    this._vAreasInfo[i].txtOdds.node.parent.convertToWorldSpaceAR(
                        this._vAreasInfo[i].txtOdds.node.position,
                        pos
                    );
                    anim.parent.convertToNodeSpaceAR(pos, pos);
                    anim.setPosition(pos);
                } while (false);
            }
        } while (false);

        // 开局动画
        do {
            this._animRoundStart = cc.instantiate(this.prefabCbRoundStart).getComponent(cc.Animation);
            this._animRoundStart.node.active = false;
            this._animRoundStart.node.setPosition(cc.Vec2.ZERO);
            this._nodeAnim.addChild(this._animRoundStart.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);
            if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
                let sharkTip = cc.find('vs_shark/shark_tip', this._animRoundStart.node);
                let masterTip = cc.find('vs_tom/master_tip', this._animRoundStart.node);
                // cv.resMgr.setSpriteFrame(shark_tip, 'en_US/game/pokermaster/shark_tip');
                pf.addressableAssetManager
                    .loadAsset(macros.Dynamic_Assets.POKER_MASTER_SHARK_TIP_SPRITE)
                    .then((asset: cc.SpriteFrame) => {
                        sharkTip.getComponent(cc.Sprite).spriteFrame = asset;
                    });
                // shark_tip.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset('en_US/game/pokermaster/shark_tip');
                // cv.resMgr.setSpriteFrame(master_tip, 'en_US/game/pokermaster/master_tip');
                pf.addressableAssetManager
                    .loadAsset(macros.Dynamic_Assets.POKER_MASTER_TIP_SPRITE)
                    .then((asset: cc.SpriteFrame) => {
                        masterTip.getComponent(cc.Sprite).spriteFrame = asset;
                    });
                // master_tip.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset('en_US/game/pokermaster/master_tip');
            }
        } while (false);

        // 开始下注动画
        do {
            this._animStartBet = cc
                .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.START_BETS))
                .getComponent(cc.Animation);
            this._nodeAnim.addChild(this._animStartBet.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);

            this._animStartBet.node.setPosition(cc.Vec2.ZERO);
            this._animStartBet.node.active = false;

            // // 切换语言, 切换资源
            // 切换语言, 切换资源
            let imgClosBetNode: cc.Node = this._animStartBet.node.getChildByName('Sprite_2');
            imgClosBetNode.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_GAME_RUN_SPRITE_002
            );
            // cv.resMgr.setSpriteFrame(
            //     img_clos_bet_node,
            //     cv.config.getLanguagePath('game/humanboy/animation/game_round/003')
            // );

            let imgTimesUpNode: cc.Node = this._animStartBet.node.getChildByName('Sprite_7');
            imgTimesUpNode.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_GAME_RUN_SPRITE_004
            );
            // cv.resMgr.setSpriteFrame(
            //     img_times_up_node,
            //     cv.config.getLanguagePath('game/humanboy/animation/game_round/005')
            // );
            // let imgStartBetNode: cc.Node = this._animStartBet.node.getChildByName('Sprite_2');
            // // cv.resMgr.setSpriteFrame(img_start_bet_node, cv.config.getLanguagePath('game/humanboy/animation/game_round/002'));
            // // img_start_bet_node.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset('game/humanboy/animation/game_round/002');
            // pf.addressableAssetManager.loadAsset('game/humanboy/animation/game_round/002').then((asset: cc.SpriteFrame) => {
            //     imgStartBetNode.getComponent(cc.Sprite).spriteFrame = asset;
            // });

            // let imgChooseBoxNode: cc.Node = this._animStartBet.node.getChildByName('Sprite_7');
            // // cv.resMgr.setSpriteFrame(img_choose_box_node, cv.config.getLanguagePath('game/humanboy/animation/game_round/004'));
            // // img_choose_box_node.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset('game/humanboy/animation/game_round/004');
            // pf.addressableAssetManager.loadAsset('game/humanboy/animation/game_round/004').then((asset: cc.SpriteFrame) => {
            //     imgChooseBoxNode.getComponent(cc.Sprite).spriteFrame = asset;
            // });
        } while (false);

        // 停止下注动画
        do {
            this._animStopBet = cc
                .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.END_BETS))
                .getComponent(cc.Animation);
            this._nodeAnim.addChild(this._animStopBet.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);

            this._animStopBet.node.setPosition(cc.Vec2.ZERO);
            this._animStopBet.node.active = false;

            // 切换语言, 切换资源
            let imgClosBetNode: cc.Node = this._animStopBet.node.getChildByName('Sprite_2');
            imgClosBetNode.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_GAME_RUN_SPRITE_003
            );
            // cv.resMgr.setSpriteFrame(
            //     img_clos_bet_node,
            //     cv.config.getLanguagePath('game/humanboy/animation/game_round/003')
            // );

            let imgTimesUpNode: cc.Node = this._animStopBet.node.getChildByName('Sprite_7');
            imgTimesUpNode.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_GAME_RUN_SPRITE_005
            );
            // cv.resMgr.setSpriteFrame(
            //     img_times_up_node,
            //     cv.config.getLanguagePath('game/humanboy/animation/game_round/005')
            // );
            // let imgClosBetNode: cc.Node = this._animStopBet.node.getChildByName('Sprite_2');
            // // cv.resMgr.setSpriteFrame(img_clos_bet_node, cv.config.getLanguagePath('game/humanboy/animation/game_round/003'));
            // // img_clos_bet_node.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset('game/humanboy/animation/game_round/003');
            // pf.addressableAssetManager.loadAsset('game/humanboy/animation/game_round/003').then((asset: cc.SpriteFrame) => {
            //     imgClosBetNode.getComponent(cc.Sprite).spriteFrame = asset;
            // });

            // let imgTimesUpNode: cc.Node = this._animStopBet.node.getChildByName('Sprite_7');
            // // cv.resMgr.setSpriteFrame(img_times_up_node, cv.config.getLanguagePath('game/humanboy/animation/game_round/005'));
            // // img_times_up_node.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset('game/humanboy/animation/game_round/005');
            // pf.addressableAssetManager.loadAsset('game/humanboy/animation/game_round/005').then((asset: cc.SpriteFrame) => {
            //     imgTimesUpNode.getComponent(cc.Sprite).spriteFrame = asset;
            // });
        } while (false);

        // 路单闪光动画
        do {
            let pos = cc.Vec3.ZERO;
            this._btnReview.parent.convertToWorldSpaceAR(this._btnReview.position, pos);

            this._animWayoutLight = cc
                .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.WAY_OUT))
                .getComponent(cc.Animation);
            this._nodeAnim.addChild(this._animWayoutLight.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);

            this._animWayoutLight.node.setPosition(this._animWayoutLight.node.parent.convertToNodeSpaceAR(pos));
            this._animWayoutLight.node.active = false;
        } while (false);

        // 输赢动画
        do {
            let nodeFisherman = this._panelCard.getChildByName('node_fisherman');
            let nodeShark = this._panelCard.getChildByName('node_shark');
            let fishPos = nodeFisherman.getChildByName('img').position;
            let sharkPos = nodeShark.getChildByName('img').position;

            let aniArr = [this.prefabDashiWin, this.prefabDashiLose, this.prefabSharkWin, this.prefabSharkLose];

            this._animDashiWin = cc.instantiate(this.prefabDashiWin).getComponent(cc.Animation);
            nodeFisherman.addChild(this._animDashiWin.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);

            this._animDashiLose = cc.instantiate(this.prefabDashiLose).getComponent(cc.Animation);
            nodeFisherman.addChild(this._animDashiLose.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);

            this._animDashiWin.node.setPosition(fishPos);
            this._animDashiLose.node.setPosition(fishPos);
            this._animDashiWin.node.active = false;
            this._animDashiLose.node.active = false;

            this._animSharkWin = cc.instantiate(this.prefabSharkWin).getComponent(cc.Animation);
            nodeShark.addChild(this._animSharkWin.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);

            this._animSharkLose = cc.instantiate(this.prefabSharkLose).getComponent(cc.Animation);
            nodeShark.addChild(this._animSharkLose.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);

            this._animSharkWin.node.setPosition(sharkPos);
            this._animSharkLose.node.setPosition(sharkPos);
            this._animSharkWin.node.active = false;
            this._animSharkLose.node.active = false;
        } while (false);
    }

    /**
     * 停止"timeline"动画
     * @param animNode
     */
    private _stopTimelineAnims(anim: cc.Animation): void {
        if (anim && anim instanceof cc.Animation && cc.isValid(anim, true)) {
            let vClips: cc.AnimationClip[] = anim.getClips();
            for (let clip of vClips) {
                anim.stop(clip.name);
            }

            anim.node.active = false;
        }
    }

    /**
     * 隐藏所有"timeline"动画
     */
    private _restAllTimelineAnims(): void {
        this._stopTimelineAnims(this._animRoundStart);
        this._stopTimelineAnims(this._animStartBet);
        this._stopTimelineAnims(this._animStopBet);
        this._stopTimelineAnims(this._animWayoutLight);

        this._hideAllOdds();
        this._hideAllWinFlagAnim();
        this._hideAllWinPlayerLightAnim();

        this._nodeAnim.stopAllActions();
    }

    /**
     * 初始化红包等相关按钮入口
     */
    private _initRedPackage(): void {
        // 红包节按钮
        this._btnRedpacketFestival = this._panelBetBtn.getChildByName('btn_redpacket_festival');
        this._btnRedpacketFestival.getComponent(cc.Sprite).spriteFrame = null;
        this._btnRedpacketFestival.active = false;

        // 红包节按钮提示层
        this._btnRedpacketFestivalLayer = cc.instantiate(this._btnRedpacketFestival);
        this.node.addChild(this._btnRedpacketFestivalLayer, PokerMasterDef.LayerZorder.Z_IDX_PANEL_RED_PACKET);

        let wpos: cc.Vec2 = cc.Vec2.ZERO;
        this._btnRedpacketFestival.convertToWorldSpaceAR(cc.Vec2.ZERO, wpos);
        this._btnRedpacketFestivalLayer.setPosition(this._btnRedpacketFestivalLayer.parent.convertToNodeSpaceAR(wpos));

        // 初始执行一次
        this._onMsgShowLuckButton(pf.client.RedPacketLotteryMode.Classical);
    }

    /**
     * 适配刘海屏相关控件
     */
    private _adaptiveScreen(): void {
        let offsetX: number = PokerMasterControl.gFullScreenOffset.x;
        let offsetY: number = PokerMasterControl.gFullScreenOffset.y;

        if (pf.system.view.isFullScreen()) {
            // 左右玩家列表
            // let panelLeftPlayerlist: cc.Node = this.node.getChildByName('panel_left_playerlist');
            // let panelRightPlayerlist: cc.Node = this.node.getChildByName('panel_right_playerlist');
            // if (panel_left_playerlist) {
            //     let tmp_x: number = offset_x - 25;
            //     panel_left_playerlist.setPosition(cc.v2(panel_left_playerlist.x + tmp_x, panel_left_playerlist.y));
            // }
            // if (panel_right_playerlist) {
            //     let tmp_x: number = offset_x - 25;
            //     panel_right_playerlist.setPosition(cc.v2(panel_right_playerlist.x - tmp_x, panel_right_playerlist.y));
            // }

            // 菜单按钮
            let btnMenu: cc.Node = this.node.getChildByName('btn_menu');
            if (btnMenu) {
                btnMenu.setPosition(cc.v2(this._panelLeftPlayerlist.x, btnMenu.y));
            }
            let rebateFloatingButton: cc.Node = this.node.getChildByName('rebate_float_button');
            if (rebateFloatingButton) {
                const space = btnMenu.width / 2 + rebateFloatingButton.width / 2;
                rebateFloatingButton.setPosition(cc.v2(btnMenu.x + space, rebateFloatingButton.y));
            }

            // 表格记录按钮
            if (this._btnReview) {
                this._btnReview.setPosition(cc.v2(this._panelRightPlayerlist.x, this._btnReview.y));
            }

            // 玩家自己面板
            if (this._panelSelf) {
                let imgHead: cc.Node = this._panelSelf.getChildByName('img_head');
                let pos: cc.Vec2 = cc.Vec2.ZERO;
                btnMenu.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);
                imgHead.parent.convertToNodeSpaceAR(pos, pos);
                let ox: number = pos.x - imgHead.x;
                let oy = 0;
                this._panelSelf.setPosition(cc.v2(this._panelSelf.x + ox, this._panelSelf.y + oy));
            }

            // 玩家列表按钮
            if (this._btnPlayerList) {
                let tmpX: number = offsetX - 50;
                this._btnPlayerList.setPosition(cc.v2(this._btnPlayerList.x - tmpX, this._btnPlayerList.y));
            }
        }
    }

    /**
     * 适配下注按钮面板布局(横向)
     */
    private _adaptiveBetBtnPanel(): void {
        // 若为空, 则填充按钮数组
        if (this._vBottomBetBtns.length <= 0) {
            // 下注按钮
            for (let button of this._vBetButtons) {
                this._vBottomBetBtns.push(
                    new MiniGameCommonDef.GameNodeScale(button.node, this.fBetBtnSrcScaleRate, true)
                );
            }

            // 续投按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btnBetAuto.node, this._btnBetAuto.node.scale, true)
            );

            // 清屏按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btnBetClean.node, this._btnBetClean.node.scale, true)
            );

            // 红包节按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btnRedpacketFestival, this._btnRedpacketFestival.scale)
            );
        }

        let w: number = this._btnPlayerList.x - this._btnPlayerList.width / 2;
        w -= this._panelSelf.x + this._panelSelf.width / 2;
        this._panelBetBtn.setContentSize(cc.size(w, this._panelBetBtn.height));
        this._panelBetBtn.setPosition(this._panelSelf.x + this._panelSelf.width / 2 + w / 2, this._panelBetBtn.y);

        let totalW = 0; // 所有可见子节点宽度和
        let spacingX = 0; // 子节点之间的间距
        let childrenCount = 0; // 可见的子节点个数

        for (let btn of this._vBottomBetBtns) {
            let node: cc.Node = btn.node;
            let scale: number = btn.scale;
            let isFixLayout: boolean = btn.isFixLayout;
            if (node.active || isFixLayout) {
                ++childrenCount;
                totalW += node.width * scale;
            }
        }

        spacingX = (this._panelBetBtn.width - totalW) / (childrenCount + 1);
        let startX: number = -this._panelBetBtn.width * this._panelBetBtn.anchorX;
        for (let btn of this._vBottomBetBtns) {
            let node: cc.Node = btn.node;
            let scale: number = btn.scale;
            let isFixLayout: boolean = btn.isFixLayout;
            if (node.active || isFixLayout) {
                let x = startX + spacingX + node.width * scale * node.anchorX;
                let pos: cc.Vec2 = cc.Vec2.ZERO;
                this._panelBetBtn.convertToWorldSpaceAR(cc.v2(x, 0), pos);
                node.parent.convertToNodeSpaceAR(pos, pos);

                node.setPosition(pos.x, node.y);
                startX = pos.x + node.width * scale * node.anchorX;
            }
        }

        // 适配红包节入口节点提示层
        if (this._btnRedpacketFestivalLayer) {
            let wpos: cc.Vec2 = cc.Vec2.ZERO;
            this._btnRedpacketFestival.convertToWorldSpaceAR(cc.Vec2.ZERO, wpos);
            this._btnRedpacketFestivalLayer.setPosition(
                this._btnRedpacketFestivalLayer.parent.convertToNodeSpaceAR(wpos)
            );
        }

        // 适配高级续投提示语位置
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.adaptAdvanceAutoTipsPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto.adaptAdvanceAutoCountPos(this._btnBetAuto.node);
        }
    }

    /**
     * 显示 win 动画
     * @param betOption
     */
    private _showWinFlagAnim(betOption: network.BetZoneOption): void {
        let anim: cc.Animation = this._mapAnimWinFlags.get(betOption);
        if (!anim) return;

        // 沿用旧的win动画
        anim.node.active = true;
        let animation0: cc.AnimationState = anim.getAnimationState('animation0');
        let animation1: cc.AnimationState = anim.getAnimationState('animation1');

        animation0.speed = MiniGameCommonDef.getAnimClipSpeedByDuring(animation0.clip, this._fActExecuteWinFlag);
        animation0.wrapMode = cc.WrapMode.Normal;

        anim.play(animation0.name);
        anim.on(
            cc.Animation.EventType.FINISHED,
            (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                anim.off(cc.Animation.EventType.FINISHED);
                animation1.wrapMode = cc.WrapMode.Loop;
                animation1.play();
            },
            this
        );
    }

    /**
     * 隐藏win动画
     * @param betOption
     */
    private _hideWinFlagAnim(betOption: network.BetZoneOption): void {
        let anim: cc.Animation = this._mapAnimWinFlags.get(betOption);
        this._stopTimelineAnims(anim);
    }

    /**
     * 显示所有区域win标记
     */
    private _showAllWinFlagAnim(): void {
        this._pokerMasterRoom.betZones.forEach((betZone: domain.BetZone, option: network.BetZoneOption): any => {
            let result: number = betZone.optionResult.result;
            if (result === 1) {
                this._showWinFlagAnim(option);
            }
        });
    }

    /**
     * 隐藏所有区域win标记
     */
    private _hideAllWinFlagAnim(): void {
        this._mapAnimWinFlags.forEach((anim, option): void => {
            this._hideWinFlagAnim(option);
        });
    }

    /**
     * 显示"顶栏"路单动画
     */
    private _showTopWayOutAnim(): void {
        let fileNameBall = '';
        let fileNameFire = '';
        let fileNameLight = '';
        let resultOption: network.BetZoneOption = this._pokerMasterRoom.roundInfo.roundResult.winOp;

        switch (resultOption) {
            case network.BetZoneOption.FISHER_WIN:
                fileNameBall = 'record_red';
                fileNameFire = 'record_red_fire';
                fileNameLight = 'record_red_fire_light';
                break;

            case network.BetZoneOption.SHARK_WIN:
                fileNameBall = 'record_blue';
                fileNameFire = 'record_blue_fire';
                fileNameLight = 'record_blue_fire_light';
                break;

            case network.BetZoneOption.EQUAL:
                fileNameBall = 'record_draw';
                fileNameFire = 'record_draw_fire';
                fileNameLight = 'record_draw_fire_light';
                break;

            default:
                break;
        }

        // 计算空闲路子索引
        let freeIndex: number = this._vTopWayOutImg.length;
        for (let i = 0; i < this._vTopWayOutImg.length; ++i) {
            if (!this._vTopWayOutImg[i].node.active) {
                freeIndex = i;
                break;
            }
        }

        // 赢得区域朝"顶栏"飞路子动画
        let nAreaIdx: number = this._getAreaIdxByBetOption(resultOption);
        if (nAreaIdx >= 0 && nAreaIdx < this._vAreasInfo.length) {
            let imgFire: cc.Sprite = new cc.Node().addComponent(cc.Sprite);
            let imgLight: cc.Sprite = new cc.Node().addComponent(cc.Sprite);
            this._nodeAnim.addChild(imgFire.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_0);
            this._nodeAnim.addChild(imgLight.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_0);

            imgFire.node.active = true;
            imgFire.spriteFrame = this._atlasPmPokermaster.getSpriteFrame(fileNameFire);

            imgLight.node.active = false;
            imgLight.spriteFrame = this._atlasPmPokermaster.getSpriteFrame(fileNameLight);

            // 计算旋转角度
            let startPos: cc.Vec2 = cc.Vec2.ZERO;
            this._vAreasInfo[nAreaIdx].panelCoin.convertToWorldSpaceAR(cc.Vec2.ZERO, startPos);
            let endPosIndex: number = Math.min(freeIndex, this._vTopWayOutImg.length - 1);
            let endPos: cc.Vec2 = cc.Vec2.ZERO;
            this._vTopWayOutImg[endPosIndex].node.convertToWorldSpaceAR(cc.Vec2.ZERO, endPos);

            let v1: cc.Vec2 = cc.v2(0, startPos.y);
            let v2: cc.Vec2 = cc.v2(endPos.x - startPos.x, endPos.y - startPos.y);
            let rotation: number = (v2.signAngle(v1) / Math.PI) * 180;
            imgFire.node.angle = -rotation;

            // light
            let lightPos: cc.Vec2 = cc.Vec2.ZERO;
            imgLight.node.parent.convertToNodeSpaceAR(endPos, lightPos);
            imgLight.node.setPosition(lightPos);

            // action
            imgFire.node.setAnchorPoint(cc.v2(0.5, 0.7));
            imgFire.node.parent.convertToNodeSpaceAR(startPos, startPos);
            imgFire.node.parent.convertToNodeSpaceAR(endPos, endPos);
            imgFire.node.setPosition(startPos);
            imgFire.node.runAction(
                cc.sequence(
                    cc.delayTime(0.2 * this._fActExecuteWayOut),
                    cc.moveTo(0.6 * this._fActExecuteWayOut, endPos).easing(cc.easeSineInOut()),
                    cc.callFunc((): void => {
                        imgFire.node.removeFromParent(true);
                        imgFire.node.destroy();
                        imgLight.node.active = true;
                        imgLight.node.runAction(
                            cc.sequence(cc.fadeOut(0.2), cc.fadeIn(0.2), cc.fadeOut(0.2), cc.destroySelf())
                        );
                    }, this)
                )
            );
        }

        this.showTrendChangeAnim();
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(1.5),
                cc.callFunc(() => {
                    this.hideTrendChangeAnim();
                }, this)
            )
        );

        // 路子满了挤动动画
        if (freeIndex > this._vTopWayOutImg.length - 1) {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.2 * this._fActExecuteWayOut),
                    cc.callFunc((): void => {
                        if (this._vTopWayOutImg.length > 0) {
                            let st: cc.ActionInterval = cc.scaleTo(0.2, 0);
                            let fo: cc.ActionInterval = cc.fadeOut(0.2);
                            let spawn: cc.FiniteTimeAction = cc.spawn(st, fo);
                            this._vTopWayOutImg[0].node.runAction(
                                cc.sequence(
                                    spawn,
                                    cc.callFunc((): void => {
                                        this._vTopWayOutImg[0].node.active = false;
                                        let tarPos: cc.Vec2 = cc.v2(cc.Vec2.ZERO);
                                        for (let i = 0; i < this._vTopWayOutImg.length; ++i) {
                                            if (i <= 0) continue;

                                            tarPos.x = this._vTopWayOutImgSrcPos[i - 1].x;
                                            tarPos.y = this._vTopWayOutImgSrcPos[i - 1].y;
                                            this._vTopWayOutImg[i].node.runAction(
                                                cc.sequence(
                                                    cc.moveTo(0.4 * this._fActExecuteWayOut, tarPos),
                                                    cc.callFunc((): void => {
                                                        if (i === this._vTopWayOutImg.length - 1) {
                                                            this._updateTopWayOut();
                                                            this._vTopWayOutImg[0].node.setScale(1.0);
                                                            this._vTopWayOutImg[0].node.opacity = 0xff;
                                                            this._vTopWayOutImg[0].node.active = true;
                                                        }
                                                    }, this)
                                                )
                                            );
                                        }
                                    })
                                )
                            );
                        }
                    }, this)
                )
            );
        } else {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.8 * this._fActExecuteWayOut),
                    cc.callFunc((): void => {
                        this._vTopWayOutImg[freeIndex].node.active = true;
                        this._vTopWayOutImg[freeIndex].spriteFrame =
                            this._atlasPmPokermaster.getSpriteFrame(fileNameBall);
                    }, this)
                )
            );
        }
    }

    /**
     * 更新"顶栏"路单
     * @param reduce 少显示个数
     */
    protected _updateTopWayOut(reduce: number = 0): void {
        let fileNameBall = '';

        let vLastResult: network.BetZoneOption[] = this._pokerMasterRoom.roundInfo.vLastResult;

        let minCount: number = Math.min(this._vTopWayOutImg.length, vLastResult.length);
        let startIndex = 0;
        let validCount = 0;

        // ui显示个数 >= 路子数据个数, 少显示 reduce 个
        if (this._vTopWayOutImg.length >= vLastResult.length) {
            startIndex = 0;
            validCount = minCount - reduce;
        }
        // ui显示个数 < 路子数据个数, 偏移 reduce 位数据显示
        else {
            startIndex = vLastResult.length - minCount - reduce;
            validCount = minCount;
        }

        for (let i = 0; i < this._vTopWayOutImg.length; ++i) {
            // 复原位置
            this._vTopWayOutImg[i].node.setPosition(this._vTopWayOutImgSrcPos[i]);

            // 更新路单精灵
            let index: number = startIndex + i;
            if (i < validCount && index >= 0 && index < vLastResult.length) {
                switch (vLastResult[index]) {
                    case network.BetZoneOption.FISHER_WIN:
                        fileNameBall = 'record_red';
                        break;
                    case network.BetZoneOption.SHARK_WIN:
                        fileNameBall = 'record_blue';
                        break;
                    case network.BetZoneOption.EQUAL:
                        fileNameBall = 'record_draw';
                        break;
                    default:
                        break;
                }
                this._vTopWayOutImg[i].node.active = true;
                this._vTopWayOutImg[i].spriteFrame = this._atlasPmPokermaster.getSpriteFrame(fileNameBall);
            } else {
                this._vTopWayOutImg[i].node.active = false;
            }
        }
    }

    /**
     * 路单滚动动画
     * @param betOption
     */
    private _showWayOutMoveAnim(betOption: network.BetZoneOption): void {
        let nAreaIdx = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let panelWayOut: cc.Node = this._vAreasInfo[nAreaIdx].panelWayOut;
        let wayOutImgArray: cc.Sprite[] = this._vAreasInfo[nAreaIdx].wayOutImgArray;
        let wayOutImgSrcPosArray: cc.Vec2[] = this._vAreasInfo[nAreaIdx].wayOutImgSrcPosArray;
        if (!panelWayOut || wayOutImgArray.length <= 0) return;

        // 裁剪右移 模式
        // let tarPos: cc.Vec2 = cc.v2(cc.Vec2.ZERO);
        // for (let i = 0; i < wayOutImgArray.length; ++i) {
        //     if (i === 0) {
        //         tarPos.x = wayOutImgSrcPosArray[i].x - wayOutImgArray[nAreaIdx].node.width * wayOutImgArray[nAreaIdx].node.scaleX;
        //         tarPos.y = wayOutImgSrcPosArray[i].y;
        //     }
        //     else {
        //         tarPos.x = wayOutImgSrcPosArray[i - 1].x;
        //         tarPos.y = wayOutImgSrcPosArray[i - 1].y;
        //     }

        //     wayOutImgArray[i].node.runAction(cc.sequence(cc.moveTo(0.3, tarPos), cc.callFunc((): void => {
        //         if (i === wayOutImgArray.length - 1) {
        //             this._updateWayOut(betOption, 0);
        //         }
        //     }, this)));
        // }

        // 缩小渐隐右移 模式
        let st: cc.ActionInterval = cc.scaleTo(0.2, 0);
        let fo: cc.ActionInterval = cc.fadeOut(0.3);
        let spawn: cc.FiniteTimeAction = cc.spawn(st, fo);
        wayOutImgArray[0].node.runAction(
            cc.sequence(
                spawn,
                cc.callFunc((): void => {
                    wayOutImgArray[0].node.active = false;
                    let tarPos: cc.Vec2 = cc.v2(cc.Vec2.ZERO);
                    for (let i = 0; i < wayOutImgArray.length; ++i) {
                        if (i === 0) continue;

                        tarPos.x = wayOutImgSrcPosArray[i - 1].x;
                        tarPos.y = wayOutImgSrcPosArray[i - 1].y;
                        wayOutImgArray[i].node.runAction(
                            cc.sequence(
                                cc.moveTo(0.5, tarPos),
                                cc.callFunc((): void => {
                                    if (i === wayOutImgArray.length - 1) {
                                        this._updateWayOut(betOption, 0);
                                        wayOutImgArray[0].node.setScale(1.0);
                                        wayOutImgArray[0].node.opacity = 0xff;
                                        wayOutImgArray[0].node.active = true;
                                    }
                                }, this)
                            )
                        );
                    }
                })
            )
        );
    }

    /**
     * 显示路单图片动画
     * @param betOption
     */
    private _showWayOutImgAnim(isLastOne: boolean, betOption: network.BetZoneOption): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let wayOutImgArray: cc.Sprite[] = this._vAreasInfo[nAreaIdx].wayOutImgArray;
        if (wayOutImgArray.length <= 0) return;

        let zoneData = this._pokerMasterRoom.betZones.get(betOption);
        if (!zoneData) return;

        // 隐藏路单文本
        let rtxtWayOut: cc.Label = this._vAreasInfo[nAreaIdx].rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 1 赢,  0 输
        let fileNameBall = '';
        let result: number = zoneData.optionResult.result;

        if (result === 1) {
            fileNameBall = 'humanboy_icon_circle_small_red'; // 'humanboy_icon_circle_red';
        } else {
            fileNameBall = 'humanboy_icon_circle_small_gray'; // 'humanboy_icon_circle_gray';
        }

        // 计算空闲路子索引
        let freeIndex: number = wayOutImgArray.length;
        for (let i = 0; i < wayOutImgArray.length; ++i) {
            if (!wayOutImgArray[i].node.active) {
                freeIndex = i;
                break;
            }
        }

        let wayOutMoveAnimCallBack = cc.callFunc(() => {
            if (isLastOne) {
                this.isWayOutAnimPlaying = false;
            }
        });

        // 路子满了挤动动画
        if (freeIndex > wayOutImgArray.length - 1) {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.3 * this._fActExecuteWayOut),
                    cc.callFunc(
                        (): void => {
                            this._showWayOutMoveAnim(betOption);
                        },
                        wayOutMoveAnimCallBack,
                        this
                    )
                )
            );
        } else {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.8 * this._fActExecuteWayOut),
                    cc.callFunc(
                        (): void => {
                            wayOutImgArray[freeIndex].node.active = true;
                            wayOutImgArray[freeIndex].spriteFrame = this._atlasHbLanguage.getSpriteFrame(fileNameBall);
                        },
                        wayOutMoveAnimCallBack,
                        this
                    )
                )
            );
        }
    }

    /**
     * 路单动画(包括图片,文本等)
     * @param betOption
     */
    private _showWayOutAnim(isLastOne: boolean, betOption: network.BetZoneOption): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let panelWayOut: cc.Node = this._vAreasInfo[nAreaIdx].panelWayOut;
        let wayOutImgArray: cc.Sprite[] = this._vAreasInfo[nAreaIdx].wayOutImgArray;
        if (!panelWayOut || wayOutImgArray.length <= 0) return;

        let zoneData = this._pokerMasterRoom.betZones.get(betOption);
        if (!zoneData) return;

        // 路子显示风格
        switch (this._vAreasInfo[nAreaIdx].wayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(betOption, 1);
                this._showWayOutImgAnim(isLastOne, betOption);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(betOption);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    let bShowTxt = false;
                    let vHistoryResults: number[] = zoneData.optionResult.historyResults;
                    if (vHistoryResults.length > 0 && vHistoryResults.length > wayOutImgArray.length) {
                        let bDefeat = true;
                        for (let i = 0; i <= wayOutImgArray.length; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] > 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(betOption);
                    } else {
                        this._updateWayOutImg(betOption, 1);
                        this._showWayOutImgAnim(isLastOne, betOption);
                    }
                }
                break;

            default:
                break;
        }
    }

    /**
     * 更新路单图片
     * @param betOption
     * @param reduce
     */
    private _updateWayOutImg(betOption: network.BetZoneOption, reduce: number): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let panelWayOut: cc.Node = this._vAreasInfo[nAreaIdx].panelWayOut;
        panelWayOut.active = true;

        let wayOutImgArray: cc.Sprite[] = this._vAreasInfo[nAreaIdx].wayOutImgArray;
        let wayOutImgSrcPosArray: cc.Vec2[] = this._vAreasInfo[nAreaIdx].wayOutImgSrcPosArray;

        let zoneData = this._pokerMasterRoom.betZones.get(betOption);
        if (!zoneData) return;

        // 隐藏路单文本
        let rtxtWayOut: cc.Label = this._vAreasInfo[nAreaIdx].rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 逆序取历史记录
        let fileNameBall = '';
        let vHistoryResults: number[] = zoneData.optionResult.historyResults;

        let minCount: number = Math.min(wayOutImgArray.length, vHistoryResults.length);
        let endIndex = 0;
        let endCount = 0;

        // ui显示个数 >= 路子数据个数, 少显示 reduce 个
        if (wayOutImgArray.length >= vHistoryResults.length) {
            endIndex = minCount - 1;
            endCount = minCount - reduce;
        }
        // ui显示个数 < 路子数据个数, 偏移 reduce 位数据显示
        else {
            endIndex = minCount - 1 + reduce;
            endCount = minCount;
        }

        for (let i = 0; i < wayOutImgArray.length; ++i) {
            // 复原位置
            wayOutImgArray[i].node.setPosition(wayOutImgSrcPosArray[i]);

            let index = endIndex - i;
            if (i < endCount && index >= 0 && index < vHistoryResults.length) {
                // 1 赢,  0 输
                let result: number = vHistoryResults[index];

                if (result === 1) {
                    fileNameBall = 'humanboy_icon_circle_small_red'; // 'humanboy_icon_circle_red';
                } else {
                    fileNameBall = 'humanboy_icon_circle_small_gray'; // 'humanboy_icon_circle_gray';
                }

                wayOutImgArray[i].node.active = true;
                wayOutImgArray[i].spriteFrame = this._atlasHbLanguage.getSpriteFrame(fileNameBall);
            } else {
                wayOutImgArray[i].node.active = false;
            }
        }
    }

    /**
     * 显示路单文本
     * @param betOption
     */
    private _updateWayOutTxt(betOption: network.BetZoneOption): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let rtxtWayOut: cc.Label = this._vAreasInfo[nAreaIdx].rtxtWayOut;
        if (!rtxtWayOut) return;

        let wayOutImgArray: cc.Sprite[] = this._vAreasInfo[nAreaIdx].wayOutImgArray;
        let wayOutLoseLimitCount: number = this._vAreasInfo[nAreaIdx].wayOutLoseLimitCount;

        // 隐藏路单球图片面板
        for (let sprite of wayOutImgArray) {
            sprite.node.active = false;
        }

        // 显示文本
        let eCurState: network.RoundState = this._pokerMasterRoom.gameState.roundState;
        let zoneData = this._pokerMasterRoom.betZones.get(betOption);
        if (!zoneData) return;

        // 连续多少手未出现(< 0 房间刚刚开始,不需要统计; > 0 多少手; = 0 上一手出现过)
        let luckLoseHand: number = zoneData.optionResult.luckLoseHand;
        let isZh = pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN;
        if (luckLoseHand < 0) {
            rtxtWayOut.string = '';
        } else if (luckLoseHand === 0) {
            if (eCurState === network.RoundState.WAIT_NEXT_ROUND) {
                // pf.StringUtil.setRichTextString(rtxtWayOut.node, pf.languageManager.getString('Humanboy_game_wayout_hit_txt'));
                rtxtWayOut.string = isZh ? 'b' : 'B';
            } else {
                // pf.StringUtil.setRichTextString(rtxtWayOut.node, pf.languageManager.getString('Humanboy_game_wayout_hit_last_txt'));
                rtxtWayOut.string = isZh ? 'c' : 'C';
            }
        } else {
            let strCountDest = '';
            if (wayOutLoseLimitCount !== 0 && luckLoseHand > wayOutLoseLimitCount) {
                strCountDest = pf.StringUtil.formatC('%d+', wayOutLoseLimitCount);
            } else {
                strCountDest = pf.StringUtil.formatC('%d', luckLoseHand);
            }
            rtxtWayOut.string = strCountDest + (isZh ? 'a' : 'A');
            // pf.StringUtil.setRichTextString(rtxtWayOut.node, pf.StringUtil.formatC(pf.languageManager.getString('Humanboy_game_wayout_lose_txt'), strCountDest));
        }
        rtxtWayOut.node.active = true;
    }

    /**
     *  更新路单
     * @param betOption
     * @param reduce
     */
    private _updateWayOut(betOption: network.BetZoneOption, reduce: number): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let wayOutImgArray: cc.Sprite[] = this._vAreasInfo[nAreaIdx].wayOutImgArray;
        let zoneData = this._pokerMasterRoom.betZones.get(betOption);
        if (!zoneData) return;

        switch (this._vAreasInfo[nAreaIdx].wayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(betOption, reduce);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(betOption);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    let bShowTxt = false;
                    let vHistoryResults: number[] = zoneData.optionResult.historyResults;
                    if (vHistoryResults.length > 0 && vHistoryResults.length > wayOutImgArray.length) {
                        let bDefeat = true;
                        for (let i = 0; i <= wayOutImgArray.length; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] > 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(betOption);
                    } else {
                        this._updateWayOutImg(betOption, reduce);
                    }
                }
                break;

            default:
                break;
        }
    }

    /**
     * 更新所有路单
     * @param reduce
     */
    protected _updateAllWayOut(reduce: number = 0): void {
        for (let areasInfo of this._vAreasInfo) {
            this._updateWayOut(areasInfo.zoneOption, reduce);
        }
    }

    /**
     * 显示所有路单动画
     */
    private _showAllWayOutAnim(): void {
        // if (this.isWayOutAnimPlaying) {
        //     return;
        // }

        // this.isWayOutAnimPlaying = true;

        for (let i = 0; i < this._vAreasInfo.length; ++i) {
            if (i === this._vAreasInfo.length - 1) {
                this._showWayOutAnim(true, this._vAreasInfo[i].zoneOption);
            } else {
                this._showWayOutAnim(false, this._vAreasInfo[i].zoneOption);
            }
        }

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._fActExecuteWayOut),
                cc.callFunc((): void => {
                    // 一局结束 请求路单
                    if (this._pokerMasterChart && this._pokerMasterChart.node.active) {
                        this._pokerMasterRoom.queryTrend(); // cv.pokerMasterNet.requestTrend();
                    }

                    // 动画
                    this._showWayOutLightAnim();
                }, this)
            )
        );
    }

    /**
     * 显示路单闪光动画
     */
    private _showWayOutLightAnim(): void {
        let pos = cc.Vec3.ZERO;
        this._btnReview.parent.convertToWorldSpaceAR(this._btnReview.position, pos);
        this._animWayoutLight.node.active = true;
        this._animWayoutLight.node.setPosition(this._animWayoutLight.node.parent.convertToNodeSpaceAR(pos));

        let anim: cc.Animation = this._animWayoutLight;
        let clip: cc.AnimationClip = anim.defaultClip;
        clip.speed = MiniGameCommonDef.getAnimClipSpeedByDuring(clip, this._fActExecuteWayOutLight);
        clip.wrapMode = cc.WrapMode.Normal;

        anim.play();
        anim.on(
            cc.Animation.EventType.FINISHED,
            (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                anim.off(cc.Animation.EventType.FINISHED);
                this._animWayoutLight.node.active = false;
            },
            this
        );
    }

    /**
     * 显示开局动画
     */
    private _showRoundStartAnim(): void {
        this._animRoundStart.node.active = true;

        let anim: cc.Animation = this._animRoundStart;
        let clip: cc.AnimationClip = anim.defaultClip;
        clip.speed = MiniGameCommonDef.getAnimClipSpeedByDuring(clip, this._fActExecuteRoundStart);
        clip.wrapMode = cc.WrapMode.Normal;

        anim.play();
        anim.on(
            cc.Animation.EventType.FINISHED,
            (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                anim.off(cc.Animation.EventType.FINISHED);
                this._animRoundStart.node.active = false;
            },
            this
        );
    }

    /**
     * 开始下注动画
     */
    private _showStartBetAnim(): void {
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._fActDelayedStartBet),
                cc.callFunc((): void => {
                    this._playSoundEffect(macros.Audio.Begin_Bet);
                    this._animStartBet.node.active = true;

                    let anim: cc.Animation = this._animStartBet;
                    let clip: cc.AnimationClip = anim.defaultClip;
                    clip.speed = MiniGameCommonDef.getAnimClipSpeedByDuring(clip, this._fActExecuteStartbet);
                    clip.wrapMode = cc.WrapMode.Normal;

                    anim.play();
                    anim.on(
                        cc.Animation.EventType.FINISHED,
                        (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                            anim.off(cc.Animation.EventType.FINISHED);
                            this._animStartBet.node.active = false;
                        },
                        this
                    );
                }, this)
            )
        );
    }

    /**
     * 停止下注动画
     */
    private _showStopBetAnim(): void {
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._fActDelayedStopBet),
                cc.callFunc((): void => {
                    this._playSoundEffect(macros.Audio.End_Bet);
                    this._animStopBet.node.active = true;

                    let anim: cc.Animation = this._animStopBet;
                    let clip: cc.AnimationClip = anim.defaultClip;
                    clip.speed = MiniGameCommonDef.getAnimClipSpeedByDuring(clip, this._fActExecuteStopBet);
                    clip.wrapMode = cc.WrapMode.Normal;

                    anim.play();
                    anim.on(
                        cc.Animation.EventType.FINISHED,
                        (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                            anim.off(cc.Animation.EventType.FINISHED);
                            this._animStopBet.node.active = false;
                        },
                        this
                    );
                }, this)
            )
        );
    }

    /**
     * 发牌动画
     */
    private _sendCardAnim(): void {
        this._setAllCardsFace(false);
        this._setAllCardsVisible(false);

        let handCards: PokerCardControl[] = [];
        let handCardsTargetPos: cc.Vec2[] = [];

        let TEMP_NUM = 15;
        for (let i = 0; i < this._vLeftHandCards.length; ++i) {
            let px: number = this._vLeftHandCardsSrcPos[i].x + TEMP_NUM;
            let py: number = this._vLeftHandCardsSrcPos[i].y - TEMP_NUM;
            this._vLeftHandCards[i].node.setPosition(px, py);
            handCards.push(this._vLeftHandCards[i]);
            handCardsTargetPos.push(this._vLeftHandCardsSrcPos[i]);
        }

        for (let i = 0; i < this._vRightHandCards.length; ++i) {
            let px: number = this._vRightHandCardsSrcPos[i].x - TEMP_NUM;
            let py: number = this._vRightHandCardsSrcPos[i].y - TEMP_NUM;
            this._vRightHandCards[i].node.setPosition(px, py);
            handCards.push(this._vRightHandCards[i]);
            handCardsTargetPos.push(this._vRightHandCardsSrcPos[i]);
        }

        for (let i = 0; i < this._vPublicHoleCards.length; ++i) {
            let px: number = this._vPublicHoleCardsSrcPos[i].x - TEMP_NUM;
            let py: number = this._vPublicHoleCardsSrcPos[i].y - TEMP_NUM;
            this._vPublicHoleCards[i].node.setPosition(px, py);
        }

        // 发手牌动画
        let easeRate = 1.0;
        let duration = 0.07;
        for (let i = 0; i < handCards.length; ++i) {
            let showAction: cc.ActionInstant = cc.callFunc((): void => {
                handCards[i].SetFace(false);
            });
            let moveAction: cc.ActionInterval = cc
                .moveTo(duration, handCardsTargetPos[i])
                .easing(cc.easeInOut(easeRate));
            this.scheduleOnce((elapsed: number): void => {
                handCards[i].node.active = true;
                handCards[i].node.runAction(
                    cc.sequence(
                        showAction,
                        moveAction,
                        cc.callFunc((): void => {
                            this._playSoundEffect(macros.Audio.Fapai);

                            // 发公共牌动画
                            if (i === handCards.length - 1) {
                                for (let j = 0; j < this._vPublicHoleCards.length; ++j) {
                                    let pubShowAction = cc.callFunc((): void => {
                                        this._vPublicHoleCards[j].SetFace(false);
                                        this._playSoundEffect(macros.Audio.Fapai);
                                    });

                                    let pubMoveAction = cc
                                        .moveTo(duration, this._vPublicHoleCardsSrcPos[j])
                                        .easing(cc.easeInOut(easeRate));
                                    let pubMoveComplete = cc.callFunc(() => {
                                        if (j === this._vPublicHoleCards.length - 1) {
                                            // 发完最后一张公共牌
                                            // 可以在这里回调
                                            // 目前采用并发动画, 此处无需回调
                                        }
                                    });

                                    this.scheduleOnce((elapsed: number): void => {
                                        this._vPublicHoleCards[j].node.active = true;
                                        this._vPublicHoleCards[j].node.runAction(
                                            cc.sequence(pubMoveAction, pubShowAction, pubMoveComplete)
                                        );
                                    }, j * duration);
                                }
                            }
                        })
                    )
                );
            }, i * duration);
        }
    }

    /**
     * 翻开"手牌"动画
     */
    private _showHandCardsAnim(): void {
        // // 显示所有牌
        this._setAllCardsVisible(true);

        // 翻手牌
        let minCount = 0;
        let vLeftHandCards = this._pokerMasterRoom.roundInfo.blueHandCards; // pokerMasterDataMgr.getPokerMasterRoom().vLeftHandCards;
        let vRightHandCards = this._pokerMasterRoom.roundInfo.redHandCards; // pokerMasterDataMgr.getPokerMasterRoom().vRightHandCards;

        minCount = Math.min(this._vLeftHandCards.length, vLeftHandCards.length);
        for (let i = 0; i < minCount; ++i) {
            this._vLeftHandCards[i].SetContent(vLeftHandCards[i].number, vLeftHandCards[i].suit);
            this._vLeftHandCards[i].SetFace(false);
            this._vLeftHandCards[i].Turn(true);

            if (i === 0) {
                this._playSoundEffect(macros.Audio.Kaipai);
            }
        }

        minCount = Math.min(this._vRightHandCards.length, vRightHandCards.length);
        for (let i = 0; i < minCount; ++i) {
            this._vRightHandCards[i].SetContent(vRightHandCards[i].number, vRightHandCards[i].suit);
            this._vRightHandCards[i].SetFace(false);
            this._vRightHandCards[i].Turn(true, 0.5);

            if (i === 0) {
                this.scheduleOnce(() => {
                    this._playSoundEffect(macros.Audio.Kaipai);
                }, 0.5);
            }
        }
    }

    /**
     * 翻开"公共牌"动画
     */
    private _showPublicHoleCardsAnim(showNums: number): void {
        let vPublicHoleCards = this._pokerMasterRoom.roundInfo.publicCards; // pokerMasterDataMgr.getPokerMasterRoom().vPublicHoleCards;
        let min: number = Math.min(showNums, vPublicHoleCards.length);
        for (let i = 0; i < this._vPublicHoleCards.length; ++i) {
            this._vPublicHoleCards[i].SetFace(false);

            if (i < min) {
                let flopOverFunc: cc.ActionInstant = cc.callFunc(() => {
                    if (i === min - 1) {
                        this._playSoundEffect(macros.Audio.Kaipai);
                    }
                });

                this._vPublicHoleCards[i].node.stopAllActions();
                this._vPublicHoleCards[i].node.runAction(
                    cc.sequence(
                        cc.moveTo(0.3, this._vPublicHoleCardsSrcPos[0]),
                        cc.callFunc((): void => {
                            this._vPublicHoleCards[i].SetContent(vPublicHoleCards[i].number, vPublicHoleCards[i].suit);
                            this._vPublicHoleCards[i].Turn(true);
                        }),
                        cc.moveTo(0.2, this._vPublicHoleCardsSrcPos[i]),
                        flopOverFunc
                    )
                );
            }
        }
    }

    /**
     * 更新"公共牌"高亮/置灰与否
     */
    private _updateWinCardGray(): void {
        let tRoundresult = this._pokerMasterRoom.roundInfo.roundResult;
        if (tRoundresult.Cards.length <= 0) return;

        // 先全部置灰
        for (let handCard of this._vLeftHandCards) {
            handCard.Gray(true);
        }
        for (let handCard of this._vRightHandCards) {
            handCard.Gray(true);
        }
        for (let handCard of this._vPublicHoleCards) {
            handCard.Gray(true);
        }

        // 然后选择性高亮
        for (let card of tRoundresult.Cards) {
            let suit: number = card.suit;
            let number: number = card.number;
            console.log('boob =>', suit, ',', number);

            // 左手牌
            for (let handCard of this._vLeftHandCards) {
                if (handCard.GetSuit() === suit && handCard.GetNumber() === number) {
                    handCard.Gray(false);
                }
            }

            // 右手牌
            for (let handCard of this._vRightHandCards) {
                if (handCard.GetSuit() === suit && handCard.GetNumber() === number) {
                    handCard.Gray(false);
                }
            }

            // 公共牌
            for (let handCard of this._vPublicHoleCards) {
                if (handCard.GetSuit() === suit && handCard.GetNumber() === number) {
                    handCard.Gray(false);
                }
            }
        }

        // // 先全部置灰
        // for (let i = 0; i < this._vLeftHandCards.length; ++i) {
        //     this._vLeftHandCards[i].Gray(true);
        // }
        // for (let i = 0; i < this._vRightHandCards.length; ++i) {
        //     this._vRightHandCards[i].Gray(true);
        // }
        // for (let i = 0; i < this._vPublicHoleCards.length; ++i) {
        //     this._vPublicHoleCards[i].Gray(true);
        // }

        // // 然后选择性高亮
        // for (let i = 0; i < tRoundresult.Cards.length; ++i) {
        //     let suit: number = tRoundresult.Cards[i].suit;
        //     let number: number = tRoundresult.Cards[i].number;
        //     console.log('boob =>', suit, ',', number);

        //     // 左手牌
        //     for (let l_idx = 0; l_idx < this._vLeftHandCards.length; ++l_idx) {
        //         if (this._vLeftHandCards[l_idx].GetSuit() === suit && this._vLeftHandCards[l_idx].GetNumber() === number) {
        //             this._vLeftHandCards[l_idx].Gray(false);
        //         }
        //     }

        //     // 右手牌
        //     for (let r_idx = 0; r_idx < this._vRightHandCards.length; ++r_idx) {
        //         if (this._vRightHandCards[r_idx].GetSuit() === suit && this._vRightHandCards[r_idx].GetNumber() === number) {
        //             this._vRightHandCards[r_idx].Gray(false);
        //         }
        //     }

        //     // 公共牌
        //     for (let p_idx = 0; p_idx < this._vPublicHoleCards.length; ++p_idx) {
        //         if (this._vPublicHoleCards[p_idx].GetSuit() === suit && this._vPublicHoleCards[p_idx].GetNumber() === number) {
        //             this._vPublicHoleCards[p_idx].Gray(false);
        //         }
        //     }
        // }
    }

    /**
     * 获取牌型对应的精灵帧名称
     * @param level
     * @param isGray
     */
    private _getCardTypeFrameName(level: network.HandLevel, isGray: boolean = false): string {
        let strCardType = '';

        // 成牌牌型
        switch (level) {
            // 无
            case network.HandLevel.HAND_DUMMY:
                break;

            // 高牌
            case network.HandLevel.HAND_NONE:
                strCardType = 'gaopai';
                break;

            // 一对
            case network.HandLevel.HAND_DUI:
                strCardType = 'yidui';
                break;

            // 两对
            case network.HandLevel.HAND_DUI_TWO:
                strCardType = 'liangdui';
                break;

            // 三条
            case network.HandLevel.HAND_SANJO:
                strCardType = 'santiao';
                break;

            // 顺子
            case network.HandLevel.HAND_SHUN:
                strCardType = 'shunzi';
                break;

            // 同花
            case network.HandLevel.HAND_TONG:
                strCardType = 'tonghua';
                break;

            // 葫芦
            case network.HandLevel.HAND_HULU:
                strCardType = 'hulu';
                break;

            // 金刚
            case network.HandLevel.HAND_SIJO:
                strCardType = 'jingang';
                break;

            // 同花顺
            case network.HandLevel.HAND_TONG_SHUN:
                strCardType = 'tonghuashun';
                break;

            // 皇家同花顺
            case network.HandLevel.HAND_KING:
                strCardType = 'huangtong';
                break;

            default:
                break;
        }

        strCardType = isGray ? strCardType + '_gray' : strCardType;

        return strCardType;
    }

    /**
     * 显示牌型动画
     * @param betOption
     */
    private _showCardTypeAnim(bAnim: boolean): void {
        let tRoundresult = this._pokerMasterRoom.roundInfo.roundResult;

        this._imgLeftCardTypeBg.node.active = true;
        this._imgLeftCardType.node.active = true;

        this._imgRightCardTypeBg.node.active = true;
        this._imgRightCardType.node.active = true;

        let leftBgFrame = '';
        let rightBgFrame = '';

        let lefTypeFrame = '';
        let righTyperFrame = '';

        switch (tRoundresult.winOp) {
            case network.BetZoneOption.FISHER_WIN:
                leftBgFrame = 'win_cardtype_bg';
                rightBgFrame = 'lose_cardtype_bg';

                lefTypeFrame = this._getCardTypeFrameName(tRoundresult.fisherLevel);
                righTyperFrame = this._getCardTypeFrameName(tRoundresult.sharkLevel, true);
                break;

            case network.BetZoneOption.SHARK_WIN:
                leftBgFrame = 'lose_cardtype_bg';
                rightBgFrame = 'win_cardtype_bg';

                lefTypeFrame = this._getCardTypeFrameName(tRoundresult.fisherLevel, true);
                righTyperFrame = this._getCardTypeFrameName(tRoundresult.sharkLevel);
                break;

            case network.BetZoneOption.EQUAL:
                leftBgFrame = 'win_cardtype_bg';
                rightBgFrame = 'win_cardtype_bg';

                lefTypeFrame = this._getCardTypeFrameName(tRoundresult.fisherLevel);
                righTyperFrame = this._getCardTypeFrameName(tRoundresult.sharkLevel);
                break;

            default:
                break;
        }

        this._imgLeftCardTypeBg.spriteFrame = this._atlasPmPokermaster.getSpriteFrame(leftBgFrame);
        this._imgLeftCardType.spriteFrame = this._atlasCbLanguage.getSpriteFrame(lefTypeFrame);

        this._imgRightCardTypeBg.spriteFrame = this._atlasPmPokermaster.getSpriteFrame(rightBgFrame);
        this._imgRightCardType.spriteFrame = this._atlasCbLanguage.getSpriteFrame(righTyperFrame);

        if (bAnim) {
            this._imgLeftCardTypeBg.node.scale = 0;
            this._imgLeftCardType.node.scale = 0;

            this._imgRightCardTypeBg.node.scale = 0;
            this._imgRightCardType.node.scale = 0;

            let st: cc.ActionInterval = cc.scaleTo(0.2, 1.0).easing(cc.easeInOut(1.0));
            this._imgLeftCardTypeBg.node.runAction(st);
            this._imgLeftCardType.node.runAction(st.clone());
            this._imgRightCardTypeBg.node.runAction(st.clone());
            this._imgRightCardType.node.runAction(st.clone());
        }
    }

    /**
     * 显示"角色"领先
     */
    private _showRoleLeadAnim(bAnim: boolean): void {
        // 谁领先 1 渔夫 -1 鲨鱼 0平局
        let uWhoIsLeader = this._pokerMasterRoom.whoIsLeader; // pokerMasterDataMgr.getPokerMasterRoom().uWhoIsLeader;
        this._imgLeftCardTypeBg.node.active = false;
        this._imgLeftCardType.node.active = false;

        this._imgRightCardTypeBg.node.active = false;
        this._imgRightCardType.node.active = false;

        // 左领先
        if (uWhoIsLeader === 1) {
            this._imgLeftCardTypeBg.node.active = true;
            this._imgLeftCardTypeBg.spriteFrame = this._atlasPmPokermaster.getSpriteFrame('win_cardtype_bg');

            this._imgLeftCardType.node.active = true;
            this._imgLeftCardType.spriteFrame = this._atlasCbLanguage.getSpriteFrame('role_lead');
        }
        // // 平
        // else if (uWhoIsLeader === 0) {

        // }
        // 右领先
        else if (uWhoIsLeader === -1) {
            this._imgRightCardTypeBg.node.active = true;
            this._imgRightCardTypeBg.spriteFrame = this._atlasPmPokermaster.getSpriteFrame('win_cardtype_bg');

            this._imgRightCardType.node.active = true;
            this._imgRightCardType.spriteFrame = this._atlasCbLanguage.getSpriteFrame('role_lead');
        }

        // action
        if (bAnim) {
            let vAnimImgNode: cc.Node[] = [];
            vAnimImgNode.push(this._imgLeftCardType.node);
            vAnimImgNode.push(this._imgLeftCardTypeBg.node);
            vAnimImgNode.push(this._imgRightCardType.node);
            vAnimImgNode.push(this._imgRightCardTypeBg.node);

            for (let node of vAnimImgNode) {
                if (!node.active) continue;
                node.setScale(0);
                node.runAction(cc.scaleTo(0.2, 1.0).easing(cc.easeInOut(1.0)));
            }
        }
    }

    /**
     * 显示"角色"运势
     */
    private _updateRoleFortune(): void {
        let nodeFortune: cc.Node = this._panelCard.getChildByName('node_fortune');

        // 横向偏移量(以左边为起点)
        let offsetW = 20;
        let getFortuneColor: (fortune: number) => cc.Color = (fortune: number): cc.Color => {
            let color: cc.Color = cc.Color.WHITE;
            if (fortune > 0) {
                color.setR(0xff);
                color.setG(0x40);
                color.setB(0x00);
            } else {
                color.setR(0xff);
                color.setG(0xff);
                color.setB(0xff);
            }
            return color;
        };

        // 左边"运势"
        do {
            let imgFortuneBg: cc.Node = nodeFortune.getChildByName('img_left_fortune_bg');
            let imgFortune: cc.Node = nodeFortune.getChildByName('img_left_fortune');
            let txtFortune: cc.Label = nodeFortune.getChildByName('txt_left_fortune').getComponent(cc.Label);

            let nFortune = MiniGameCommonDef.getNumberFixedDown(this._pokerMasterRoom.fLeftFortune);
            let strFortune: string = pf.Util.String(nFortune);
            if (strFortune !== txtFortune.string) {
                txtFortune.string = strFortune;
                txtFortune.node.color = getFortuneColor(nFortune);

                // 位置运算
                let szImg: cc.Size = imgFortune.getContentSize();
                let szTxt: cc.Size = pf.UIUtil.getLabelStringSize(txtFortune);

                let totalW: number = szImg.width + offsetW + szTxt.width;
                let startX: number =
                    imgFortuneBg.position.x -
                    imgFortuneBg.width * imgFortuneBg.anchorX +
                    (imgFortuneBg.width - totalW) / 2;
                let px: number = startX;
                let py: number = imgFortune.y;

                px += szImg.width * imgFortune.anchorX;
                imgFortune.setPosition(px, py);
                px += szImg.width * (1 - imgFortune.anchorX);
                px += offsetW;

                px += szTxt.width * txtFortune.node.anchorX;
                txtFortune.node.setPosition(px, py);
            }
        } while (false);

        // 右边"运势"
        do {
            let imgFortuneBg: cc.Node = nodeFortune.getChildByName('img_right_fortune_bg');
            let imgFortune: cc.Node = nodeFortune.getChildByName('img_right_fortune');
            let txtFortune: cc.Label = nodeFortune.getChildByName('txt_right_fortune').getComponent(cc.Label);

            let nFortune = MiniGameCommonDef.getNumberFixedDown(this._pokerMasterRoom.fRightFortune);
            let strFortune: string = pf.Util.String(nFortune);
            if (strFortune !== txtFortune.string) {
                txtFortune.string = strFortune;
                txtFortune.node.color = getFortuneColor(nFortune);

                // 位置运算
                let szImg: cc.Size = imgFortune.getContentSize();
                let szTxt: cc.Size = pf.UIUtil.getLabelStringSize(txtFortune);

                let totalW: number = szImg.width + offsetW + szTxt.width;
                let startX: number =
                    imgFortuneBg.position.x -
                    imgFortuneBg.width * imgFortuneBg.anchorX +
                    (imgFortuneBg.width - totalW) / 2;
                let px: number = startX;
                let py: number = imgFortune.y;

                px += szImg.width * imgFortune.anchorX;
                imgFortune.setPosition(px, py);
                px += szImg.width * (1 - imgFortune.anchorX);
                px += offsetW;

                px += szTxt.width * txtFortune.node.anchorX;
                txtFortune.node.setPosition(px, py);
            }
        } while (false);
    }

    /**
     * 重置牌型/领先
     */
    private _resetCardTypeOrRoleLead(): void {
        this._imgLeftCardTypeBg.node.setScale(1);
        this._imgLeftCardTypeBg.node.stopAllActions();
        this._imgLeftCardTypeBg.node.active = false;

        this._imgLeftCardType.node.setScale(1);
        this._imgLeftCardType.node.stopAllActions();
        this._imgLeftCardType.node.active = false;

        this._imgRightCardTypeBg.node.setScale(1);
        this._imgRightCardTypeBg.node.stopAllActions();
        this._imgRightCardTypeBg.node.active = false;

        this._imgRightCardType.node.setScale(1);
        this._imgRightCardType.node.stopAllActions();
        this._imgRightCardType.node.active = false;
    }

    /**
     * 显示"赔率"
     * @param betOption
     * @param bAnim
     */
    private _showOdds(betOption: network.BetZoneOption, bAnim: boolean): void {
        let cb: (idx: number, odds: string) => void = (idx: number, odds: string): void => {
            if (idx >= 0 && idx < this._vAreasInfo.length) {
                this._vAreasInfo[idx].txtOdds.node.active = true;
                this._vAreasInfo[idx].txtOdds.string = odds;
            }
        };

        let zoneData /* : PokerMasterZoneData */ = this._pokerMasterRoom.betZones.get(betOption);
        if (!zoneData) return;
        let odds: number = MiniGameCommonDef.getNumberFixedDown(zoneData.odds, 2);
        // let strOdds: string = `${odds}${pf.languageManager.getString('Humanboy_game_fnt_anim_odd')}`;
        let strOdds: string =
            pf.Util.String(odds) + (pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN ? ';' : '');

        let idx: number = this._getAreaIdxByBetOption(betOption);
        if (idx < 0 || idx >= this._vAreasInfo.length) return;

        if (bAnim) {
            let anim: cc.Animation = this._mapAnimShowOdds.get(betOption);
            if (anim) {
                anim.node.active = true;

                let txtOdds: cc.Label = anim.node.getChildByName('txt_odds').getComponent(cc.Label);
                txtOdds.string = strOdds;

                let clip: cc.AnimationClip = anim.defaultClip;
                clip.speed = MiniGameCommonDef.getAnimClipSpeedByDuring(clip, this._fActExecuteShowOdds);
                clip.wrapMode = cc.WrapMode.Normal;

                anim.play();
                anim.targetOff(this);
                anim.on(
                    cc.Animation.EventType.FINISHED,
                    (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                        anim.targetOff(this);
                        anim.node.active = false;
                        cb(idx, strOdds);
                    },
                    this
                );
            }
        } else {
            cb(idx, strOdds);
        }
    }

    /**
     * 隐藏'赔率'
     */
    private _hideOdds(betOption: network.BetZoneOption): void {
        let idx: number = this._getAreaIdxByBetOption(betOption);
        if (idx < 0 || idx >= this._vAreasInfo.length) return;

        this._vAreasInfo[idx].txtOdds.string = '';

        this._vAreasInfo[idx].txtOdds.node.active = false;

        let anim: cc.Animation = this._mapAnimShowOdds.get(betOption);
        this._stopTimelineAnims(anim);
    }

    /**
     * 显示所有区域"赔率"
     */
    private _showAllOdds(bAnim: boolean): void {
        for (let areaInfo of this._vAreasInfo) {
            this._showOdds(areaInfo.zoneOption, bAnim);
        }
    }

    /**
     * 隐藏所有区域"赔率"
     */
    private _hideAllOdds(): void {
        this._mapAnimShowOdds.forEach((anim: cc.Animation, option: network.BetZoneOption): void => {
            this._hideOdds(option);
        });
    }

    /**
     * 显示玩家胜利头像框光环动画
     * @param uid
     * @param fDelayTime
     */
    private _showWinPlayerLightAnim(uid: number, fDelayTime: number): void {
        let vPlayerHeads: cc.Node[] = this._getPlayerHeadNodesByUid(uid);
        if (vPlayerHeads.length <= 0) return;

        let callFunc: Function = (): void => {
            for (let playerHand of vPlayerHeads) {
                let head: cc.Node = playerHand;

                // 自己不显示光环
                if (head === this._imgSelfHead.node) continue;

                let winNode: cc.Node = cc.find('win_player_light', head);
                if (winNode && cc.isValid(winNode, true)) {
                    head.removeChild(winNode);
                    winNode.destroy();
                }

                let winPlayerLightAnim: cc.Node = cc.instantiate(
                    pf.addressableAssetManager.getAsset(macros.Assets.WIN_PLAYER_LIGHT)
                );
                head.addChild(winPlayerLightAnim);

                winPlayerLightAnim.active = true;
                winPlayerLightAnim.name = 'win_player_light';
                winPlayerLightAnim.setPosition(cc.Vec2.ZERO);
                winPlayerLightAnim.zIndex = 10;

                let winPlayerLightAction: cc.Animation = winPlayerLightAnim.getComponent(cc.Animation);
                winPlayerLightAction.defaultClip.wrapMode = cc.WrapMode.Loop;
                winPlayerLightAction.play();
            }
        };

        this._nodeAnim.runAction(cc.sequence(cc.delayTime(fDelayTime), cc.callFunc(callFunc, this)));
    }

    /**
     * 隐藏所有玩家胜利头像框光环动画
     */
    private _hideAllWinPlayerLightAnim(): void {
        for (let playerInfo of this._vOtherPlayerInfo) {
            let node: cc.Node = cc.find('win_player_light', playerInfo.nodeHead);
            if (node && cc.isValid(node, true)) {
                playerInfo.nodeHead.removeChild(node);
                node.destroy();
            }
        }
    }

    /**
     * 飞金币动画以金币精灵为起始点/终点
     * @param uid
     */
    private _getPlayerCoinNodesByUid(uid: number): cc.Node[] {
        let ret: cc.Node[] = [];

        // 富豪和神算子是自己时, 记录一次
        if (uid === this._authService.currentUser.userId) {
            ret.push(this._imgSelfGold.node);
        }

        // 左右侧玩家列表, 再次检测记录一次
        for (let playerInfo of this._vOtherPlayerInfo) {
            if (uid > 0 && playerInfo.uid === uid) {
                ret.push(playerInfo.nodeHead);
            }
        }

        return ret;
    }

    /**
     * 飞金币动画以头像精灵为起始点/终点
     * @param uid
     */
    private _getPlayerHeadNodesByUid(uid: number): cc.Node[] {
        let ret: cc.Node[] = [];

        // 富豪和神算子是自己时, 记录一次
        if (uid === this._authService.currentUser.userId) {
            ret.push(this._imgSelfHead.node);
        }

        // 左右侧玩家列表, 再次检测记录一次
        for (let playerInfo of this._vOtherPlayerInfo) {
            if (uid > 0 && playerInfo.uid === uid) {
                ret.push(playerInfo.nodeHead);
            }
        }

        return ret;
    }

    /**
     * 根据下注金额拆分多个筹码金额
     * @param gold
     */
    private _getBetDetailAmounts(gold: number): number[] {
        let vAmountLevels: number[] = this._pokerMasterRoom.roomParams.amountLevel;
        return MiniGameCommonDef.disinteBetAmounts(gold, vAmountLevels);
    }

    /**
     * 隐藏"赢/输"区域金币
     * @param bWinOrLose
     */
    private _hideAreaCoinsAnim(bWinOrLose: boolean): void {
        let resultOption: network.BetZoneOption = this._pokerMasterRoom.roundInfo.roundResult.winOp;
        for (let areasInfo of this._vAreasInfo) {
            let option: network.BetZoneOption = areasInfo.zoneOption;
            let deque: Deque<HumanboyBetCoinControl> = this._mapCoinQueue.get(option);
            let zoneData = this._pokerMasterRoom.betZones.get(option);
            if (!deque || !zoneData) return;

            let equalRes =
                (option === network.BetZoneOption.FISHER_WIN || option === network.BetZoneOption.SHARK_WIN) &&
                resultOption === network.BetZoneOption.EQUAL;
            if (!bWinOrLose && equalRes) continue;

            let result: number = zoneData.optionResult.result;
            if ((bWinOrLose && result === 1) || (!bWinOrLose && result === 0) || (bWinOrLose && equalRes)) {
                for (let idx = 0; idx < deque.size(); ++idx) {
                    let coin: HumanboyBetCoinControl = deque.at(idx);
                    if (!coin.node.active) continue;
                    let sequence: cc.ActionInterval = cc.sequence(
                        cc.fadeOut(this._fActDelayedFlyWinCoin),
                        cc.callFunc((): void => {
                            coin.node.active = false;
                        })
                    );
                    coin.node.runAction(sequence);
                }
            }
        }
    }

    /**
     * 从系统向击中区域吐金币
     * @param anim
     */
    private _showCoinToWinAreaFromSystem(anim: boolean = true): void {
        // let vSettles: domain.PlayerSettle[] = [];
        // this._pokerMasterRoom.roundInfo.playerSettles.forEach ((v, k) =>
        //      {
        //         vSettles.push(v);
        //      });
        // vSettles.push(this._pokerMasterRoom.roundInfo.otherPlayersSettle);
        this._pokerMasterRoom.roundInfo.getAllPlayerSettles().forEach((playerSettle) => {
            // for (let i = 0; i < vSettles.length; ++i) {
            let tSettle: Readonly<domain.PlayerSettle> = playerSettle; // vSettles[i];
            for (let settle of tSettle.settle) {
                let zoneDetail = settle;
                let winAmount: number = zoneDetail.winAmount;
                if (winAmount <= 0) continue;

                // 减去该区域已下注的金币
                winAmount -= zoneDetail.betAmount;

                let nAreaIdx: number = this._getAreaIdxByBetOption(zoneDetail.option);
                if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) continue;

                let panelCoin: cc.Node = this._vAreasInfo[nAreaIdx].panelCoin;
                let worldPos: cc.Vec2 = cc.Vec2.ZERO;
                panelCoin.convertToWorldSpaceAR(cc.Vec2.ZERO, worldPos);
                let vAmountlevel: number[] = this._getBetDetailAmounts(winAmount);
                for (let k = 0; k < vAmountlevel.length; ++k) {
                    let fDelayedTime: number = 0.2 + k * 0.02;
                    this._showCoinToWinAreaFromPos(worldPos, nAreaIdx, vAmountlevel[k], anim, fDelayedTime);
                }
            }
        });
    }

    /**
     * 从指定位置朝目标区域飞金币动画
     * @param worldPos
     * @param nAreaIdx
     * @param gold
     * @param anim
     * @param fDelayedTime
     * @param cb
     */
    private _showCoinToWinAreaFromPos(
        worldPos: cc.Vec2,
        nAreaIdx: number,
        gold: number,
        anim: boolean,
        fDelayedTime: number,
        cb: () => void = null
    ): void {
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let coin: HumanboyBetCoinControl = this._getCoinFromPool(nAreaIdx, gold);
        if (!coin) return;

        let coinFlyFromPos: cc.Vec2 = cc.Vec2.ZERO;
        coin.node.parent.convertToNodeSpaceAR(worldPos, coinFlyFromPos);
        let coinFlyDestPos: cc.Vec2 = this._getCoinRandomPos(coin.node, nAreaIdx, true);
        coin.node.parent.convertToNodeSpaceAR(coinFlyDestPos, coinFlyDestPos);
        if (anim) {
            coin.node.setPosition(coinFlyFromPos);
            this.scheduleOnce((elapsed: number): void => {
                coin.node.active = true;
                coin.node.runAction(
                    cc.sequence(
                        cc.moveTo(0.5, coinFlyDestPos).easing(cc.easeOut(0.8)),
                        cc.rotateBy(0.15, 180),
                        cc.rotateBy(0.15, 180),
                        cc.callFunc(cb, this)
                    )
                );
            }, fDelayedTime);
        } else {
            coin.node.active = true;
            coin.node.setPosition(coinFlyDestPos);
            if (cb) cb();
        }
    }

    /**
     * 从对应位置向玩家飞金币(公用接口)
     * @param uid
     * @param amount
     * @param fromNode
     * @param bRandomPos
     * @param func
     */
    private _showFlyCoinToPlayerAnim(
        uid: number,
        amount: number,
        fromNode: cc.Node,
        bRandomPos: boolean = true,
        func: () => void = null
    ): void {
        if (amount <= 0 || !fromNode) return;

        // 飞往的目标节点数
        let vPlayerCoinNodes: cc.Node[] = this._getPlayerCoinNodesByUid(uid);

        // 桌面没有该玩家
        if (vPlayerCoinNodes.length === 0) {
            console.log(
                `showFlyCoinToPlayerAnim - playerSettles uid: ${uid} not in gameplayers, use player list button`
            );
            vPlayerCoinNodes.push(this._btnPlayerList);
        }

        // 找出该玩家同时存在哪几个头像(一个玩家可以同时是富豪,神算子等)
        for (let i = 0; i < vPlayerCoinNodes.length; ++i) {
            // 自己是富豪/神算子， 只回收一次金币到自己头像
            if (uid === this._authService.currentUser.userId && i > 0) {
                continue;
            }

            // 飞金币动画
            let fromHead: cc.Node = vPlayerCoinNodes[i];
            let vAmountlevel: number[] = this._getBetDetailAmounts(amount);
            for (let j = 0; j < vAmountlevel.length; ++j) {
                let flyCoin: HumanboyBetCoinControl = this._createFlyCoin(vAmountlevel[j]);
                this._nodeAnim.addChild(flyCoin.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_0);
                this.flyCoinToPlayerArr.push(flyCoin.node);

                let offset: cc.Vec2 = cc.Vec2.ZERO;
                if (bRandomPos) {
                    offset.x =
                        pf.StringUtil.randomRange(fromNode.width * 0.3, fromNode.width * 0.7) -
                        fromNode.width * fromNode.anchorX;
                    offset.y =
                        pf.StringUtil.randomRange(fromNode.height * 0.3, fromNode.height * 0.7) -
                        fromNode.height * fromNode.anchorY;
                }

                let coinFlyFromPos: cc.Vec2 = cc.Vec2.ZERO;
                fromNode.convertToWorldSpaceAR(offset, coinFlyFromPos);

                let coinFlyDestPos: cc.Vec2 = cc.Vec2.ZERO;
                fromHead.convertToWorldSpaceAR(cc.Vec2.ZERO, coinFlyDestPos);

                flyCoin.node.parent.convertToNodeSpaceAR(coinFlyFromPos, coinFlyFromPos);
                flyCoin.node.parent.convertToNodeSpaceAR(coinFlyDestPos, coinFlyDestPos);
                flyCoin.node.setPosition(coinFlyFromPos);
                // flyCoin.node.active = false;

                // 开始飞金币
                this.scheduleOnce((elapsed: number) => {
                    if (pf.UIUtil.isValidNode(flyCoin)) {
                        flyCoin.node.active = true;
                        flyCoin.node.runAction(
                            cc.sequence(
                                cc.delayTime(0.2 + j * 0.02),
                                cc.moveTo(0.5, coinFlyDestPos).easing(cc.easeOut(0.8)),
                                cc.destroySelf()
                            )
                        );
                    } else {
                        console.error('PokerMasterControl - _showFlyCoinToPlayerAnim: flyCoin.node not exist');
                    }
                }, this._fActDelayedFlyWinCoin);
            }

            if (func && amount > 0) {
                // 头像弹性动画
                this._showHeadElasticAnim(fromHead, this._fActExecuteFlyWinCoin);

                // 赢的玩家头像光环
                this._showWinPlayerLightAnim(uid, this._fActExecuteFlyWinCoin);

                // 加金币动画
                this._showAddCoinAnim(fromHead, amount, this._fActExecuteFlyWinCoin, func);
            }
        }
    }

    /**
     * 加金币飘分动画(公用接口)
     * @param toNode
     * @param amount
     * @param fDelayTime
     * @param func
     */
    private _showAddCoinAnim(toNode: cc.Node, amount: number, fDelayTime: number, func: () => void = null): void {
        if (!toNode) return;

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(fDelayTime),
                cc.callFunc((): void => {
                    let strTotalWinAmount: string = pf.StringUtil.transNumberToString(amount, 2, true);
                    let flutterScore: HumanboyFlutterScoreControl = cc
                        .instantiate(
                            pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.HUMANBOY_FLUTTER_SCORE)
                        )
                        .getComponent(HumanboyFlutterScoreControl);
                    flutterScore.node.setScale(1.4);
                    this.node.addChild(flutterScore.node, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_2);
                    flutterScore.init(strTotalWinAmount);

                    let pos: cc.Vec2 = cc.Vec2.ZERO;
                    toNode.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);
                    flutterScore.node.parent.convertToNodeSpaceAR(pos, pos);

                    // 适配飘数字的位置
                    do {
                        let offset = 10;
                        let extraW: number = cc.winSize.width / 2 - Math.abs(pos.x);

                        let w: number = flutterScore.node.width * flutterScore.node.scaleX;
                        let h: number = flutterScore.node.height * flutterScore.node.scaleY;

                        // 自己
                        if (toNode === this._imgSelfGold.node) {
                            let tmpX = Math.max(0, w / 2 - extraW);
                            pos.x += tmpX;
                            pos.y += toNode.height / 2 + offset;
                            // pos.y += (toNode.height / 2 + h / 2 + offset);
                        }
                        // 其他玩家
                        else if (toNode === this._btnPlayerList) {
                            let tmpX = Math.max(0, w / 2 - extraW);
                            pos.x -= tmpX;
                            pos.y += toNode.height / 2 + offset;
                            // pos.y += (toNode.height / 2 + h / 2 + offset);
                        }
                        // 左列表
                        else if (pos.x < 0) {
                            pos.x += toNode.width / 2 + w / 2 + offset;
                        }
                        // 右列表
                        else if (pos.x > 0) {
                            pos.x -= toNode.width / 2 + w / 2 + offset;
                        }
                    } while (0);

                    flutterScore.node.setPosition(pos);
                    flutterScore.show();
                    this._nodeAnim.runAction(
                        cc.sequence(cc.delayTime(this._fActDelayedFlyWinCoin), cc.callFunc(func, this))
                    );
                }, this)
            )
        );
    }

    /**
     * 头像弹性动画(公用接口)
     * @param toNode
     * @param fDelayTime
     */
    private _showHeadElasticAnim(toNode: cc.Node, fDelayTime: number): void {
        if (!toNode) return;

        let scaleRatio: number = toNode.scale;
        toNode.runAction(
            cc.sequence(
                cc.delayTime(fDelayTime),
                cc.scaleTo(0.5, scaleRatio + 0.2).easing(cc.easeBackOut()),
                cc.scaleTo(0.2, scaleRatio - 0.2),
                cc.scaleTo(0.5, scaleRatio).easing(cc.easeBackOut())
            )
        );
    }

    /**
     * 显示所有赢区域金币回收动画
     */
    private _showAllAreaWinFlagsAndFlyCoinAnim(): void {
        // 隐藏赢的区域的下注金币
        this._hideAreaCoinsAnim(true);

        // 动画:在哪些选项赢了(增加除主界面8个人输赢外其它玩家列表的输赢)
        // let vSettles: pokermaster_proto.PlayerSettle[] = [];
        // for (let i = 0; i < pokerMasterDataMgr.getPokerMasterRoom().vPlayerSettles.length; ++i) {
        //     vSettles.push(pokerMasterDataMgr.getPokerMasterRoom().vPlayerSettles[i]);
        // }
        // vSettles.push(pokerMasterDataMgr.getPokerMasterRoom().tOtherPlayerSettle);
        this._pokerMasterRoom.roundInfo.getAllPlayerSettles().forEach((playerSettle) => {
            // for (let i = 0; i < vSettles.length; ++i) {
            let it: domain.PlayerSettle = playerSettle; // vSettles[i];
            let uid: number = it.uid;
            let totalWinAmount: number = it.totalWinAmount;

            for (let settle of it.settle) {
                let zoneSettleDetail = settle;
                let option: network.BetZoneOption = zoneSettleDetail.option;
                let nAreaIdx: number = this._getAreaIdxByBetOption(option);
                if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) continue;

                let amount = zoneSettleDetail.winAmount;
                if (amount > 0) {
                    let panelCoin: cc.Node = this._vAreasInfo[nAreaIdx].panelCoin;
                    this._showFlyCoinToPlayerAnim(uid, amount, panelCoin, true);
                }
            }

            // 额外飘分等其他动画
            if (totalWinAmount > 0) {
                let vPlayerCoinNodes: cc.Node[] = this._getPlayerCoinNodesByUid(uid);

                // 桌面没有该玩家
                if (vPlayerCoinNodes.length === 0) {
                    console.log('playerSettles uid: %d not in gameplayers, use player list button', uid);
                    vPlayerCoinNodes.push(this._btnPlayerList);
                }

                // 找出该玩家同时存在哪几个头像(一个玩家可以同时是富豪,神算子等)
                for (let idx = 0; idx < vPlayerCoinNodes.length; ++idx) {
                    // 自己是富豪/神算子, 只回收一次金币到自己头像
                    if (idx > 0 && uid === this._authService.currentUser.userId) {
                        continue;
                    }

                    let fromHead: cc.Node = vPlayerCoinNodes[idx];

                    // 头像弹性动画
                    this._showHeadElasticAnim(fromHead, this._fActExecuteFlyWinCoin);

                    // 赢的玩家头像光环
                    this._showWinPlayerLightAnim(uid, this._fActExecuteFlyWinCoin);

                    // 加金币动画
                    this._showAddCoinAnim(fromHead, totalWinAmount, this._fActExecuteFlyWinCoin);
                }
            }
        });
    }

    /**
     * 清除数据等(停止一些循环动画,音频之类的资源的状态等,否则直接切换场景会闪退)
     */
    private _clearData(): void {
        // 移除所有监听者
        this._removeObserver();

        // 停止所有音乐播放
        // this._stopSound('', true);

        // 停止背景音乐
        pf.audioManager.stopAll();

        // 重置UI状态等
        this._resetAllUI();

        // 清除房间数据结构等
        // pokerMasterDataMgr.getPokerMasterRoom().reset();

        // 清理所有网络消息注册
        // cv.pokerMasterNet.unregisterNetMsgs();

        // 清理命名空间实例引用
        PokerMasterDef.clear();
    }

    /**
     * 返回房间列表
     */
    private _backToRoomListScene(): void {
        // this.exitGame();
        this.tryLeaveRoom();
    }

    /**
     * 返回主场景
     * @param tips
     */
    private _backToMainScene(tips: string): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        context.backToMainTips = tips;
        // this.exitGame();
        this.tryLeaveRoom();
    }

    /**
     * 播放音效
     * @param fileName
     * @param loop
     */
    protected _playSoundEffect(fileName: string, loop: boolean = false): void {
        pf.audioManager.playSoundEffect(fileName, loop);
        // if (cv.tools.isSoundEffectOpen()) {
        //     if (!this._mapSounds.has(fileName)) {
        //         this._mapSounds.add(fileName, true);
        //     }
        //     cv.AudioMgr.playEffect(fileName, loop);
        // }
    }

    /**
     * 暂停音频
     * @param fileName
     * @param all
     */
    // private _pauseSound(fileName: string, all: boolean = false): void {
    //     if (all) {
    //         this._mapSounds.forEach((key: string, value: boolean, i?: number): any => {
    //             let audioID: number = cv.AudioMgr.getAudioID(key);
    //             cv.AudioMgr.pause(audioID);
    //         });
    //     }
    //     else {
    //         if (!this._mapSounds.has(fileName)) return;
    //         let audioID: number = cv.AudioMgr.getAudioID(fileName);
    //         cv.AudioMgr.pause(audioID);
    //     }
    // }

    /**
     * 恢复音频
     * @param fileName
     * @param all
     */
    // private _resumeSound(fileName: string, all: boolean = false): void {
    //     if (all) {
    //         this._mapSounds.forEach((key: string, value: boolean, i?: number): any => {
    //             let audioID: number = cv.AudioMgr.getAudioID(key);
    //             cv.AudioMgr.resume(audioID);
    //         });
    //     }
    //     else {
    //         if (!this._mapSounds.has(fileName)) return;
    //         let audioID: number = cv.AudioMgr.getAudioID(fileName);
    //         cv.AudioMgr.resume(audioID);
    //     }
    // }

    /**
     * 停止播放音频
     * @param fileName
     * @param all
     */
    // private _stopSound(fileName: string, all: boolean = false): void {
    //     if (all) {
    //         this._mapSounds.forEach((key: string, value: boolean, i?: number): any => {
    //             let audioID: number = cv.AudioMgr.getAudioID(key);
    //             cv.AudioMgr.stop(audioID);
    //         });
    //     }
    //     else {
    //         if (!this._mapSounds.has(fileName)) return;
    //         let audioID: number = cv.AudioMgr.getAudioID(fileName);
    //         cv.AudioMgr.stop(audioID);
    //     }
    // }

    /**
     * 重置下个状态的截止时间
     */
    private _resetLeftTime(): void {
        this._nLeftTime = this._pokerMasterRoom.gameState.leftSeconds;

        this._msNowTime = 0;
        this._msLastTime = 0;
    }

    /**
     * 获取下个状态的截止时间
     */
    private _getLeftTime(): number {
        return this._nLeftTime;
    }

    /**
     * 显/隐下注计倒计时闹钟动画
     * @param bShow
     * @param bAnim
     */
    private _showBetClockAction(bShow: boolean, bAnim: boolean = true): void {
        // 复原
        this._imgBetClock.active = true;
        this._imgBetClock.stopAllActions();
        this._imgBetClock.setPosition(this._imgBetClockSrcPos);

        let worldPos: cc.Vec2 = cc.v2(0, cc.winSize.height / 2 + this._imgBetClock.height / 2);
        this.node.convertToWorldSpaceAR(worldPos, worldPos);
        let nodePos: cc.Vec2 = cc.Vec2.ZERO;
        this._imgBetClock.parent.convertToNodeSpaceAR(worldPos, nodePos);
        let pos: cc.Vec2 = cc.v2(this._imgBetClockSrcPos.x, nodePos.y);

        let ftnClock: cc.Label = this._imgBetClock.getChildByName('txt').getComponent(cc.Label);
        ftnClock.string = '1';

        if (bShow) {
            if (bAnim) {
                this._imgBetClock.setPosition(pos);
                let mt: cc.ActionInterval = cc.moveTo(this._fActExecuteBetClock, this._imgBetClockSrcPos);
                let ebo: cc.ActionInterval = mt.easing(cc.easeBackOut());
                this._imgBetClock.runAction(ebo);
            } else {
                this._imgBetClock.setPosition(this._imgBetClockSrcPos);
            }
            ftnClock.string = pf.StringUtil.formatC('%lld', this._getLeftTime());
        } else {
            if (bAnim) {
                this._imgBetClock.setPosition(this._imgBetClockSrcPos);
                let mt: cc.ActionInterval = cc.moveTo(this._fActExecuteBetClock, pos);
                let ebi: cc.ActionInterval = mt.easing(cc.easeBackIn());
                this._imgBetClock.runAction(
                    cc.sequence(
                        ebi,
                        cc.callFunc((): void => {
                            this._imgBetClock.active = false;
                        })
                    )
                );
            } else {
                this._imgBetClock.setPosition(this._imgBetClockSrcPos);
                this._imgBetClock.active = false;
            }
        }
    }

    /**
     * 开始倒计时
     */
    private _updateTimeBetClock(): void {
        if (this._pokerMasterRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0) {
            this.schedule(this._onTimeBetClock, 1.0);
            this._showBetClockAction(true, true);
        } else {
            this.unschedule(this._onTimeBetClock);
            this._showBetClockAction(false, true);
        }
    }

    /**
     * 停止倒计时
     */
    private _stopTimeBetClock(bAnim: boolean = false): void {
        this.unschedule(this._onTimeBetClock);
        this._showBetClockAction(false, bAnim);
    }

    /**
     * 更新下注倒计时
     * @param f32Delta
     */
    private _onTimeBetClock(f32Delta: number): void {
        if (this._pokerMasterRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0) {
            this._playSoundEffect(macros.Audio.Time_Tick);
            this._showBetClockAction(true, false);
        } else {
            this.unschedule(this._onTimeBetClock);
            this._showBetClockAction(false, true);
        }
    }

    /**
     * 开始"等待下局"计时
     */
    private _startWaittingNextRound(): void {
        let nLeftTime: number = this._getLeftTime();
        let eCurState: network.RoundState = this._pokerMasterRoom.gameState.roundState;
        if (eCurState === network.RoundState.WAIT_NEXT_ROUND && nLeftTime >= 0) {
            this.unschedule(this._onTimeWaittingNextRound);
            this.schedule(this._onTimeWaittingNextRound, 1.0);
            this._updateCountDownClock(true, nLeftTime);
        }
    }

    /**
     * 停止"等待下局"计时
     */
    private _stopWaittingNextRound(): void {
        this.unschedule(this._onTimeWaittingNextRound);
        this._updateCountDownClock(false, 0);
    }

    /**
     * 更新"等待下局"计时
     */
    private _onTimeWaittingNextRound(): void {
        let nLeftTime: number = this._getLeftTime();
        if (this._pokerMasterRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND && nLeftTime > 0) {
            this._updateCountDownClock(true, nLeftTime);
        } else {
            this._stopWaittingNextRound();
        }
    }

    /**
     * 开始"下局即将开始"计时
     */
    private _startPrepareNextRound(): void {
        // 维护状态:非0代表系统即将维护
        if (this._pokerMasterRoom.roundInfo.stopWorld !== 0) {
            this._showGameToast(pf.languageManager.getString('Humanboy_server_will_stop_text'));
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(2.0),
                    cc.callFunc((): void => {
                        this._backToRoomListScene();
                    }, this)
                )
            );
        }
        // 下一局即将开始
        else {
            let nLeftTime: number = this._getLeftTime();
            let eCurState: network.RoundState = this._pokerMasterRoom.gameState.roundState;
            // if (eCurState === network.RoundState.READY_GAME && nLeftTime > 0) {
            //     // ????? 執行Room.emit? 看不出來要做啥
            //     // cv.MessageCenter.send("on_pokerMaster_willstart_notify");
            //     this.unschedule(this._onTimePrepareNextRound);
            //     this.schedule(this._onTimePrepareNextRound, 1.0);
            //     this._updateCountDownClock(true, nLeftTime);
            // }

            // 下一局即将开始
            this._onTimePrepareNextRound();
        }
    }

    /**
     * 停止"下局即将开始"计时
     */
    private _stopPrepareNextRound(): void {
        this.unschedule(this._onTimePrepareNextRound);
        this._updateCountDownClock(false, 0);
    }

    /**
     * 更新"下局即将开始"计时
     */
    private _onTimePrepareNextRound(): void {
        if (this._bWaitting) return;

        let nLeftTime: number = this._getLeftTime();
        if (this._pokerMasterRoom.gameState.roundState === network.RoundState.READY_GAME && nLeftTime > 0) {
            this.schedule(this._onTimeNextRoundCountDown, 1.0);
            this._updateCountDownClock(true, nLeftTime);
        } else {
            this.unschedule(this._onTimeNextRoundCountDown);
            this._updateCountDownClock(false, 0);
        }
    }

    /**
     * 更新开局倒计时
     * @param f32Delta
     */
    private _onTimeNextRoundCountDown(f32Delta: number): void {
        let eCurState: network.RoundState = this._pokerMasterRoom.gameState.roundState;
        let nLeftTime: number = this._getLeftTime();
        if (eCurState === network.RoundState.READY_GAME && nLeftTime > 0) {
            this._updateCountDownClock(true, nLeftTime);
        } else {
            this.unschedule(this._onTimeNextRoundCountDown);
            this._updateCountDownClock(false, 0);
        }
    }

    /**
     * 耐心等待下一局/下局即将开始
     * @param bShow
     * @param fDelta
     */
    private _updateCountDownClock(bShow: boolean, fDelta: number): void {
        this._imgCountDown.active = bShow;
        this._imgCountDown.setPosition(this._imgCountDownSrcPos);

        let txt: cc.Label = this._imgCountDown.getChildByName('txt').getComponent(cc.Label);
        if (bShow) {
            switch (this._pokerMasterRoom.gameState.roundState) {
                // 耐心等待下一局
                case network.RoundState.WAIT_NEXT_ROUND:
                    txt.string = pf.StringUtil.formatC(
                        pf.languageManager.getString('Humanboy_game_tips_wait_next_enter_text'),
                        fDelta
                    );
                    break;

                // 下局即将开始
                case network.RoundState.READY_GAME:
                    txt.string = pf.StringUtil.formatC(
                        pf.languageManager.getString('Humanboy_game_tips_wait_next_round_text'),
                        fDelta
                    );
                    break;

                default:
                    break;
            }
        } else {
            txt.string = '';
            this._imgCountDown.setPosition(this._imgCountDownSrcPos);
        }
    }

    /**
     * 动态游戏提示
     * @param strText           提示内容
     * @param nStrandedTime     滞留时间(默认0.5s)
     */
    private _showGameToast(strText: string, nStrandedTime: number = 0.5): void {
        let toast: HumanboyToastControl = cc
            .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.HUMANBOY_TOAST))
            .getComponent(HumanboyToastControl);
        toast.txt.string = strText;
        this.node.addChild(toast.node, PokerMasterDef.LayerZorder.Z_IDX_PANEL_SERVER_TOAST);

        let move: cc.ActionInterval = cc.moveBy(1.0, cc.v2(0, 120));
        let fade: cc.ActionInterval = cc.fadeOut(0.8).easing(cc.easeInOut(1.0));
        toast.node.runAction(cc.sequence(move, cc.delayTime(pf.Util.Number(nStrandedTime)), fade, cc.destroySelf()));
    }

    /** 显示投金币动画和头像抖动动画
     * @param nAreaIdx	对应区域索引
     * @param gold		金额
     * @param uid		玩家id
     * @param anim		是否动画(true:动画到目标位置, false: 直接显示到目标位置)
     * @param headAnim	是否显示头像抖动动画
     * @param playSound	是否播放音效
     * @param cb			回调函数
     */
    // eslint-disable-next-line max-params
    private _showCoinAnim(
        nAreaIdx: number,
        gold: number,
        uid: number,
        anim: boolean,
        headAnim: boolean,
        playSound: boolean,
        cb: () => void = null
    ): void {
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;
        let option: network.BetZoneOption = this._getBetOptionByAreaIdx(nAreaIdx);

        // 开始动画
        if (anim) {
            let vPlayerCoinNodes: cc.Node[] = this._getPlayerCoinNodesByUid(uid);
            if (pf.StringUtil.getArrayLength(vPlayerCoinNodes) === 0) {
                console.log(
                    `${PokerMasterControl.gClassName} showBetInAnim, cannot find valid headBg, use btnPlayerList, oneBet.uid: ${uid}`
                );
                vPlayerCoinNodes.push(this._btnPlayerList);
            }
            let coinFlyFromPos = cc.Vec3.ZERO;
            let coinFlyDestPos: cc.Vec2 = cc.Vec2.ZERO;
            for (let i = 0; i < vPlayerCoinNodes.length; ++i) {
                let fromHead: cc.Node = vPlayerCoinNodes[i];
                fromHead.parent.convertToWorldSpaceAR(fromHead.position, coinFlyFromPos);

                // 发射时头像抖动动画
                if (
                    headAnim &&
                    fromHead !== this._imgSelfGold.node &&
                    cc.director.getActionManager().getNumberOfRunningActionsInTarget(fromHead) <= 0
                ) {
                    // if (headAnim && fromHead != this._img_self_gold.node) {
                    let offsetX = 20;
                    let offsetY = 0;
                    let ac: cc.ActionInterval = null;
                    if (coinFlyFromPos.x < cc.winSize.width / 2) {
                        ac = cc.sequence(
                            cc.moveBy(0.1, cc.v2(-offsetX, 0)),
                            cc.moveBy(0.1, cc.v2(offsetX, offsetY)).easing(cc.easeInOut(1.0))
                        );
                    } else {
                        ac = cc.sequence(
                            cc.moveBy(0.1, cc.v2(offsetX, offsetY)),
                            cc.moveBy(0.1, cc.v2(-offsetX, offsetY)).easing(cc.easeInOut(1.0))
                        );
                    }
                    if (ac) {
                        fromHead.runAction(ac);
                    }
                }

                // 富豪和神算子是自己的情况，只下一个金币和播放一次音效
                if (uid === this._authService.currentUser.userId && i > 0) continue;

                // 飞金币动画方式
                do {
                    let coin: HumanboyBetCoinControl = this._getCoinFromPool(option, gold);
                    if (coin) {
                        coin.node.setPosition(coin.node.parent.convertToNodeSpaceAR(coinFlyFromPos));
                        if (i === 0) {
                            // 下注音效
                            if (playSound) {
                                let llRealGold: number = pf.StringUtil.clientGoldByServer(gold);
                                let sound: string =
                                    llRealGold < this._pokerMasterRoom.llCoinUICritical
                                        ? macros.Audio.Betin
                                        : macros.Audio.Betin_Many;

                                if (sound !== this._lastSoundName || Date.now() - this._lastSoundTime > 200) {
                                    this._lastSoundTime = Date.now();
                                    this._lastSoundName = sound;
                                    this._playSoundEffect(sound);
                                }
                            }

                            // 动画
                            coinFlyDestPos = this._getCoinRandomPos(coin.node, nAreaIdx, true);
                            coin.node.parent.convertToNodeSpaceAR(coinFlyDestPos, coinFlyDestPos);

                            coin.node.active = true;
                            coin.node.runAction(
                                cc.sequence(
                                    // cc.moveTo(0.3, coinFlyDestPos).easing(cc.easeSineOut()),
                                    cc.moveTo(0.3, coinFlyDestPos),
                                    cc.rotateBy(0.15, 180),
                                    cc.rotateBy(0.15, 180),
                                    cc.callFunc(cb, this)
                                )
                            );
                        } else {
                            // 动画
                            coin.node.active = true;
                            coin.node.runAction(
                                cc.sequence(
                                    // cc.moveTo(0.3, coinFlyDestPos).easing(cc.easeSineOut()),
                                    cc.moveTo(0.3, coinFlyDestPos),
                                    cc.rotateBy(0.15, 180),
                                    cc.rotateBy(0.15, 180),
                                    cc.callFunc((): void => {
                                        coin.node.active = false;
                                    })
                                )
                            );
                        }
                    }
                } while (0);
            }
        } else {
            let coin: HumanboyBetCoinControl = this._getCoinFromPool(option, gold);
            if (coin) {
                let coinFlyDestPos: cc.Vec2 = this._getCoinRandomPos(coin.node, nAreaIdx, true);
                coin.node.setPosition(coin.node.parent.convertToNodeSpaceAR(coinFlyDestPos));
                coin.node.active = true;
            }

            if (cb) cb();
        }
    }

    /**
     * 游戏开局动画: 开局(0 + 1.3) ===> 发牌(0 + 1.5) ===> 翻手牌(0 + 1.2) ===> 显示领先者(0 + 0.5)
     * @param step
     */
    private _gameStepsAnimStart(step: number = 0): void {
        let fDelayedTime = 0;

        switch (step) {
            // 开局动画
            case 0:
                fDelayedTime += this._fActDelayedRoundStart;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._showRoundStartAnim();
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteRoundStart;

            // 发牌动画
            // eslint-disable-next-line no-fallthrough
            case 1:
                fDelayedTime += this._fActDelayedSendCard;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._sendCardAnim();
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteSendCard;

            // 翻手牌动画
            // eslint-disable-next-line no-fallthrough
            case 2:
                fDelayedTime += this._fActDelayedShowHandCard;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._showHandCardsAnim();
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteShowHandCard;

            // break;
            // 显示领先者
            // case 3: {
            //     fDelayedTime += this._fActDelayed_ShowCardType;
            //     this._nodeAnim.runAction(cc.sequence(cc.delayTime(fDelayedTime), cc.callFunc((): void => {
            //         this._showRoleLeadAnim(true);
            //     }, this)));
            //     fDelayedTime += this._fActExecute_ShowCardType;
            // } break;
            // eslint-disable-next-line no-fallthrough
            default:
                break;
        }
    }

    /**
     * 游戏赔率动画: 赔率(0 + 1.0) ===> 开始下注(0 + 1.0)
     * @param step
     */
    private _gameStepsAnimOdds(step: number = 0): void {
        let fDelayedTime = 0;

        switch (step) {
            // 赔率动画
            case 0:
                fDelayedTime += this._fActDelayedShowOdds;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._showAllOdds(true);
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteShowOdds;

            // 开始下注动画
            // eslint-disable-next-line no-fallthrough
            case 1:
                fDelayedTime += this._fActDelayedStartBet;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._showStartBetAnim();
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteStartbet;

                break;

            default:
                break;
        }
    }

    /**
     * 停止下注: 停止下注(0.5 + 1) ===> 翻turn牌(0 + 0.5) ===> 显示领先(0 + 0.5) ===> 眯牌(0.5 + 5)
     * @param step
     */
    private _gameStepsAnimStopBet(step: number = 0): void {
        let fDelayedTime = 0;

        switch (step) {
            // 停止下注
            case 0:
                fDelayedTime += this._fActDelayedStopBet;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._showStopBetAnim();
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteStopBet;

            // 翻turn牌(前4张公共牌)
            // eslint-disable-next-line no-fallthrough
            case 1:
                fDelayedTime += this._fActDelayedShowTurnCard;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._showPublicHoleCardsAnim(4);
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteShowTurnCard;

            // 显示领先
            // eslint-disable-next-line no-fallthrough
            case 2:
                fDelayedTime += this._fActDelayedShowCardType;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this.showTempPaixing(
                                this._pokerMasterRoom.roundInfo.roundResult.fisherLevel,
                                this._pokerMasterRoom.roundInfo.roundResult.sharkLevel
                            );
                            this._showRoleLeadAnim(true);
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteShowCardType;

            // 眯牌
            // eslint-disable-next-line no-fallthrough
            case 3:
                {
                    let riverCard = this._pokerMasterRoom.roundInfo.publicCards[4];
                    this.preLoadCard(riverCard.number, riverCard.suit);
                    let leftTime: number =
                        this._getLeftTime() - this._fActDelayedShowRiverCard - this._fActExecuteShowRiverCard;
                    leftTime = Math.min(this._fActExecuteSquintCard, leftTime);
                    // 跳过眯牌
                    if (this._pokerMasterRoom.bSkipSquint) {
                        // fDelayedTime += this._fActDelayed_ShowRiverCard;
                        // this._nodeAnim.runAction(cc.sequence(cc.delayTime(fDelayedTime), cc.callFunc((): void => {
                        //     let river_idx: number = this._vPublicHoleCards.length - 1;
                        //     this._vPublicHoleCards[river_idx].SetContent(river_card.number, river_card.suit);
                        //     this._vPublicHoleCards[river_idx].Turn(true, this._fActExecute_ShowRiverCard - 0.3);
                        // }, this)));
                        // fDelayedTime += this._fActExecute_ShowRiverCard;
                        return;
                    }
                    if (leftTime >= 1) {
                        fDelayedTime += this._fActDelayedSquintCard;
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(fDelayedTime),
                                cc.callFunc((): void => {
                                    this.isSquintCard = true;

                                    if (this._humanboyMenu) {
                                        this._humanboyMenu.hide(false);
                                    }

                                    if (this._humanboyRule) {
                                        this._humanboyRule.onClose();
                                    }

                                    if (this._humanboySetting) {
                                        this._humanboySetting.close();
                                    }

                                    if (this._humanboyAdvancedSetting) {
                                        this._humanboyAdvancedSetting.hide();
                                    }

                                    if (this._humanboyAdvancedSetting) {
                                        this._humanboyAdvancedSetting.hide();
                                    }

                                    let PokerMasterDialog = this.node.getChildByName('PokerMaster_Dialog');
                                    if (PokerMasterDialog) {
                                        PokerMasterDialog.getComponent(HumanboyDialogControl).close();
                                    }

                                    let PokerMasterNodeExit = this.node.getChildByName('PokerMaster_nodeExit');
                                    if (PokerMasterNodeExit) {
                                        PokerMasterNodeExit.active = false;
                                    }

                                    if (this._pokerMasterChart) {
                                        this._pokerMasterChart.close();
                                    }

                                    if (this._humanboyPlayerList) {
                                        this._humanboyPlayerList.node.active = false;
                                    }

                                    if (this._pokerMasterReview) {
                                        this._pokerMasterReview.hide(false);
                                    }

                                    if (this._luckButton) {
                                        this._luckButton.hidePopup();
                                    }

                                    // 隐藏高级续投子面板
                                    if (this._humanboyAdvancedAuto) {
                                        this._humanboyAdvancedAuto.hideAdvanceAutoTips();
                                        // this._humanboyAdvancedAuto.hideAdvanceAutoCount();
                                        this._humanboyAdvancedAuto.hideSelectPanel(false);
                                    }

                                    let tipsNode = this.node.getChildByName('GAB_STYLE_ADVANCE_USING_tips');
                                    if (tipsNode) {
                                        tipsNode.getComponent(HumanboyDialogControl).close();
                                    }

                                    if (this._pokerMasterRoom.bCanSquint) {
                                        // ????? 沒得抄先拿掉
                                        // let tag = cv.TP.getTag();
                                        // if (tag != null) {
                                        //     if (tag === 'NoticeMTT_MatchBegin') {
                                        //         cv.TP.hideTipsPanel();
                                        //     }
                                        // }
                                    }

                                    let strNodeName = 'humanboy_dialog_recharge';
                                    let dialogNode: cc.Node = this.node.getChildByName(strNodeName);
                                    if (dialogNode) {
                                        dialogNode.removeFromParent(true);
                                        dialogNode.destroy();
                                    }
                                    if (this.advanceAutoAddBet) {
                                        this.advanceAutoAddBet
                                            .getComponent(MiniGameAdvancedAuto)
                                            .hideSelectPanel(false);
                                    }
                                    this._squintCard.show(
                                        this._pokerMasterRoom.bCanSquint,
                                        riverCard.number,
                                        riverCard.suit,
                                        leftTime,
                                        this._pokerMasterRoom.sharkOuts,
                                        this._pokerMasterRoom.dashiOuts,
                                        this.isIphoneXArea
                                    );
                                    this.GetDialogHub().processClose();
                                }, this)
                            )
                        );
                        fDelayedTime += leftTime;
                    }

                    // 重置眯牌信息
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime),
                            cc.callFunc((): void => {
                                this._resetSquintCardInfo();
                            }, this)
                        )
                    );
                }
                break;

            default:
                break;
        }
    }

    /**
     * 一手结束: 翻river牌(0.5 + 0.5) ===> 显示牌型(0 + 0.5) ===> 清除未击中区域金币(0 + 0.5) ===> win标记检测/路单(0 + 2) ===> 飞金币(3)
     * @param step
     */
    private _gameStepsAnimRoundEnd(step: number = 0): void {
        let fDelayedTime = 0;
        let riverCard = this._pokerMasterRoom.roundInfo.publicCards[4];

        switch (step) {
            // 翻river牌
            case 0: {
                // 翻river牌
                // fDelayedTime += this._fActDelayed_ShowRiverCard;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            let riverIdx: number = this._vPublicHoleCards.length - 1;
                            this._vPublicHoleCards[riverIdx].SetContent(riverCard.number, riverCard.suit);
                            this._vPublicHoleCards[riverIdx].Turn(true); // , this._fActExecute_ShowRiverCard - 0.3
                            // this.scheduleOnce(() => {
                            this._playSoundEffect(macros.Audio.Kaipai);
                            // }, this._fActExecute_ShowRiverCard - 0.3);
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteShowRiverCard;
            }

            // 显示牌型动画
            // eslint-disable-next-line no-fallthrough
            case 1: {
                fDelayedTime += this._fActDelayedShowCardType;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime + 0.4),
                        cc.callFunc((): void => {
                            this._updateWinCardGray();
                            this._showCardTypeAnim(true);
                            // }, this), cc.delayTime(0.3), cc.callFunc((): void => {
                            //     // this._updateWinCardGray();
                            //     this._showCardTypeAnim(true);
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecuteShowCardType;
            }

            // 清除未击中区域金币
            // eslint-disable-next-line no-fallthrough
            case 2: {
                fDelayedTime += 0;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._hideAreaCoinsAnim(false);
                            this.playResultAni();
                        }, this)
                    )
                );
                fDelayedTime += this._fActDelayedFlyWinCoin;
            }

            // win动画/路单
            // eslint-disable-next-line no-fallthrough
            case 3: {
                // win

                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            if (!this.showSpecialCardTypeAnim()) {
                                this._showAllWinFlagAnim();
                            }
                        }, this)
                    )
                );

                if (this.isResultSpecialCardType()) {
                    fDelayedTime += this._specialTypeTime;
                }

                // 路单
                fDelayedTime += this._fActDelayedShowWinFlag;

                // fDelayedTime += this._fActExecute_WinFlag;
            }

            // 飞金币
            // eslint-disable-next-line no-fallthrough
            case 4:
                {
                    let bWinRecover = false;
                    // let vSettles: pokermaster_proto.PlayerSettle[] = [];
                    // for (let i = 0; i < pokerMasterDataMgr.getPokerMasterRoom().vPlayerSettles.length; ++i) {
                    //     vSettles.push(pokerMasterDataMgr.getPokerMasterRoom().vPlayerSettles[i]);
                    // }
                    // vSettles.push(pokerMasterDataMgr.getPokerMasterRoom().tOtherPlayerSettle);

                    // 区域是否有钱回收
                    this._pokerMasterRoom.roundInfo.getAllPlayerSettles().forEach((playerSettle) => {
                        // for (let i = 0; i < vSettles.length; ++i) {
                        // let playerSettle: pokermaster_proto.PlayerSettle = vSettles[i];
                        if (playerSettle.totalWinAmount > 0) {
                            bWinRecover = true;
                            return; // break;
                        }
                    });

                    if (bWinRecover) {
                        // 从系统吐金币到对应击中区域
                        // fDelayedTime += this._fActDelayed_FlyWinCoin;
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(fDelayedTime),
                                cc.callFunc((): void => {
                                    this._showCoinToWinAreaFromSystem();
                                }, this)
                            )
                        );
                        fDelayedTime += this._fActExecuteFlyWinCoin;

                        // 从对应区域飞金币给玩家
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(fDelayedTime),
                                cc.callFunc((): void => {
                                    this._hideAllWinFlagAnim();
                                    this._playSoundEffect(macros.Audio.Get_win_coin);
                                    this._showAllAreaWinFlagsAndFlyCoinAnim();
                                }, this)
                            )
                        );
                    }

                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime),
                            cc.callFunc((): void => {
                                this._showTopWayOutAnim();
                                this._showAllWayOutAnim();
                            }, this)
                        )
                    );

                    if (bWinRecover) {
                        fDelayedTime += this._fActExecuteFlyWinCoin;
                    }
                    // 更新玩家金币
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime),
                            cc.callFunc((): void => {
                                // 更新玩家金币显示
                                this._updateAllPlayerGold();

                                // 更新所有玩家连胜状态
                                this._updateAllPlayerWinCount(true);

                                // 维护状态:非0代表系统即将维护
                                if (this._pokerMasterRoom.roundInfo.stopWorld !== 0) {
                                    let bTrue = this._pokerMasterRoom.roundInfo.idleRoomId > 0;
                                    if (!bTrue) {
                                        this._showGameToast(
                                            pf.languageManager.getString('Humanboy_server_will_stop_text')
                                        );
                                    }
                                    this._nodeAnim.runAction(
                                        cc.sequence(
                                            cc.delayTime(2.0),
                                            cc.callFunc((): void => {
                                                if (bTrue) {
                                                    this.showSwitchTable();
                                                } else {
                                                    this._backToRoomListScene();
                                                }
                                            }, this)
                                        )
                                    );
                                }
                            }, this)
                        )
                    );
                }
                break;

            default:
                break;
        }
    }

    /**
     * 清屏(3)
     */
    private _gameStepsAnimReady(): void {
        this._resetAllUI();
        this._updateAllWayOut();
        this._startPrepareNextRound();
    }

    /**
     * 显示路单面板
     */
    private _showChart(): void {
        if (!this._pokerMasterChart) {
            this._pokerMasterChart = cc.instantiate(this.prefabPmChart).getComponent(PokerMasterChartControl);
            this.node.addChild(this._pokerMasterChart.node, PokerMasterDef.LayerZorder.Z_IDX_PANEL_RECORD);
        } else {
            this._pokerMasterChart.node.active = true;
        }

        this._pokerMasterRoom.queryTrend(); // cv.pokerMasterNet.requestTrend();
    }

    /**
     * 显示投注回顾面板
     * @param option
     */
    private _showReview(): void {
        if (!this._pokerMasterReview) {
            this._pokerMasterReview = cc.instantiate(this.prefabPmReview).getComponent(PokerMasterReviewControl);
            this.node.addChild(this._pokerMasterReview.node, PokerMasterDef.LayerZorder.Z_IDX_PANEL_RECORD);
        }
        this._pokerMasterReview.show();
    }

    /**
     * 更新下注额级别
     */
    private _updateBetAmountLevel(): void {
        let vBetCoinOption: number[] = this._pokerMasterRoom.betSettings.betCoinOptions;
        for (let i = 0; i < vBetCoinOption.length; ++i) {
            if (i < this._nBetBtnNum) {
                let llAmountLevel: number = pf.StringUtil.clientGoldByServer(vBetCoinOption[i]);
                this._vBetButtons[i].setTxtNum(pf.StringUtil.numberToShowNumber(llAmountLevel));
                if (llAmountLevel < this._pokerMasterRoom.llCoinUICritical) {
                    this._vBetButtons[i].setShape(HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_COIN);
                } else {
                    this._vBetButtons[i].setShape(HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_BLOCK);
                }
            } else {
                console.error(
                    `${PokerMasterControl.gClassName} updateBetAmountLevel vBetCoinOption must be ${this._nBetBtnNum}, size: ${vBetCoinOption.length}`
                );
            }
        }

        switch (this._pokerMasterRoom.betSettings.autoBetLevel) {
            case pf.client.session.AutoBetLevel.Level_Normal:
                this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL);
                break;

            case pf.client.session.AutoBetLevel.Level_Advance:
                if (this._pokerMasterRoom.betSettings.selectAutoBetCount > 0) {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING);
                } else {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE);
                }
                break;

            default:
                break;
        }

        this._adaptiveBetBtnPanel();
    }

    /**
     * 获取当前选中的下注额
     */
    private _getCurBetLevel(): number {
        if (this._nCurBetBtnIndex < 0) return 0;
        let vBetCoinOption: number[] = this._pokerMasterRoom.betSettings.betCoinOptions;
        return vBetCoinOption[this._nCurBetBtnIndex];
    }

    /**
     * 通过下注选项获取下注区域索引
     * @param betOption
     */
    private _getAreaIdxByBetOption(betOption: network.BetZoneOption): number {
        for (let areasInfo of this._vAreasInfo) {
            if (areasInfo.zoneOption === betOption) {
                return areasInfo.zoneIndex;
            }
        }

        return -1;
    }

    /**
     * 通过下注区域索引获取下注选项
     * @param betOption
     */
    private _getBetOptionByAreaIdx(betIdx: number): network.BetZoneOption {
        for (let areasInfo of this._vAreasInfo) {
            if (areasInfo.zoneIndex === betIdx) {
                return areasInfo.zoneOption;
            }
        }

        // for (let i = 0; i < this._vAreasInfo.length; ++i) {
        //     if (this._vAreasInfo[i].zoneIndex === betIdx) {
        //         return this._vAreasInfo[i].zoneOption;
        //     }
        // }
        return network.BetZoneOption.BetZoneOption_DUMMY;
    }

    /**
     * 检测高级续投请求
     */
    private _checkAdvanceAutoReq(): void {
        if (
            this._pokerMasterRoom.gameState.roundState === network.RoundState.BET &&
            this._getAutoBetBtnStytle() === MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING
        ) {
            if (this._humanboyAdvancedAuto) {
                this._humanboyAdvancedAuto.hideAdvanceAutoTips();
            }

            if (
                this._pokerMasterRoom.betSettings.usedAutoBetCount <
                this._pokerMasterRoom.betSettings.selectAutoBetCount
            ) {
                // cv.pokerMasterNet.reqAdvanceAutoBet();
                this._pokerMasterRoom
                    .advanceAutoBet()
                    .then(() => {
                        this._onMsgAdvanceAutobet(network.ErrorCode.OK);
                    })
                    .catch((err: pf.ServerError) => {
                        this._onMsgAdvanceAutobet(err.errorCode);
                    });
            }
        }
    }

    /**
     * 点击金币区域下注
     * @param nAreaIdx
     */
    private _onClickAreaCoinPanel(nAreaIdx: number): void {
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        // 可以下注
        let eCurState: network.RoundState = this._pokerMasterRoom.gameState.roundState;
        if (eCurState === network.RoundState.BET && this._getLeftTime() > 0) {
            if (this._nCurBetBtnIndex < 0) {
                this._showGameToast(pf.languageManager.getString('Humanboy_not_select_betbtn'));
                return;
            } else {
                // if( !cv.C2CNotify.isAllowBet() ) return;
                this._pokerMasterRoom.bet(this._vAreasInfo[nAreaIdx].zoneOption, this._getCurBetLevel());
            }
        } else {
            console.log(
                `${PokerMasterControl.gClassName} click betArea, cannot bet, curState: ${
                    this._pokerMasterRoom.gameState.roundState
                }, left bet time: ${this._getLeftTime()}`
            );
            this._showGameToast(pf.languageManager.getString('PokerMaster_ServerErrorCode51009'));
        }
    }

    /**
     * 切换指定场景前回调(切出该场景)
     * @param scene
     */
    // private _onMsgSwitchSceneBegan(sceneName: string): void {
    //     console.log(pf.StringUtil.formatC("SwitchScene - From[%s] To [%s]", cv.Enum.SCENE.POKERMASTER_SCENE, sceneName));
    //     this._clearData();
    // }

    /**
     * 充值
     * @param event
     */
    protected recharge(): void {
        if (this._platform === 'pkw') {
            if (pf.system.isBrowser) {
                cr.commonResourceAgent.commonDialog.showMsg(
                    pf.languageManager.getString('UIOpenNewWindow'),
                    [pf.languageManager.getString('TipsPanel_sure_button')],
                    () => cr.commonResourceAgent.shop?.open()
                );
            } else {
                const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                context.isSelfRecharge = true;
                // this.exitGame();
                this.tryLeaveRoom();
            }
        } else if (this._platform === 'wpk') {
            cr.commonResourceAgent.commonDialog.showMsg(
                pf.languageManager.getString('PokerMaster_dialog_recharge'),
                [
                    pf.languageManager.getString('TipsPanel_sure_button'),
                    pf.languageManager.getString('TipsPanel_cancel_button')
                ],
                () => {
                    const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                    context.isSelfRecharge = true;
                    context.exitCallback(pf.client.ExitType.Standard);
                },
                () => {
                    // do nothing here
                }
            );
        }
    }

    /**
     * 设置声音改变
     */
    private _onMsgSoundSwitch(): void {
        pf.audioManager.enableMusic = pf.localStorage.getItem(macros.AudioSettingKeys.MUSIC) !== 'false';
        pf.audioManager.enalbeSoundEffect = pf.localStorage.getItem(macros.AudioSettingKeys.SOUND_EFFECT) !== 'false';

        pf.audioManager.playMusic(macros.Audio.BGM);
    }

    /**
     * 游戏内错误提示
     */
    private _onMsgGameError(param?: any): void {
        let code: number = pf.Util.Number(param);
        let strValue = `PokerMaster_ServerErrorCode${code}`;

        // 下注额小于最小下注额
        if (code === network.ErrorCode.BET_TOO_SMALL) {
            let formatCoin: number = pf.StringUtil.clientGoldByServer(this._pokerMasterRoom.roomParams.smallBet);
            this._showGameToast(
                pf.StringUtil.formatC(pf.languageManager.getString(strValue), pf.StringUtil.numberToString(formatCoin))
            );
        } else if (
            code === network.ErrorCode.CAN_NOT_LEAVE_IN_BETTING ||
            code === network.ErrorCode.CAN_NOT_LEAVE_IN_DEALER
        ) {
            cr.commonResourceAgent.toastMessage.showMsg(pf.languageManager.getString(strValue));
            // else if (code === network.ErrorCode.NO_BET) {

            // }
            const context = pf.app.getGameContext<pf.services.MiniGameContext>();
            context.isSelfRecharge = false;
        } else {
            this._showGameToast(pf.languageManager.getString(strValue));
        }
    }

    /**
     * 进入房间游戏数据同步
     */
    private _onMsgGameDataSyn(/* param?: any */): void {
        let gameStatus: network.RoundState = this._pokerMasterRoom.gameState.roundState;
        console.log(`${PokerMasterControl.gClassName} RoundState =  ${gameStatus}`);
        this._bSwitchTable = false;
        this.unscheduleAllCallbacks();
        if (this._nodeAnim) {
            this._nodeAnim.stopAllActions();
        }

        // 重置 UI
        this._resetAllUI();

        // 根据不同的游戏状态恢复游戏场景
        switch (gameStatus) {
            // 无
            case network.RoundState.RoundState_DUMMY:
                break;

            // 房间新建的，准备开局( do nothing )
            case network.RoundState.GAME_PENDING:
                break;

            // 新的一局
            case network.RoundState.NEW_ROUND:
                {
                    this._updateTopWayOut();
                    this._updateAllWayOut();

                    let leftTime: number = this._getLeftTime();
                    if (
                        leftTime >=
                        this._fActExecuteRoundStart +
                            this._fActExecuteSendCard +
                            this._fActDelayedSendCard +
                            this._fActExecuteSendCard +
                            this._fActDelayedShowHandCard +
                            this._fActExecuteShowHandCard +
                            this._fActDelayedShowCardType +
                            this._fActExecuteShowCardType
                    ) {
                        this._gameStepsAnimStart(0);
                    } else if (
                        leftTime >=
                        this._fActDelayedSendCard +
                            this._fActExecuteSendCard +
                            this._fActDelayedShowHandCard +
                            this._fActExecuteShowHandCard +
                            this._fActDelayedShowCardType +
                            this._fActExecuteShowCardType
                    ) {
                        this._gameStepsAnimStart(1);
                    } else if (
                        leftTime >=
                        this._fActDelayedShowHandCard +
                            this._fActExecuteShowHandCard +
                            this._fActDelayedShowCardType +
                            this._fActExecuteShowCardType
                    ) {
                        this._setAllCardsVisible(true);
                        this._gameStepsAnimStart(2);
                    }
                    // else if (leftTime >= this._fActDelayed_ShowCardType + this._fActExecute_ShowCardType) {
                    //     this._setAllCardsVisible(true);
                    //     this._gameStepsAnimStart(3);
                    // }
                    else {
                        this._setAllCardsVisible(true);
                        this._setAllHandsCardsFace(true);
                        this._showRoleLeadAnim(false);
                    }
                }
                break;

            // 显示赔率
            case network.RoundState.SHOW_ODDS:
                this._updateTopWayOut();
                this._updateAllWayOut();
                this._setAllCardsVisible(true);
                this._setAllHandsCardsFace(true);
                this._showRoleLeadAnim(false);

                if (
                    this._getLeftTime() >=
                    this._fActDelayedShowOdds +
                        this._fActExecuteShowOdds +
                        this._fActDelayedStartBet +
                        this._fActExecuteStartbet
                ) {
                    this._gameStepsAnimOdds(0);
                } else if (this._getLeftTime() >= this._fActDelayedStartBet + this._fActExecuteStartbet) {
                    this._showAllOdds(false);
                    this._gameStepsAnimOdds(1);
                } else {
                    this._showAllOdds(false);
                }
                break;

            // 开始下注
            case network.RoundState.BET:
                this._updateTopWayOut();
                this._updateAllWayOut();
                this._setAllCardsVisible(true);
                this._setAllHandsCardsFace(true);
                this._showRoleLeadAnim(false);

                this._showAllOdds(false);
                this._recoverAreasCoin(true);

                if (this._getLeftTime() >= this._fActDelayedStartBet + this._fActExecuteStartbet) {
                    this._showStartBetAnim();
                }

                this._onMsgGameStartBet(true);
                break;

            // 停止下注
            case network.RoundState.STOP_BET:
                {
                    this._updateTopWayOut();
                    this._updateAllWayOut();
                    this._setAllCardsVisible(true);
                    this._setAllHandsCardsFace(true);
                    this._showRoleLeadAnim(false);

                    this._showAllOdds(false);
                    this._recoverAreasCoin(true);

                    let leftTime: number = this._getLeftTime();
                    if (
                        leftTime >=
                        this._fActDelayedStopBet +
                            this._fActExecuteStopBet +
                            this._fActDelayedShowTurnCard +
                            this._fActExecuteShowTurnCard +
                            this._fActDelayedShowCardType +
                            this._fActExecuteShowCardType +
                            this._miPaiTime
                    ) {
                        this._gameStepsAnimStopBet(0);
                    } else if (
                        leftTime >=
                        this._fActDelayedShowTurnCard +
                            this._fActExecuteShowTurnCard +
                            this._fActDelayedShowCardType +
                            this._fActExecuteShowCardType +
                            this._miPaiTime
                    ) {
                        this._gameStepsAnimStopBet(1);
                    }
                    // else if (leftTime >= this._fActDelayed_ShowCardType + this._fActExecute_ShowCardType + this._miPai_time) {
                    //     this._gameStepsAnimStopBet(2);
                    // }
                    else {
                        this._gameStepsAnimStopBet(3);
                        this._setAllPublicHoleCardsFace(true, true);

                        this.showTempPaixing(
                            this._pokerMasterRoom.roundInfo.roundResult.fisherLevel,
                            this._pokerMasterRoom.roundInfo.roundResult.sharkLevel
                        );
                        // this._showRoleLeadAnim(false);
                    }
                }
                break;

            // 一局结算
            case network.RoundState.WAIT_NEXT_ROUND:
                {
                    this._setAllCardsVisible(true);
                    this._setAllCardsFace(true);
                    this._showCardTypeAnim(false);
                    this._updateWinCardGray();

                    this._showAllOdds(false);
                    let tempTime = this._getLeftTime();
                    if (tempTime > 5) {
                        if (!this.showSpecialCardTypeAnim(tempTime - 3)) {
                            this._showAllWinFlagAnim();
                        }
                    } else {
                        this._showAllWinFlagAnim();
                    }

                    // 路子动画
                    if (this._getLeftTime() >= this._fActExecuteWayOutLight) {
                        this._updateTopWayOut(1);
                        this._showTopWayOutAnim();

                        this._updateAllWayOut(1);
                        this._showAllWayOutAnim();
                    } else {
                        this._updateTopWayOut();
                        this._updateAllWayOut();
                    }

                    // 等待下局
                    this._startWaittingNextRound();
                }
                break;

            // 清屏
            case network.RoundState.READY_GAME:
                this._updateTopWayOut();
                this._updateAllWayOut();
                this._startPrepareNextRound();
                break;

            default:
                break;
        }
    }

    /**
     * 服务器踢人
     */
    private _onMsgKick(param: network.IKickNotify): void {
        if (param.idle_roomid > 0) {
            if (!this._bSwitchTable) {
                this._pokerMasterRoom.roundInfo.idleRoomId = param.idle_roomid;
                // this.showSwitchTable();
            }
            return;
        }
        let eKickType: number = pf.Util.Number(param.kickType);
        switch (eKickType) {
            case network.Kick.Kick_DUMMY:
                break;

            // 太长时间没下注
            case network.Kick.IDLE_LONG_TIME:
                {
                    let tips: string = pf.languageManager.getString('Humanboy_server_kick_long_time_text');
                    this._backToMainScene(tips);
                }
                break;

            // 停服踢人
            case network.Kick.Stop_World:
                {
                    let tips: string = pf.languageManager.getString('Humanboy_server_kick_stop_world_text');
                    this._backToMainScene(tips);
                }
                break;

            default:
                break;
        }
    }

    /**
     * 下注级别变更
     */
    private _onMsgBetAmountLevelChange(param?: any): void {
        this._updateBetAmountLevel();
        this._updateBetBtnState();
    }

    /**
     * 设置高级续投次数成功
     */
    private _onMsgAdvanceAutobetSet(param?: any): void {
        this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING);

        // 如果本局没有下注,且已勾选续投局数,则本局就生效一次
        if (!this._pokerMasterRoom.roundInfo.hasBetInCurRound && this._pokerMasterRoom.betSettings.canAutoBet) {
            this._checkAdvanceAutoReq();
        }
    }

    /**
     * 高级续投
     */
    private _onMsgAdvanceAutobet(param?: any): void {
        let code: number = pf.Util.Number(param);
        switch (code) {
            case network.ErrorCode.OK:
                break;

            // 高级续投超出限红
            case network.ErrorCode.AUTO_BET_EXCEED_LIMIT:
                if (this._humanboyAdvancedAuto) {
                    this._humanboyAdvancedAuto.adaptAdvanceAutoTipsPos(this._btnBetAuto.node);
                    this._humanboyAdvancedAuto.showAdvanceAutoTips(
                        pf.languageManager.getString(pf.StringUtil.formatC('PokerMaster_ServerErrorCode%d', code))
                    );
                }
                break;

            // 高级续投金额不足
            case network.ErrorCode.AUTO_BET_NO_MONEY:
                {
                    let strNodeName = 'humanboy_dialog_recharge';
                    let dialogNode: cc.Node = this.node.getChildByName(strNodeName);
                    if (!dialogNode) {
                        dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_DIALOG)
                        );
                        dialogNode.name = strNodeName;
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, PokerMasterDef.LayerZorder.Z_IDX_PANEL_SERVER_TOAST);

                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const stringContent = pf.languageManager.getString(
                            pf.StringUtil.formatC('PokerMaster_ServerErrorCode%d', code)
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_auto_cancel');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_auto_recharge');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            this._pokerMasterRoom.cancelAdavnceAutoBet();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            // cv.MessageCenter.send(PokerMasterDef.LocalMsg().MsgPrefix + PokerMasterDef.LocalMsg().RECHARGE);
                            this.recharge();
                        };

                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: false,
                            legacyDialog: legacyDialog,
                            isShowBtnCenter: false,
                            themeType: ThemeSystemType.TwoButton_NoMoney_Style
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                    }
                }
                break;

            default:
                this._pokerMasterRoom.emit('serverError', code);
                // cv.MessageCenter.send(PokerMasterDef.LocalMsg().MsgPrefix + PokerMasterDef.LocalMsg().ERROR, code);
                break;
        }

        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.adaptAdvanceAutoCountPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto.showAdvanceAutoCount();
        }
    }

    /**
     * 取消高级续投成功
     */
    private _onMsgAdvanceAutobetCancel(param?: any): void {
        this._updateBetAmountLevel();
        this._updateBetBtnState();
    }

    /**
     * 提示续投已达上限
     */
    private _onMsgAdvanceAutobetLimitReached(param?: any): void {
        const handAdded = param;
        if (handAdded) {
            cr.commonResourceAgent.toastMessage.showMsg(
                pf.StringUtil.formatC(pf.languageManager.getString('MiniGame_btn_desc_auto_bet_reached'), handAdded)
            );
        }
    }

    /**
     * 房间变更通知(目前只针对赔率)
     */
    private _onMsgRoomParamChange(param?: any): void {
        // this._updateBetOddsDetail();
    }

    /**
     * 新开一局
     */
    private _onMsgGameStatusDeal(): void {
        this._resetLeftTime();

        // 重置游戏视图
        // this._resetGameView();

        // 更新下注按钮和触摸状态
        this._updateBetBtnState();
        this._updateBetAreaTouchEnabled();

        // 更新自己信息
        this._updateSelfInfo();

        // 更新其他人信息
        this._updateOtherPlayersInfo();

        // 更新所有玩家连胜状态
        this._updateAllPlayerWinCount();

        // 开局动画
        this._gameStepsAnimStart();
    }

    /**
     * 显示赔率
     */
    private _onMsgGameStatusShowOdds(param?: any): void {
        this._resetLeftTime();
        this._gameStepsAnimOdds();
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.2),
                cc.callFunc((): void => {
                    this._showRoleLeadAnim(true);
                }, this)
            )
        );
    }

    /**
     * 开始 turn 轮下注
     */
    private _onMsgGameStartBet(isGameDataSyn?: boolean): void {
        let _isGameDataSyn = isGameDataSyn === true ? true : false;
        this._resetLeftTime();
        // this._updateTimeBetClock();
        // 更新下注倒计时
        this._updateTimeBetClock();

        // 更新区域触摸状态
        this._updateBetBtnState();
        this._updateBetAreaTouchEnabled();

        let autoReq =
            _isGameDataSyn === false || (_isGameDataSyn && this._pokerMasterRoom.betSettings.canAdvanceAutoBet);
        // 检测是否正在使用高级续投
        if (autoReq) {
            this._checkAdvanceAutoReq();
        }
    }

    /**
     * 停止 turn 轮下注
     * @param param
     */
    private _onMsgGameStatusStopBet(): void {
        this._resetLeftTime();
        this._stopTimeBetClock();

        this._updateBetBtnState(false);
        this._updateBetAreaTouchEnabled();

        this._gameStepsAnimStopBet();

        // 隐藏高级续投选择面板
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.hideSelectPanel(false);
        }
    }

    /**
     * 一局结束
     * @param param
     */
    private _onMsgGameStatusRoundEnd(param?: any): void {
        this.playPointAni();
        this.showMttBeginMsg();
        this.resetTempPaixing();
        this._resetLeftTime();
        this._gameStepsAnimRoundEnd();
    }

    /**
     * 清屏准备
     * @param param
     */
    private _onMsgGameStatusReady(param?: any): void {
        this._resetLeftTime();
        this._gameStepsAnimReady();
    }

    /**
     * 下注
     */
    private _onMsgBet(/* param: network.IBetNotify */ bet: PlayerOneBet): void {
        this._updateAutoBetBtnStatus();
        // 区域索引
        let nAreaIdx: number = this._getAreaIdxByBetOption(bet.betOption);

        // 常规下注阶段, 直接push金币队列
        let tCoinOptimization: HumanboyCoinOptimization = new HumanboyCoinOptimization();
        tCoinOptimization.nAreaIdx = nAreaIdx;
        tCoinOptimization.nGold = bet.betAmount;
        tCoinOptimization.nUid = bet.uid;
        tCoinOptimization.bAnim = true;
        tCoinOptimization.bHeadAnim = true;
        tCoinOptimization.bPlaySound = true;
        this._vCoinOptimizationDeque.push_back(tCoinOptimization);
    }

    /**
     * 请求续投成功
     */
    private _onMsgAutoBet(): void {
        this._updateBetBtnState();
    }

    private _onMsgMergeAutoBet(bets: PlayerOneBet[]): void {
        let betSize: number = bets.length;
        this._updateBetBtnState();

        for (let betNotify of bets) {
            let uid: number = betNotify.uid;
            let betAmount: number = betNotify.betAmount;
            let option /* : pokermaster_proto.BetZoneOption */ = betNotify.betOption;
            let areaIdx: number = this._getAreaIdxByBetOption(option);

            // 动态增加金币池(主要用于优化续投大量金币体验)
            do {
                let nFreeCoinCount: number = this._getFreeCoinCountFromPool(option);
                // if(betSize > this._nAreaCoinLimitCountMin)
                if (betSize > nFreeCoinCount) {
                    // let nDiffCount: number = betSize - this._nAreaCoinLimitCountMin;
                    let nDiffCount: number = betSize - nFreeCoinCount;
                    let nFinalCount: number = Math.min(
                        this._nAreaCoinLimitCountMin + nDiffCount,
                        this._nAreaCoinLimitCountMax
                    );
                    this._nAreaCoinLimitCountMin = nFinalCount;
                }
            } while (0);

            // 添加到金币队列, 按帧添加
            let tCoinOptimization: HumanboyCoinOptimization = new HumanboyCoinOptimization();
            tCoinOptimization.nAreaIdx = areaIdx;
            tCoinOptimization.nGold = betAmount;
            tCoinOptimization.nUid = uid;
            tCoinOptimization.bAnim = true;
            tCoinOptimization.bHeadAnim = true;
            tCoinOptimization.bPlaySound = true;
            this._vCoinOptimizationDeque.push_back(tCoinOptimization);
        }
    }

    /**
     * 合并续投动作结束
     */
    // private _onMsgMergeAutoBet(param: network.IMergeAutoBetNotify): void {

    //     let betSize: number = param.notify.length;
    //     this._updateBetBtnState();

    //     for (let betNotify of param.notify) {
    //         let uid: number = betNotify.uid;
    //         let betAmount: number = betNotify.detail.betAmount;
    //         let option/* : pokermaster_proto.BetZoneOption */ = betNotify.detail.option;
    //         let areaIdx: number = this._getAreaIdxByBetOption(option);

    //         // 动态增加金币池(主要用于优化续投大量金币体验)
    //         do {
    //             let nFreeCoinCount: number = this._getFreeCoinCountFromPool(option);
    //             // if(betSize > this._nAreaCoinLimitCountMin)
    //             if (betSize > nFreeCoinCount) {
    //                 // let nDiffCount: number = betSize - this._nAreaCoinLimitCountMin;
    //                 let nDiffCount: number = betSize - nFreeCoinCount;
    //                 let nFinalCount: number = Math.min(this._nAreaCoinLimitCountMin + nDiffCount, this._nAreaCoinLimitCountMax);
    //                 this._nAreaCoinLimitCountMin = nFinalCount;
    //             }
    //         } while (0);

    //         // 添加到金币队列, 按帧添加
    //         let tCoinOptimization: HumanboyCoinOptimization = new HumanboyCoinOptimization();
    //         tCoinOptimization.nAreaIdx = areaIdx;
    //         tCoinOptimization.nGold = betAmount;
    //         tCoinOptimization.nUid = uid;
    //         tCoinOptimization.bAnim = true;
    //         tCoinOptimization.bHeadAnim = true;
    //         tCoinOptimization.bPlaySound = true;
    //         this._vCoinOptimizationDeque.push_back(tCoinOptimization);
    //     }
    // }

    /**
     * 更新玩家列表
     */
    private _onMsgPlayerList(gamePlayers: pf.services.GamePlayer[], playerNum: number): void {
        if (!this._humanboyPlayerList) {
            // this._humanboyPlayerList = cc
            //     .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_PLAYER_LIST))
            //     .getComponent(MiniGamePlayerListControl);
            pf.addressableAssetManager
                .loadAsset(macros.Dynamic_Assets.MINI_GAME_PLAYER_LIST)
                .then((asset: cc.Prefab) => {
                    this._humanboyPlayerList = cc.instantiate(asset).getComponent(MiniGamePlayerListControl);
                    this.node.addChild(this._humanboyPlayerList.node, PokerMasterDef.LayerZorder.Z_IDX_PANEL_RECORD);
                    this._humanboyPlayerList.setPokerMasterData(gamePlayers, playerNum);
                    this._humanboyPlayerList.displayCell(0);
                });
        } else {
            this._humanboyPlayerList.node.active = true;
            this._humanboyPlayerList.setPokerMasterData(gamePlayers, playerNum);
            this._humanboyPlayerList.displayCell(-1);
        }
    }

    /**
     * 中奖,荣耀榜等提示
     */
    private _onMsgRewardTips(param?: any): void {
        if (!this._humanboyRewardTips) {
            let posX: number = (1 - this.node.anchorX) * this.node.width * this.node.scaleX;
            let posY: number = (1 - this.node.anchorY) * this.node.height * this.node.scaleY - 122;

            this._humanboyRewardTips = cc
                .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.HUMANBOY_REWARD_TIPS))
                .getComponent(HumanboyRewardTipsControl);
            this.node.addChild(this._humanboyRewardTips.node, PokerMasterDef.LayerZorder.Z_IDX_PANEL_REWRAD_TIP);
            this._humanboyRewardTips.node.setPosition(posX, posY);
        }

        let value: string = pf.Util.String(param);
        this._humanboyRewardTips.show(value, 4);
    }

    /**
     * world服金币有变动通知
     */
    private _onMsgUpdateWorldServerGold(param?: any): void {
        // world服接收接口已过滤只发自己, 因此这里无需再次判断(同时没有别的需求, 所以也不用缓存下发的结构)
        let llCurGold: number = this._walletService.getWallet().totalAmount;

        // 结算阶段跳过(否则会提前知道输赢结果)
        if (this._pokerMasterRoom.canUpdateWorldServerGold) {
            // 更新自己金币信息
            this._pokerMasterRoom.selfPlayer.curCoin = llCurGold;
            this._updatePlayerGold(this._authService.currentUser.userId);

            // 更新其他人信息(因为自己有可能会在8人列表中)
            let bOnMainPlayerList = false;
            let vOtherPlayerInfo: pf.services.GamePlayer[] = this._pokerMasterRoom.otherPlayers;
            for (let playerInfo of vOtherPlayerInfo) {
                if (this._authService.currentUser.userId === playerInfo.uid) {
                    bOnMainPlayerList = true;
                    playerInfo.curCoin = llCurGold;
                }
            }

            if (bOnMainPlayerList) {
                this._updateOtherPlayersInfo();
            }
        }
    }

    /**
     * 红包节开关通知
     */
    private _onMsgShowLuckButton(mode: number) {
        if (mode === pf.client.RedPacketLotteryMode.Diamond) {
            cc.log('[3in1] no diamond red packet in poker master');
            return;
        }

        if (!this._luckButton) {
            const luckButtonPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>(
                macros.Assets.LUCK_TURNTABLE_BUTTON
            );
            this._luckButton = cc.instantiate(luckButtonPrefab).getComponent(LuckTurntableButtonControl);
            this._luckButton.setLotteryMode(pf.client.RedPacketLotteryMode.Classical);
            this._btnRedpacketFestival.addChild(this._luckButton.node);
            this._luckButton.node.setPosition(0, 0);
            let pos: cc.Vec2 = cc.Vec2.ZERO;
            this._imgSelfGold.node.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);
            this._luckButton.setViewData(pos);
        }

        if (this._luckTurntableService.isShowLuckTurntable(mode)) {
            this._btnRedpacketFestival.active = true;
            this._luckButton.updateView(true, this._btnRedpacketFestivalLayer);
        } else {
            this._btnRedpacketFestival.active = false;
        }
        // "红包节"提示层是否显隐
        this._btnRedpacketFestivalLayer.active = this._btnRedpacketFestival.active;
        // "红包节"状态有变化, 适配底栏按钮位置
        this._adaptiveBetBtnPanel();
    }

    /**
     * 红包转盘中奖结果通知
     * @param id
     */
    private _onMsgTurntableResultNotice(userId: number, mode: number) {
        if (mode === pf.client.RedPacketLotteryMode.Diamond) {
            cc.log('[3in1] no diamond red packet in poker master');
            return;
        }

        let list: cc.Node[] = this._getPlayerCoinNodesByUid(userId);
        if (list.length <= 0) {
            list.push(this._btnPlayerList);
        }

        for (let node of list) {
            // let node: cc.Node = list[i];
            // let pos: cc.Vec2 = cc.Vec2.ZERO;
            // node.getParent().convertToWorldSpaceAR(node.getPosition(), pos);
            // this._luckButton.showGoldMoveAction(pos, param.currency_type);

            this._luckButton.runGoldMoveAction(this._btnRedpacketFestival, node);
        }
    }

    private _resetCardPos() {
        let len = this._vLeftHandCards.length;
        for (let i = 0; i < len; ++i) {
            this._vLeftHandCards[i].node.stopAllActions();
            this._vLeftHandCards[i].node.setPosition(this._vLeftHandCardsSrcPos[i]);
        }

        len = this._vRightHandCards.length;
        for (let i = 0; i < len; ++i) {
            this._vRightHandCards[i].node.stopAllActions();
            this._vRightHandCards[i].node.setPosition(this._vRightHandCardsSrcPos[i]);
        }

        len = this._vPublicHoleCards.length;
        for (let i = 0; i < len; ++i) {
            this._vPublicHoleCards[i].node.stopAllActions();
            this._vPublicHoleCards[i].node.setPosition(this._vPublicHoleCardsSrcPos[i]);
        }
    }

    showSpecialCardTypeAnim(playTime: number = 8.0): boolean {
        this.clearSpecialCardTypeAnim();
        if (!this.isResultSpecialCardType()) return false;
        let tRoundresult = this._pokerMasterRoom.roundInfo.roundResult;

        let resultOption: network.BetZoneOption = tRoundresult.winOp;

        let winLevel = 0;
        switch (resultOption) {
            case network.BetZoneOption.FISHER_WIN:
                winLevel = tRoundresult.fisherLevel;
                break;

            case network.BetZoneOption.SHARK_WIN:
                winLevel = tRoundresult.sharkLevel;
                break;

            case network.BetZoneOption.EQUAL:
                winLevel = tRoundresult.fisherLevel;
                break;

            default:
                break;
        }

        let specialCardType = '';
        let zoneData /* : PokerMasterZoneData */ = this._pokerMasterRoom.betZones.get(
            network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4
        );

        let odds: number = MiniGameCommonDef.getNumberFixedDown(zoneData.odds, 2);
        let specialCardOdd =
            pf.Util.String(odds) + (pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN ? ';' : '');
        if (winLevel === network.HandLevel.HAND_SIJO) {
            // 金刚
            specialCardType = 'special_jingang';
        } else if (winLevel === network.HandLevel.HAND_TONG_SHUN) {
            // 同花顺
            specialCardType = 'special_tonghuashun';
        } else if (winLevel === network.HandLevel.HAND_KING) {
            // 皇家同花顺
            specialCardType = 'special_huangtong';
        } else {
            console.log('showSpecialCardTypeAnim, show special cardtype anim error1');
            return false;
        }

        let winAnim: cc.Node = this._nodeAnim.getChildByName('special_card_type_anim');
        let winAction: cc.Animation = null;
        if (!winAnim) {
            // 创建动画
            winAnim = cc.instantiate(this.specialCardTypePrefab);
            this._nodeAnim.addChild(winAnim, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_1);
            winAnim.name = 'special_card_type_anim';
        }
        winAction = winAnim.getComponent(cc.Animation);
        winAnim.active = true;
        let atlas: cc.SpriteAtlas = pf.addressableAssetManager.getAsset(macros.Assets.SPECIAL_CARD_TYPE_ATLAS); // pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN ? this.specialCardTypePlist : this.enAnimationPlist;
        // cv.resMgr.loadSpriteTextureByPlist(atlas, (winAnim.getChildByName('special_card_type')).getComponent(cc.Sprite), specialCardType);
        winAnim.getChildByName('special_card_type').getComponent(cc.Sprite).spriteFrame =
            atlas.getSpriteFrame(specialCardType);
        winAnim.getChildByName('special_card_odd').getComponent(cc.Label).string = specialCardOdd;

        if (playTime > 7.8) {
            this._playSoundEffect(macros.Audio.Special_Card_Type_Big);
        }

        this.gotoFrameAndPlay(winAction, playTime);
        winAction.on('finished', (event: cc.Event): void => {
            winAction.off('finished');
            winAnim.active = false;
            this._showAllWinFlagAnim();
        });

        return true;
    }

    isResultSpecialCardType(): boolean {
        let tRoundresult = this._pokerMasterRoom.roundInfo.roundResult;
        if (
            tRoundresult.sharkLevel >= network.HandLevel.HAND_SIJO ||
            tRoundresult.fisherLevel >= network.HandLevel.HAND_SIJO
        ) {
            return true;
        }
        return false;
    }

    clearSpecialCardTypeAnim(): void {
        let specialCardTypeAnim = this._nodeAnim.getChildByName('special_card_type_anim');
        if (specialCardTypeAnim) {
            let winAction = specialCardTypeAnim.getComponent(cc.Animation);
            winAction.off('finished');
            winAction.stop();
            specialCardTypeAnim.active = false;
        }
    }

    gotoFrameAndPlay(ani: cc.Animation, playTime: number) {
        ani.play(ani.defaultClip.name, ani.defaultClip.duration - playTime);
    }

    initGuide(): void {
        let storeGuideKey = 'master_has_show_guide_2';
        if (pf.localStorage.getItem(storeGuideKey) !== 'true') {
            // let panelRecord = (this._panelTop.getChildByName('panelRecord'));
            // let len = panelRecord.childrenCount;
            // for (let i = 0; i < len; i++) {
            //     this._recordDotsTemp.push((panelRecord.getChildByName(pf.StringUtil.formatC('recordDot%d', i))).getComponent(cc.Sprite));
            // }

            if (!this._humanboyGuid) {
                this._humanboyGuid = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_GUIDE));
                this.node.addChild(this._humanboyGuid, PokerMasterDef.LayerZorder.Z_IDX_PANEL_GUID);
            }
            let guidLayer = this._humanboyGuid.getComponent(MiniGameGuideControl);
            guidLayer.setDescString(pf.languageManager.getString('Cowboy_ludan_guide_text'));

            guidLayer.show(
                this._panelTop,
                () => {
                    let hasShowGuide = 'true';
                    pf.localStorage.setItem(storeGuideKey, hasShowGuide);

                    this._showChart();
                    this._playSoundEffect(macros.Audio.Button);
                    // pf.StringUtil.clearArray(this._recordDotsTemp);
                },
                true
            );
        }
    }

    playResultAni(): void {
        // 0->平，1->大师胜，-1->shark胜
        this.resetResultAni();
        let resultOption: network.BetZoneOption = this._pokerMasterRoom.roundInfo.roundResult.winOp;
        let num = 0;
        switch (resultOption) {
            case network.BetZoneOption.FISHER_WIN:
                num = 1;
                break;

            case network.BetZoneOption.SHARK_WIN:
                num = -1;
                break;

            case network.BetZoneOption.EQUAL:
                num = 0;
                break;

            default:
                break;
        }
        if (num === 0) return;
        let nodeFisherman = cc.find('node_fisherman/img', this._panelCard);
        let nodeShark = cc.find('node_shark/img', this._panelCard);
        nodeShark.active = false;
        nodeFisherman.active = false;
        let arr: cc.Animation[] = null;
        if (num === 1) {
            arr = [this._animSharkLose, this._animDashiWin];
        } else if (num === -1) {
            arr = [this._animSharkWin, this._animDashiLose];
        } else {
            return;
        }

        let len = arr.length;

        for (let i = 0; i < len; i++) {
            arr[i].node.active = true;
            arr[i].play();
            arr[i].on('finished', (event: cc.Event): void => {
                this.resetResultAni();
            });
        }
    }

    resetResultAni() {
        let nodeFisherman = cc.find('node_fisherman/img', this._panelCard);
        let nodeShark = cc.find('node_shark/img', this._panelCard);
        nodeShark.active = true;
        nodeFisherman.active = true;
        let arr: cc.Animation[] = [this._animDashiWin, this._animDashiLose, this._animSharkWin, this._animSharkLose];

        for (let i = 0; i < 4; i++) {
            if (!arr[i]) continue;
            arr[i].stop();
            arr[i].off('finished');
            arr[i].node.active = false;
        }
    }

    setNodePosByIphoneX(): void {
        let setPosFunc = (node: cc.Node) => {
            let diff = 100;
            if (node.x > 0) {
                node.setPosition(cc.winSize.width * 0.5 - 1920 * 0.5 + node.x - diff, node.y);
            } else {
                node.setPosition(-cc.winSize.width * 0.5 + 1920 * 0.5 + node.x + diff, node.y);
            }
        };
        let panelCard = this.node.getChildByName('panel_card');
        let nodeFisherman = panelCard.getChildByName('node_fisherman');
        let nodeShark = panelCard.getChildByName('node_shark');
        let nodeFortune: cc.Node = panelCard.getChildByName('node_fortune');
        let imgLeftFortuneBg = nodeFortune.getChildByName('img_left_fortune_bg');
        let imgRightFortuneBg = nodeFortune.getChildByName('img_right_fortune_bg');
        let imgLeftFortune = nodeFortune.getChildByName('img_left_fortune');
        let imgRightFortune = nodeFortune.getChildByName('img_right_fortune');
        let txtLeftFortune = nodeFortune.getChildByName('txt_left_fortune');
        let txtRightFortune = nodeFortune.getChildByName('txt_right_fortune');

        setPosFunc(nodeFisherman);
        setPosFunc(nodeShark);
        setPosFunc(imgLeftFortuneBg);
        setPosFunc(imgRightFortuneBg);
        setPosFunc(imgLeftFortune);
        setPosFunc(imgRightFortune);
        setPosFunc(txtLeftFortune);
        setPosFunc(txtRightFortune);
    }

    showTempPaixing(fisherLevel: number, sharkLevel: number) {
        let tempPaixing = this.node.getChildByName('temp_paixing');
        if (
            fisherLevel <= network.HandLevel.HAND_DUMMY ||
            fisherLevel > network.HandLevel.HAND_KING ||
            sharkLevel <= network.HandLevel.HAND_DUMMY ||
            sharkLevel > network.HandLevel.HAND_KING
        ) {
            tempPaixing.active = false;
            return;
        }
        tempPaixing.active = true;
        let tempBgDashi = tempPaixing.getChildByName('temp_bg_dashi');
        let tempBgShark = tempPaixing.getChildByName('temp_bg_shark');
        let tempLabShark = tempPaixing.getChildByName('temp_lab_shark').getComponent(cc.Label);
        let tempLabDashi = tempPaixing.getChildByName('temp_lab_dashi').getComponent(cc.Label);
        let dsSize = pf.UIUtil.getLabelStringSize(
            tempLabDashi,
            pf.languageManager.getString(
                fisherLevel !== 8 ? `M_UITitle${fisherLevel + 112}` : 'Humanboy_game_card_type_four_of_a_kind'
            )
        );
        let skSize = pf.UIUtil.getLabelStringSize(
            tempLabShark,
            pf.languageManager.getString(
                sharkLevel !== 8 ? `M_UITitle${sharkLevel + 112}` : 'Humanboy_game_card_type_four_of_a_kind'
            )
        );

        let minWidth = 110;
        let diff = 24;
        let tempRes = minWidth - diff;
        tempBgDashi.setContentSize(dsSize.width <= tempRes ? minWidth : dsSize.width + diff, tempBgDashi.height);
        tempBgShark.setContentSize(skSize.width <= tempRes ? minWidth : skSize.width + diff, tempBgShark.height);
    }

    resetTempPaixing(): void {
        let tempPaixing = this.node.getChildByName('temp_paixing');
        if (tempPaixing) {
            tempPaixing.active = false;
        }
    }

    resetFlyCoinToPlayerArr() {
        for (let node of this.flyCoinToPlayerArr) {
            let tempNode: cc.Node = node;
            if (pf.UIUtil.isValidNode(tempNode)) {
                tempNode.removeFromParent();
                tempNode.destroy();
            }
        }

        this.flyCoinToPlayerArr = [];
    }

    initTrendChangeAnim(): void {
        // let frameVector: cc.SpriteFrame[] = [];
        // let frameVector: [cc.SpriteFrame] = [null];
        // for (let i = 0; i < 8; i++) {
        //     let frameName = pf.StringUtil.formatC('cowboy_trend_%d', i);
        //     let spriteFrame = this.cowboy_trend_anim_PLIST.getSpriteFrame(frameName);
        //     frameVector.push(spriteFrame);
        // }
        let atlas: cc.SpriteAtlas = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_TREND_ANIM_ATLAS);
        this.trendAnim = cc.AnimationClip.createWithSpriteFrames(atlas.getSpriteFrames(), 10);
        this.trendAnim.wrapMode = cc.WrapMode.Loop;
    }

    showTrendChangeAnim(): void {
        let imgRecord = this._panelTop.getChildByName('img_record');
        imgRecord.active = false;
        let trendAnim = this._panelTop.getChildByName('trend_anim');
        if (trendAnim) {
            trendAnim.removeFromParent(true);
            trendAnim.destroy();
        }
        let sprTrend = new cc.Node().addComponent(cc.Sprite);
        // cv.resMgr.loadSpriteTextureByPlist(this.cowboy_trend_anim_PLIST, sprTrend, 'cowboy_trend_0');
        let atlas: cc.SpriteAtlas = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_TREND_ANIM_ATLAS);
        sprTrend.spriteFrame = atlas.getSpriteFrame('cowboy_trend_0');
        sprTrend.node.name = 'trend_anim';
        sprTrend.node.setPosition(imgRecord.x - 1, imgRecord.y + 3);
        this._panelTop.addChild(sprTrend.node);
        let ani = sprTrend.node.addComponent(cc.Animation);
        // sprTrend.node.addChild(ani.node);
        ani.addClip(this.trendAnim, 'trend_anim');
        ani.play('trend_anim');
        // sprTrend.node.runAction(cc.repeatForever(ani.play()));
    }

    hideTrendChangeAnim(): void {
        let imgRecord = this._panelTop.getChildByName('img_record');
        imgRecord.active = true;
        let trendAnim = this._panelTop.getChildByName('trend_anim');
        if (trendAnim) {
            trendAnim.removeFromParent(true);
            trendAnim.destroy();
        }
    }

    /**
     * 游戏进入后台时触发的事件
     */
    OnAppEnterBackground(): void {
        pf.audioManager.stopAll();
        // // 私语版本, 切回后台后，将所有音频暂停
        // // if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() === cv.Enum.ClientType.H5WebPage) {
        // if (cc.sys.os === cc.sys.OS_ANDROID) {
        //     cv.AudioMgr.stopMusic();
        //     cv.AudioMgr.pauseAll();
        // } else {
        //     if (!cv.tools.isPlayMusic()) {
        //         cv.AudioMgr.play(this.silenceMusic, true, 0.5, true);
        //     }
        // }
        // cv.tools.setEnterbackState(true);

        this._isEnterBackground = true;

        // 解决结算飞金币时疯狂秒切前后台卡死的bug, 原因是依赖"this"的定时器回调后金币对象已被销毁
        // 停止根节点所有定时器和动画回调(暂时只能写在房间同步逻辑之前, 否则会造成音效循环播放bug)
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
    }

    /**
     * 游戏进入前台运行时触发的事件
     */
    OnAppEnterForeground(): void {
        pf.audioManager.playMusic(macros.Audio.BGM);
        // if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() === cv.Enum.ClientType.H5WebPage) {
        // cv.tools.setEnterbackState(false);
        // if (cc.sys.os === cc.sys.OS_ANDROID) {
        //     cv.AudioMgr.resumeAll();
        //     this._onMsgSoundSwitch();
        // } else {
        //     if (!cv.tools.isPlayMusic()) {
        //         cv.AudioMgr.stop(cv.AudioMgr.getAudioID(this.silenceMusic));
        //     }
        // }
        // }
        this._isEnterBackground = false;
    }

    NoticeMttMatchBegin(str: string) {
        this.mttbeginMsg = str;
        if (
            this._pokerMasterRoom.gameState.roundState === network.RoundState.STOP_BET &&
            !this._pokerMasterRoom.bSkipSquint &&
            this._pokerMasterRoom.bCanSquint
        ) {
            console.error(' ');
        } else {
            this.showMttBeginMsg();
        }
    }

    showMttBeginMsg() {
        // ????? 串了一半不曉得怎麼串完，先拿掉
        // if (!this.mttbeginMsg) return;
        // if (this.mttbeginMsg !== '') {
        //     if (cv.roomManager.mtt_time > 10) {
        //         cr.commonResourceAgent.commonDialog.showMsg(
        //             this.mttbeginMsg,
        //             [pf.languageManager.getString('TipsPanel_sure_button')],
        //             () => {
        //                 pf.app.emit('hideWebview');
        //             }
        //         );
        //         // cv.TP.showMsg(this.mttbeginMsg, cv.Enum.ButtonStyle.GOLD_BUTTON, () => {
        //         //     cv.MessageCenter.send('HideWebview_ShowWindows', true);
        //         // });
        //     }
        //     else {
        //         cr.commonResourceAgent.commonDialog.showMsg(
        //             this.mttbeginMsg,
        //             [pf.languageManager.getString('MiniGame_Switch_Table'), pf.languageManager.getString('MiniGame_Exit')],
        //             () => {
        //                 pf.app.emit('hideWebview');
        //                 // 原版的內容是空的乾脆不call
        //                 // cv.roomManager.closeScheduleForMTT();
        //                 cv.roomManager.isEnterMTT = true;
        //                 this.exitGame ();// cv.roomManager.RequestLeaveRoom();
        //             },
        //             () => {
        //                 pf.app.emit('hideWebview');
        //                 // 原版的內容是空的乾脆不call
        //                 // cv.roomManager.closeScheduleForMTT();
        //             }
        //         );
        //         // cv.TP.showMsg(this.mttbeginMsg, cv.Enum.ButtonStyle.TWO_BUTTON, () => {
        //         //     cv.MessageCenter.send('HideWebview_ShowWindows', true);
        //         //     cv.roomManager.closeScheduleForMTT();
        //         //     cv.roomManager.isEnterMTT = true;
        //         //     cv.roomManager.RequestLeaveRoom();
        //         // }, () => {
        //         //     cv.MessageCenter.send('HideWebview_ShowWindows', true);
        //         //     cv.roomManager.closeScheduleForMTT();
        //         // });
        //         cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_MTT_FRAME);
        //         // 原版的內容是空的乾脆不call
        //         // cv.roomManager.startScheduleForMTT();
        //     }
        //     cv.TP.setTag('NoticeMTT_MatchBegin');
        //     this.mttbeginMsg = '';
        // }
    }

    preLoadCard(cardNum: number, cardSuit: number) {
        // let frameName: string = this._getSquintCardFrameName(cardNum, cardSuit);
        // frameName = 'zh_CN/game/pokermaster/rubcard/' + frameName;
        // /* const texture: cc.Texture2D = */pf.addressableAssetManager.getAsset(frameName);
        // cv.resMgr.load(frameName, cc.Texture2D, (res: cc.Texture2D): void => { }, cv.resMgr.CleanResLevel.LEVEL_SCENE);
        pf.addressableAssetManager.getAsset(macros.Assets.POKER_MASTER_CARD_BACK_SPRITE);
        // cv.resMgr.load('zh_CN/game/pokermaster/rubcard/card_back', cc.Texture2D, (res: cc.Texture2D): void => { }, cv.resMgr.CleanResLevel.LEVEL_SCENE);
    }

    private _getSquintCardFrameName(cardNumber: number, cardSuit: number): string {
        let suit = '';
        switch (cardSuit) {
            case CardSuit.CARD_SPADE:
                suit = 'Bhm_';
                break;
            case CardSuit.CARD_HEART:
                suit = 'Rhm_';
                break;
            case CardSuit.CARD_CLUB:
                suit = 'Bcm_';
                break;
            case CardSuit.CARD_DIAMOND:
                suit = 'Rbm_';
                break;
            default:
                suit = 'Bhm_';
                break;
        }
        return `${suit}${cardNumber + 1}`;
    }

    playPointAni() {
        let pointsNum = this._pokerMasterRoom.roundInfo.changePoints;
        if (pointsNum < 0) return;

        if (!this.pointsNode) {
            // this.pointsNode = cc.instantiate(
            //     pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.HEAD_POINTS_ANI)
            // );
            pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.HEAD_POINTS_ANI).then((asset: cc.Prefab) => {
                this.pointsNode = cc.instantiate(asset);
                this.node.addChild(this.pointsNode, PokerMasterDef.LayerZorder.Z_IDX_ANIM_NODE_3);
                this.pointsNode.setPosition(
                    this.node.convertToNodeSpaceAR(
                        this._imgSelfHead.node.parent.convertToWorldSpaceAR(this._imgSelfHead.node.position)
                    )
                );
                this.pointsNode.getComponent(cc.Animation).on(
                    'finished',
                    (event: cc.Event): void => {
                        this.resetPointAni();
                    },
                    this
                );
                this.pointsNode.getComponent(HeadPointsAniControl).playPointAni(pointsNum);
            });
        } else this.pointsNode.getComponent(HeadPointsAniControl).playPointAni(pointsNum);
    }

    resetPointAni() {
        this._pokerMasterRoom.roundInfo.changePoints = 0;
        if (this.pointsNode) {
            this.pointsNode.getComponent(HeadPointsAniControl).resetPointAni();
        }
    }

    setLeftAndRightList() {
        // let panelLeftPlayerlist: cc.Node = this.node.getChildByName('panel_left_playerlist');
        // let panelRightPlayerlist: cc.Node = this.node.getChildByName('panel_right_playerlist');
        let headBgWidth = this._panelLeftPlayerlist.getChildByName('img_bg_0').width;
        let bgPosY = [288, 92, -104, -300, -300];
        let headPosY = 15;
        let coinPosY = -67;
        let leftNbFlag = cc.v2(-4, 330);
        let rightNbFlag = cc.v2(-16, 333);
        let w4 = 22; // 下注面板边缘存在3个间隙
        if (this._eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
            bgPosY = [419, 205, -9, -223, -437];
            leftNbFlag = cc.v2(-4, 461);
            rightNbFlag = cc.v2(-16, 464);
        } else if (this._eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            let baseWidth = cc.winSize.width - 2338;
            w4 = 96 + baseWidth * 0.5;
        } else {
            let baseWidth = cc.winSize.width - 1920;
            w4 = baseWidth > 0 ? 22 + baseWidth * 0.5 : 22;
        }
        this._panelLeftPlayerlist.getComponent(cc.Widget).left =
            w4 + headBgWidth * 0.5 - this._panelLeftPlayerlist.width * 0.5;
        this._panelRightPlayerlist.getComponent(cc.Widget).right =
            w4 + headBgWidth * 0.5 - this._panelRightPlayerlist.width * 0.5;
        pf.UIUtil.adaptWidget(this._panelLeftPlayerlist);
        pf.UIUtil.adaptWidget(this._panelRightPlayerlist);

        for (let i = 0; i < 5; ++i) {
            let leftImg = this._panelLeftPlayerlist.getChildByName(pf.StringUtil.formatC('img_bg_%d', i));
            let leftNodeHead = this._panelLeftPlayerlist.getChildByName(pf.StringUtil.formatC('node_head_%d', i));
            let leftTxtCoin = this._panelLeftPlayerlist.getChildByName(pf.StringUtil.formatC('text_coin_%d', i));

            let rightImg = this._panelRightPlayerlist.getChildByName(pf.StringUtil.formatC('img_bg_%d', i));
            let rightNodeHead = this._panelRightPlayerlist.getChildByName(pf.StringUtil.formatC('node_head_%d', i));
            let rightTxtCoin = this._panelRightPlayerlist.getChildByName(pf.StringUtil.formatC('text_coin_%d', i));

            leftImg.setPosition(cc.v2(0, bgPosY[i]));
            leftNodeHead.setPosition(cc.v2(0, bgPosY[i] + headPosY));
            leftTxtCoin.setPosition(cc.v2(0, bgPosY[i] + coinPosY));

            rightImg.setPosition(cc.v2(0, bgPosY[i]));
            rightNodeHead.setPosition(cc.v2(0, bgPosY[i] + headPosY));
            rightTxtCoin.setPosition(cc.v2(0, bgPosY[i] + coinPosY));

            if (this.bFlagReposition && i === 0) {
                let leftImgFlag = this._panelLeftPlayerlist.getChildByName('nb_flag');
                let rightImgFlag = this._panelRightPlayerlist.getChildByName('nb_flag');

                leftImgFlag.setPosition(leftNbFlag);
                rightImgFlag.setPosition(rightNbFlag);
            }
        }
        // console.log('世界坐标: ')
        // for (let ix = 0; ix < panel_left_playerlist.childrenCount; ++ix) {
        //     let ch = panel_left_playerlist.children[ix];
        //     cv.tools.logObject(ch.getPosition(), ch.name);
        // }

        // let nb_flag = panel_right_playerlist.getChildByName('nb_flag');
        // cv.tools.logObject(nb_flag.getPosition(), 'panel_right_playerlist:' + nb_flag.name);
    }

    showSwitchTable() {
        if (this._bSwitchTable) return;
        this._bSwitchTable = true;
        cr.commonResourceAgent.commonDialog.showMsg(
            pf.languageManager.getString('MiniGame_Switch_Content'),
            [pf.languageManager.getString('MiniGame_Switch_Table'), pf.languageManager.getString('MiniGame_Exit')],
            async () => {
                pf.app.emit('hideWebview');
                const roomId = this._pokerMasterRoom.roundInfo.idleRoomId;
                const pokerMasterService = pf.serviceManager.get(domain.PokerMasterService);
                await pokerMasterService.login();
                await this._pokerMasterRoom.joinRoom(roomId);
                if (this._eAutoBtnStyle === MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING) {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE);
                }
            },
            () => {
                pf.app.emit('hideWebview');
                this._backToRoomListScene();
            }
        );
    }

    // 冷静倒计时
    // onCalmDownShowTip(params: CalmDownParams) {
    //     if (params.calmDownLeftSeconds > 0) {
    //         cr.commonResourceAgent.calmDownDialog.autoShow(params);
    //     }
    // }

    onMsgAdvanceAutobetAdd(msg: any) {
        console.log('onMsgAdvanceAutobetAdd', msg);
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).showAdvanceAutoCount();
        }
    }

    private _onMsgConsumingNotify(msg: network.ILeftGameCoinNotify /* msg: pokermaster_proto.LeftGameCoinNotify */) {
        if (!this.consumingNotify) {
            const notifyObj = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.CONSUMING_PROMPT));
            this.consumingNotifyHolder.addChild(notifyObj);
            this.consumingNotifyHolder.parent.x = this._panelSelf.x - this._panelSelf.width / 2;
            this.consumingNotify = notifyObj.getComponent(ConsumingPromptControl);
        }
        this.consumingNotify.show(msg.lost_game_coin, msg.cur_game_coin, 2);
    }

    private _onPushNotification(notification: PushNotification) {
        const curLanguageContent = pf.StringUtil.getServerStrByLanguage(notification.msg);
        let content = '';
        if (notification.sourceType.length === 0) {
            content = curLanguageContent;
        } else {
            for (let gameId of notification.sourceType) {
                if (gameId === pf.client.GameId.PokerMaster) {
                    content = curLanguageContent;
                    break;
                }
            }
            // for (let i = 0; i < notification.sourceType.length; i++) {
            //     if (notification.sourceType[i] === pf.client.GameId.HumanBoy) {
            //         content = curLanguageContent;
            //         break;
            //     }
            // }
        }

        if (content.length > 0) {
            this._onMsgRewardTips(content);
        }
    }

    tryLeaveRoom(type: pf.client.ExitType = pf.client.ExitType.Standard) {
        if (type === pf.client.ExitType.NoLeaveRoom) {
            console.log('[3in1] poker master tryLeaveRoom with NoLeaveRoom');
            this.exitGame();
        } else {
            try {
                this._pokerMasterRoom.leaveRoom();
            } catch (err) {
                cc.warn(err);
            }
        }
    }

    exitGame() {
        cc.log('[PokerMaster] exit game');
        this._clearData();
        pf.bundleManager.exitBundle(macros.BUNDLE_NAME);
    }

    private _fitSafeArea() {
        const safeArea = pf.system.view.getSafeArea();
        cc.log(
            `[3in1] pokermaster safe area x:${safeArea.x}, y:${safeArea.y}, w: ${safeArea.width}, h: ${safeArea.height}`
        );

        if (safeArea.width <= 0 || safeArea.height <= 0) {
            if (cr.CommonUtil.isFitSafeAreaNeeded()) {
                cc.warn('[3in1] fit safe area fallback');
                this._panelLeftPlayerlist.setScale(
                    macros.SAFE_AREA_PLAYER_LIST_SCALE,
                    macros.SAFE_AREA_PLAYER_LIST_SCALE
                );
                this._panelLeftPlayerlist.setPosition(
                    this._panelLeftPlayerlist.x + macros.SAFE_AREA_PLAYER_LIST_OFFSET,
                    this._panelLeftPlayerlist.y
                );
                this._panelRightPlayerlist.setScale(
                    macros.SAFE_AREA_PLAYER_LIST_SCALE,
                    macros.SAFE_AREA_PLAYER_LIST_SCALE
                );
                this._panelRightPlayerlist.setPosition(
                    this._panelRightPlayerlist.x - macros.SAFE_AREA_PLAYER_LIST_OFFSET,
                    this._panelRightPlayerlist.y
                );

                this._panelGame.setScale(macros.SAFE_AREA_BOARD_SCALE, macros.SAFE_AREA_BOARD_SCALE);
            }
        } else {
            const safeAreaLeftBorder = safeArea.x - macros.safeAreaSurrenderDistance;
            const leftPlayerlistLeftBorder = this._panelLeftPlayerlist.getComponent(cc.Widget).left;
            // console.log(
            //     `[3in1] safe area left border:${safeAreaLeftBorder}, left playerlist left border:${leftPlayerlistLeftBorder}`
            // );
            if (safeAreaLeftBorder <= leftPlayerlistLeftBorder + macros.noFitBuffer) {
                cc.log('[3in1] no need to fit safe area');
            } else {
                cc.log('[3in1] need to fit safe area');
                const newLeftBorder = safeAreaLeftBorder - macros.noFitBuffer;
                const canvasNode = this.getComponent(cc.Canvas).node;
                const posWorld = canvasNode.convertToWorldSpaceAR(this._panelGame.position);
                // console.log('[3in1] game panel world pos:', posWorld);
                // console.log('[3in1] winSize:', cc.winSize);
                const newGamePanelLeftBorder = posWorld.x - macros.narrowGamePanelWidth * 0.5;
                const leftPlayerPanelRightBorder =
                    newGamePanelLeftBorder - macros.gapBetweenGamePanelAndPlayerlistPanel;
                const newPlayerlistWidth = leftPlayerPanelRightBorder - newLeftBorder;
                const curPlayerlistWidth = this._panelLeftPlayerlist.width;
                // console.log(`[3in1] game left:${newGamePanelLeftBorder}, left right:${leftPlayerPanelRightBorder}`);
                // console.log(`[3in1] left panel width cur:${curPlayerlistWidth}, new:${newPlayerlistWidth}`);

                // console.log('[3in1] panel_game w:' + this._panelGame.width);
                const gameBoardRatio = macros.narrowGamePanelWidth / this._panelGame.width;
                // console.log('[3in1] game board ratio:' + gameBoardRatio);
                this._panelGame.setScale(gameBoardRatio, gameBoardRatio);

                const newLeftPlayerlistX = newLeftBorder + newPlayerlistWidth * 0.5;
                const playerlistRatio = newPlayerlistWidth / curPlayerlistWidth;
                if (playerlistRatio < 1) {
                    this._panelLeftPlayerlist.setScale(playerlistRatio, playerlistRatio);
                }
                let posNew = canvasNode.convertToNodeSpaceAR(cc.v2(newLeftPlayerlistX, 0));
                this._panelLeftPlayerlist.setPosition(posNew.x, this._panelLeftPlayerlist.position.y);

                const newGamePanelRightBorder = posWorld.x + macros.narrowGamePanelWidth * 0.5;
                if (playerlistRatio < 1) {
                    this._panelRightPlayerlist.setScale(playerlistRatio, playerlistRatio);
                }
                const rightPlayerPanelLeftBorder =
                    newGamePanelRightBorder + macros.gapBetweenGamePanelAndPlayerlistPanel;
                const newRightPlayerlistX = rightPlayerPanelLeftBorder + newPlayerlistWidth * 0.5;
                posNew = this.node.getComponent(cc.Canvas).node.convertToNodeSpaceAR(cc.v2(newRightPlayerlistX, 0));
                this._panelRightPlayerlist.setPosition(posNew.x, this._panelRightPlayerlist.position.y);
            }
        }
    }

    private _adaptBtnMenu() {
        let btnMenu: cc.Node = this.node.getChildByName('btn_menu');
        setTimeout(() => {
            if (btnMenu) {
                if (btnMenu.getComponent(cc.Widget)) {
                    btnMenu.getComponent(cc.Widget).enabled = false;
                }
                btnMenu.setPosition(cc.v2(this._panelLeftPlayerlist.x, btnMenu.y));
            }
            let rebateFloatingButton: cc.Node = this.node.getChildByName('rebate_float_button');
            if (rebateFloatingButton) {
                const space = btnMenu.width / 2 + rebateFloatingButton.width / 2;
                rebateFloatingButton.setPosition(cc.v2(btnMenu.x + space, rebateFloatingButton.y));
            }
        }, 100);
    }

    protected initDialogHub(): void {}

    protected GetDialogHub(): DialogHubControl {
        return cr.commonResourceAgent.dialogHub;
    }

    private hideRebateActivity() {
        this._rebateActivity?.hide();
        this.GetDialogHub().processClose();
    }

    private showRebateRewardPopup(reward_amount: { [k: string]: number }) {
        const msgContent = pf.StringUtil.formatC(
            pf.languageManager.getString('minigame_rebate_reward_popup'),
            cr.RebateUtil.getRewardString(reward_amount, '#FFFF00')
        );

        this.GetDialogHub()
            .onInit((node) => {
                const controller = node.getComponent(RebateRewardPopupControl);
                if (controller) {
                    controller.init();
                    controller._playPopup();
                }
            })
            .showPopup({
                popupId: MiniGameDialogPopupId.RebateRewardPopup,
                content: msgContent,
                sureCallback: () => {
                    // cv.dialogMager.processClose();
                    this.GetDialogHub().processClose();
                }
            });
    }

    _onResponseRebateReceiveReward(data: pf.client.Rebate.IRebateNoticeMessage): void {
        // cv.MessageCenter.send("onClaimedReward");

        const list = Object.entries(data.reward_amount);
        if (list.length === 0) {
            return;
        }

        const msgContent = pf.StringUtil.formatC(
            pf.languageManager.getString('Rebate_claim_reward_success'),
            cr.RebateUtil.getRewardString(data.reward_amount,'#FFDE58')
        );

        this._rebateClaimToast = () => {
            this.GetDialogHub().showPopup(
                {
                    popupId: MiniGameDialogPopupId.ToastMsg,
                    content: msgContent
                },
                true
            );
        };
    }

    private _updateActivityData(
        activityNode: cc.Node,
        activityId: number,
        data: pf.client.IEventStatusClient
    ): void {
        let tag = activityNode.getComponent(TagControl);
        if (tag === null) {
            tag = activityNode.addComponent(TagControl);
        }
        tag.nIdx = activityId;
        activityNode.getComponent(BettingRebateEventControl).showRebateEvent(data);
        
    }

    private getActivityTitle(activityId: number, isDaily: boolean): string {
        switch (activityId) {
            case 1:
                return pf.languageManager.getString('minigame_cowboy_rebate_title');
            case 2:
                return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_2');
            case 3:
                return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_3');
            case 4:
                if (isDaily) {
                    return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_4_daily');
                }
                return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_4');
            default: // need update for more cases later
                return pf.languageManager.getString('minigame_cowboy_rebate_title');
        }
    }

    private getActivityContent(activityId: number): string {
        switch (activityId) {
            case 1:
                return pf.languageManager.getString('minigame_cowboy_rebate_content');
            case 2:
                return pf.languageManager.getString('minigame_cowboy_rebate_content_activity_2');
            case 3:
                return pf.languageManager.getString('minigame_cowboy_rebate_content_activity_3');
            case 4:
                return pf.languageManager.getString('minigame_cowboy_rebate_content_activity_4');
            default: // need update for more cases later
                return pf.languageManager.getString('minigame_cowboy_rebate_content');
        }
    }

    private onRebateClicked() {
        if (!this._rebateEventStatus) {
            return;
        }
        const currentTime = Date.now();
        if (currentTime - this._lastTimeClick > 2000) //Throttle the event status request
        {
            this._lastTimeClick = currentTime;
            this._getRebateEventStatus();
        }
        const eventType: number = 1;

        this.GetDialogHub()
            .onInit((node) => {
                this._rebateEventStatus.system_time = this._lastSystemTime + Math.floor((Date.now() - this._lastTimeGetRebateEventStatus)/1000);
                this._updateActivityData(node, eventType, this._rebateEventStatus);
            })
            .showPopup({
                popupId: eventType,
                title: cr.RebateUtil.getEventTitle(this._rebateEventStatus),
                content: this.getActivityContent(eventType),
                onAction2: () => {
                    this.GetDialogHub().processClose();
                },
                horizontalAlign: cc.Label.HorizontalAlign.LEFT
            });

    }
    
    onMsgRebateActivityActive() {
        if (!cc.isValid(this.node)){
            return;
        }
        const holder = this.node.getChildByName('rebate_float_button');

        if (!this._rebateEventStatus) {
            holder.active = false;
            this.GetDialogHub().processClose();
            return;
        }
        // regenerate new floating button for new event
        if (!this._rebateActivity || this._rebateActivity.eventId !== this._rebateEventStatus.id) {
            holder.destroyAllChildren();
            this._rebateActivity = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.REBATE_FLOATING_BUTTON_NEW)
            ).getComponent(RebateFloatingButtonControl);
            this._rebateCoinsFly = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.REBATE_COINS_FLY)
            );

            holder.addChild(this._rebateActivity.node);
            this.node.addChild(this._rebateCoinsFly);         
            this._rebateActivity.init(this._rebateEventStatus.id, true);
            this._rebateActivity.setup(this.onRebateClicked.bind(this));

            this._rebateActivity.node.on('barMaxAnimationEnd', this._boundBarMaxAnimationEnd);
        }

        holder.active = true;
        this._rebateActivity.play(this._rebateEventStatus);
        this._rebateService.emit("eventStatusResult", this._rebateEventStatus);
    }

    private _onRebateStatusNotify(){
        this.unschedule(this._boundGetRebateEventStatus);
        this.scheduleOnce(this._boundGetRebateEventStatus, 0.5);
    }

    private _getRebateEventStatus() {
        this._rebateService.getEventStatus().then((response) => {
            this._rebateEventStatus = cr.RebateUtil.getActiveEventByGame(pf.client.GameId.PokerMaster, response.events);
            this.onMsgRebateActivityActive();
            this._lastTimeGetRebateEventStatus = Date.now();
            this._lastSystemTime = this._rebateEventStatus?.system_time || 0;
        });

        this._rebateService.getLeaderBoard().then((response) => {
            this._rebateService.emit("eventLeaderboardResult", response);
        });
    }

    OnGameDataSynNotify() {
        this._getRebateEventStatus();
    }

    private async onBarMaxAnimationEnd() {
        if (this._rebateCoinsFly && this._selfAvatar && this._rebateActivity) {
            if (this._rebateService && this._rebateService.rebateEvents.length > 0) {
                const event = this._rebateService.rebateEvents[0];
                const idx = event.id;
                const betTime = event.setting.bet_time[0];
                
                let rewardProgressIndex = 0;
                let rewardAmount = 0;
                
                if (betTime && betTime.reward_progress) {
                    for (let i = 0; i < betTime.reward_progress.length; i++) {
                        if (betTime.reward_progress[i].can_get && !betTime.reward_progress[i].got) {
                            rewardProgressIndex = i;
                            rewardAmount = betTime.reward_progress[i].reward;
                            break;
                        }
                    }
                }
                
                await this._rebateCoinsFly.getComponent(RebateCoinsFlyControl).playCoinsFlyAnimation(
                    this._selfAvatar.node, 
                    this._rebateActivity.node, 
                    rewardAmount
                );
                this._rebateClaimToast?.();
            }
        }
    }
}

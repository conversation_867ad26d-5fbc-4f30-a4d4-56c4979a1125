/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import Wallet = pf.services.Wallet;
import Deque = pf.Deque;

import PlayerOneBet = pf.services.PlayerOneBet;

// import { MiniGameCommonDef } from '../../common/humanboy-define';
import { JackPotNumberControl } from '../dzpoker/JackPotNumberControl';
import { HumanboyChartControl } from './HumanboyChartControl';
// import { HumanboyToastControl } from './HumanboyToastControl';
import { HumanboyJackpotControl } from './HumanboyJackpotControl';
// import { HumanboyRewardTipsControl } from './HumanboyRewardTipsControl';
// import { HumanboyFlutterScoreControl } from './HumanboyFlutterScoreControl';
import { eHumanboyDealerListViewType, HumanboyDealerListControl } from './HumanboyDealerListControl';

import { macros } from '../../common/humanboy-macros';
import * as domain from '../../domain/humanboy-domain-index';
import * as network from '../../network/humanboy-network-index';

import * as cr from '../../../../common-resource/scripts/common-resource';
import AvatarControl = cr.components.AvatarControl;
import TagControl = cr.components.TagControl;
import ConcreteAdvancedAuto = cr.components.ConcreteAdvancedAuto;
import HeadPointsAniControl = cr.components.HeadPointsAniControl;
import LuckTurntableButtonControl = cr.components.LuckTurntableButtonControl;
import MiniGameAudioSettingControl = cr.components.MiniGameAudioSettingControl;
import MiniGameRuleControl = cr.components.MiniGameRuleControl;
import MiniGamePlayerListControl = cr.components.MiniGamePlayerListControl;
import MiniGameExchangeControl = cr.components.MiniGameExchangeControl;
import HumanboyDialogControl = cr.components.HumanboyDialogControl;
import HumanboyBetCoinControl = cr.components.HumanboyBetCoinControl;
import HumanboyAdvancedAutoControl = cr.components.HumanboyAdvancedAutoControl;
import MiniGameAdvancedSettingControl = cr.components.MiniGameAdvancedSettingControl;
import MiniGameDialog = cr.components.MiniGameDialog;
import MiniGameAdvancedAuto = cr.components.MiniGameAdvancedAuto;
import ConcreteMiniGameDialog = cr.components.ConcreteMiniGameDialog;
import IMiniGameDialog = cr.components.IMiniGameDialog;
import IMiniGameDialogConfig = cr.components.IMiniGameDialogConfig;
import ThemeSystemType = cr.components.ThemeSystemType;
import PokerCardControl = cr.components.PokerCardControl;
import MiniGameMenuControl = cr.components.MiniGameMenuControl;
// import CalmDownParams = pf.services.CalmDownParams;
import PushNotification = pf.services.PushNotification;
import MiniGameGuideControl = cr.components.MiniGameGuideControl;
import ConsumingPromptControl = cr.components.ConsumingPromptControl;
import MiniGameCommonDef = cr.MiniGameCommonDef;
import HumanboyToastControl = cr.components.HumanboyToastControl;
import HumanboyRewardTipsControl = cr.components.HumanboyRewardTipsControl;
import HumanboyFlutterScoreControl = cr.components.HumanboyFlutterScoreControl;
import RebateFloatingButtonControl = cr.components.RebateFloatingButtonControl;
import RebateRewardPopupControl = cr.components.RebateRewardPopupControl;
import type { DialogHubControl } from '../../../../common-resource/scripts/components/DialogHubControl';
import MiniGameDialogPopupId = cr.components.MiniGameDialogPopupId;

import RebateCoinsFlyControl from '../../../../common-resource/scripts/components/rebate-promotion/RebateCoinsFlyControl';
import BettingRebateEventControl from '../../../../common-resource/scripts/components/rebate-promotion/BettingRebateEventControl';

/**
 * 百人德州主界面层级枚举
 */
export enum eHumanboyLocalZorder {
    HL_ZORDER_DUMMY = 0, // 默认

    HL_ZORDER_IMG_HEAD = 9, // 头像
    HL_ZORDER_IMG_HEAD_TXT, // 头像文本
    HL_ZORDER_IMG_HEAD_FLAG, // 头像标签
    HL_ZORDER_IMG_WIN_COUNT, // 玩家连胜

    HL_ZORDER_COIN_POOL, // 金币池节点
    HL_ZORDER_ANIM_NODE, // 动画节点
    HL_ZORDER_ANIM_NODE_0, // 动画节点0
    HL_ZORDER_ANIM_NODE_1, // 动画节点1
    HL_ZORDER_ANIM_NODE_2, // 动画节点2
    HL_ZORDER_ANIM_NODE_3, // 动画节点3

    HL_ZORDER_PANEL_COUNT_DOWN, // 开局倒计时面板
    HL_ZORDER_PANEL_ADVANCE_AUTO_SELECT, // 高级续投选择面板
    HL_ZORDER_PANEL_REWRAD_TIP, // 中奖提示面板
    HL_ZORDER_PANEL_RED_PACKET, // 红包面板
    HL_ZORDER_PANEL_AUTO_SELECT, // 高级续投选择面板
    HL_ZORDER_PANEL_JACKPOT, // jackpot面板
    HL_ZORDER_PANEL_RECORD, // 牌局记录面板
    HL_ZORDER_PANEL_DEALERLIST, // 上庄列表面板
    HL_ZORDER_PANEL_SETTING, // 设置面板
    HL_ZORDER_PANEL_GUID, // 引导面板
    HL_ZORDER_PANEL_SERVER_TOAST, // 提示面板
    HL_ZORDER_ADVANCE_AUTO_ADD_SELECT
}

/**
 * 上庄按钮状态枚举
 */
export enum eHumanboyDealerBtnStatus {
    HDB_STATUS_NONE = 0, // 无
    HDB_STATUS_DEALER_UP, // 上庄
    HDB_STATUS_DEALER_DOWN // 下庄
}

/**
 * 百人德州下注区UI结构信息
 */
export class HumanboyAreaInfo {
    eZone: network.BetZoneOption = network.BetZoneOption.BetZoneOption_DUMMY; // 该区域枚举
    eWayOutStyle: MiniGameCommonDef.eGameboyWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE; // 路子显示风格
    index: number = 0; // 该区域索引
    iWayOutLoseLimitCount: number = 0; // 路单描述文本"xxx局"未出上限(超过上限显示: "xxx+ 局未出", 默认0表示无上限)
    panelArea: cc.Node = null; // 区域根节点
    panelCoin: cc.Node = null; // 金币层
    panelCard: cc.Node = null; // 牌层
    panelWayOut: cc.Node = null; // 路单层
    panelBorder: cc.Node = null; // 特效边框层
    imgCardTypeBg: cc.Sprite = null; // 牌型背景
    imgCardTypeTxt: cc.Sprite = null; // 牌型
    txtSelfBetNum: cc.Label = null; // 自己下注文本节点
    txtTotalBetNum: cc.Label = null; // 总下注文本节点
    rtxtWayOut: cc.RichText = null; // 路子描述文本
    txtOdds: cc.Label = null; // 赔率
    vCoinQueue: Deque<HumanboyBetCoinControl> = new Deque(); // 复用金币双向队列
    vCardsNode: PokerCardControl[] = []; // 牌组精灵数组
    vCardsSrcPos: cc.Vec2[] = []; // 牌组精灵原始位置数组
    vWayOutImg: cc.Sprite[] = []; // 路子精灵数组
    vWayOutImgSrcPos: cc.Vec2[] = []; // 路子精灵原始位置数组
}

/**
 * 百人德州主界面8位玩家UI专属结构信息
 */
export class HumanboyPlayerInfo {
    uid: number = 0;
    imgBg: cc.Sprite = null;
    nodeHead: cc.Node = null;
    txtCoin: cc.Label = null;
    imgFlag: cc.Sprite = null; // 富豪/神算子
    avatarControl: AvatarControl = null;
}

/**
 * 百人德州庄家UI专属结构信息
 */
export class HumanboyDealerInfo {
    index: number = 0; // 庄家索引
    txtName: cc.Label = null; // 庄家昵称
    txtGold: cc.Label = null; // 庄家金币
    imgGold: cc.Sprite = null; // 庄家金币精灵(用于飞金币的起始位置)
    imgHead: cc.Sprite = null; // 庄家头像
    avatarControl: AvatarControl;
    imgHeadBox: cc.Sprite = null; // 庄家头像边框
    imgIcon: cc.Sprite = null; // 庄家图标
    imgBeDealerNum: cc.Sprite = null; // 坐庄次数底图
    rtxtBeDealerNum: cc.RichText = null; // 坐庄次数
}

/**
 * 百人德州 金币优化结构体
 */
export class HumanboyCoinOptimization {
    nAreaIdx: number = 0;
    nGold: number = 0;
    nUid: number = 0;
    bAnim: boolean = false;
    bHeadAnim: boolean = false;
    bPlaySound: boolean = false;
}

// class effectLoop {
//     audioId: number = 0;
//     duringTime: number = 0;
//     startPlayTime: number = 0;
//     bGoOn: boolean = false;
//     func: Function = null;
// }

/**
 * 百人德州主逻辑类
 */
const { ccclass, property } = cc._decorator;
@ccclass
export class HumanboyControl extends cc.Component {
    static g_fullScreenOffset: cc.Vec2 = cc.Vec2.ZERO; // 全面屏偏移量

    @property(cc.Prefab) prefab_dz_jackPotNumber: cc.Prefab = null; // jackPotNumber 预制件
    // @property(cc.Prefab) prefab_cb_win_player_light: cc.Prefab = null; // 牛仔玩家赢亮框 预制件
    // @property(cc.Prefab) prefab_cb_exchange: cc.Prefab = null;
    // @property(cc.Prefab) prefab_cb_rule: cc.Prefab = null; // 规则面板 预制件
    // @property(cc.Prefab) prefab_cb_soundSetting: cc.Prefab = null; // 设置面板 预制件
    // @property(cc.Prefab) prefab_cb_exit: cc.Prefab = null; // 退出面板 预制件

    // @property(cc.Prefab) prefab_hb_win_flag: cc.Prefab = null; // win 旗子动画 预制件
    // @property(cc.Prefab) prefab_hb_start_bets: cc.Prefab = null; // 开始下注动画 预制件
    // @property(cc.Prefab) prefab_hb_end_bets: cc.Prefab = null; // 停止下注动画 预制件
    @property(cc.Prefab) prefab_hb_dealer_victory_zh_CN: cc.Prefab = null; // 庄家完胜动画 中文版预制件
    @property(cc.Prefab) prefab_hb_dealer_victory_en_US: cc.Prefab = null; // 庄家完胜动画 英文版预制件
    @property(cc.Prefab) prefab_hb_dealer_defeat_zh_CN: cc.Prefab = null; // 庄家完败动画 中文版预制件
    @property(cc.Prefab) prefab_hb_dealer_defeat_en_US: cc.Prefab = null; // 庄家完败动画 英文版预制件
    // @property(cc.Prefab) prefab_hb_way_out: cc.Prefab = null; // 路单闪光动画 预制件

    // @property(cc.Prefab) prefab_hb_flutterScore: cc.Prefab = null; // 飘分 预制件
    // @property(cc.Prefab) prefab_hb_betCoin: cc.Prefab = null; // 下注金币 预制件
    // @property(cc.Prefab) prefab_hb_toast: cc.Prefab = null; // 游戏提示 预制件
    // @property(cc.Prefab) prefab_hb_guid: cc.Prefab = null; // 新手引导 预制件
    // @property(cc.Prefab) prefab_hb_menu: cc.Prefab = null; // 游戏菜单 预制件
    // @property(cc.Prefab) prefab_hb_advancedSetting: cc.Prefab = null; // 高级设置 预制件
    // @property(cc.Prefab) prefab_hb_advancedAuto: cc.Prefab = null; // 高级续投 预制件
    // @property(cc.Prefab) prefab_hb_addAdvancedAuto: cc.Prefab = null; // mini-game advance auto
    // @property(cc.Prefab) prefab_hb_dialog: cc.Prefab = null; // 对话框 预制件

    @property(cc.Prefab) prefab_hb_jackPot: cc.Prefab = null; // jackPot面板 预制件
    @property(cc.Prefab) prefab_hb_dealerList: cc.Prefab = null; // 上庄列表 预制件
    // @property(cc.Prefab) prefab_hb_playerList: cc.Prefab = null; // 玩家列表 预制件
    @property(cc.Prefab) prefab_hb_chart: cc.Prefab = null; // 路单面板 预制件
    // @property(cc.Prefab) prefab_hb_rewardTips: cc.Prefab = null; // 通用奖励提示 预制件
    // @property(cc.Prefab) prefab_luckButton: cc.Prefab = null; // 红包节 预制件
    // @property(cc.Prefab) points_ani_prefab: cc.Prefab = null;
    // @property(cc.Prefab) popSilencePre: cc.Prefab = null; // 冷静预制件
    // @property(cc.Prefab) consumingNotifyPrefab: cc.Prefab = null;
    @property(cc.Node) consumingNotifyHolder: cc.Node = null;

    // @property(cc.Prefab) prefab_avatar: cc.Prefab = null;
    // @property(cc.Prefab) prefab_card: cc.Prefab = null;
    // @property(cc.SpriteFrame) cardbackSpriteFrame: cc.SpriteFrame = null;

    private points_node: cc.Node = null;
    private _msInterval: number = 1; // 定时器间隔(单位: 秒)
    private _msNowTime: number = 0; // 当前时间
    private _msLastTime: number = 0; // 上次时间
    private _nLeftTime: number = 0; // 剩余时间
    private _nMinTime: number = 0; // 最小时间

    private _panel_game: cc.Node = null; // 游戏面板
    private _panel_top: cc.Node = null; // 顶栏面板
    private _panel_bottom: cc.Node = null; // 底栏面板
    private _panel_self: cc.Node = null; // 玩家信息面板
    private _panel_betbtn: cc.Node = null; // 下注按钮面板

    private _panel_left_playerlist: cc.Node = null;
    private _panel_right_playerlist: cc.Node = null;

    private _panel_jackpot: cc.Node = null; // jackpot面板
    private _panel_dealer: cc.Node = null; // 庄家面板

    private _panel_dealer_extra: cc.Node = null; // 庄家额外显示面板
    private _rtxt_dealer_extra: cc.RichText = null; // 庄家额外显示面板 文本
    private _btn_dealer_extra: cc.Button = null; // 庄家额外显示面板 按钮

    private _img_dealer_card_type: cc.Sprite = null; // 庄家牌型
    private _img_dealer_card_shield: cc.Node = null; // 庄家牌遮罩底图
    private _txt_shared_limit_word: cc.Label = null; // 四门总限红描述
    private _txt_shared_limit_amount: cc.Label = null; // 四门总限红金额

    private _vBottomBetBtns: MiniGameCommonDef.GameNodeScale[] = []; // 底部下注按钮数组, 用于适配位置(k - 节点, v - 原缩放比例)
    private _vDealerInfo: HumanboyDealerInfo[] = []; // 庄家UI信息数组
    private _vDealerCardNode: PokerCardControl[] = [];
    private _vDealerCardSrcPos: cc.Vec2[] = []; // 庄家牌精灵原始位置数组

    private _txt_self_name: cc.Label = null; // 个人昵称
    private _txt_self_gold: cc.Label = null; // 个人金币
    private _img_self_gold: cc.Sprite = null; // 个人金币精灵(用于飞金币的起始位置)
    private _img_self_head: cc.Sprite = null; // 个人头像
    private _selfAvatar: AvatarControl = null;

    private _src_bet_clock_pos: cc.Vec2 = cc.Vec2.ZERO; // 下注计时器原始位置
    private _img_bet_clock: cc.Node = null; // 下注计时器精灵
    private _img_count_down: cc.Sprite = null; // 等待下一局倒计时

    private _btn_menu: cc.Button = null; // 菜单按钮
    private _btn_record: cc.Button = null; // 牌局路单记录按钮
    private _btn_playerList: cc.Node = null; // 玩家列表按钮
    private _btn_betAuto: cc.Button = null; // 续投按钮
    private _btn_betClean: cc.Button = null; // 清屏按钮(清理下注区域金币)
    private _btn_redpacket_festival: cc.Node = null; // 红包节按钮
    private _btn_redpacket_festival_layer: cc.Node = null; // 红包节按钮提示层
    private _luckButton: LuckTurntableButtonControl = null; // 红包节实例

    private _vJackPotNumberList: JackPotNumberControl[] = []; // jackpot数字节点数组
    private _vOtherPlayerInfo: HumanboyPlayerInfo[] = []; // 其他玩家列表
    private _vAreasInfo: HumanboyAreaInfo[] = []; // 下注区域数组
    private _vBetButtons: HumanboyBetCoinControl[] = []; // 下注按钮数组
    private _mapSounds: Map<string, boolean> = new Map(); // 声音容器(名称 - id)
    private _mapBetAreaLimitAmount: Map<network.BetZoneOption, number> = new Map(); // 下注区域限红

    private _nodeAnim: cc.Node = null; // 动态动画根节点
    private _nodeCoinPool: cc.Node = null; // 动态金币池节点
    private _llCoinPoolZOrderCount: number = 0; // 动态金币池节点深度计数

    private _vNodeWinFlagAnims: cc.Node[] = []; // win 动画数组
    private _vAtlWinFlagActions: cc.Animation[] = [];

    private _nodeFightBeginAnim: cc.Node = null; // 出战动画(开始下注)
    private _atlFightBeginAction: cc.Animation = null; //

    private _nodeFightEndAnim: cc.Node = null; // 开战动画(停止下注)
    private _atlFightEndAction: cc.Animation = null; //

    private _nodeDealerVictoryAnim: cc.Node = null; // 庄家完胜
    private _atlDealerVictoryAction: cc.Animation = null;

    private _nodeDealerDefeatAnim: cc.Node = null; // 庄家完败
    private _atlDealerDefeatAction: cc.Animation = null;

    private _nodeWayoutLightAnim: cc.Node = null; // 路单闪光动画
    private _atlWayoutLightAction: cc.Animation = null;

    private _nBetBtnNum: Readonly<number> = 5; // 下注按钮数量
    private _fBetBtnSrcScaleRate: Readonly<number> = 0.75; // 下注筹码原始缩放比例
    private _fBetBtnTarScaleRate: Readonly<number> = 1.0; // 下注筹码目标缩放比例
    private _fFlyCoinScaleRate: Readonly<number> = 0.5; // 创建的金币缩放比例

    private _nCurBetBtnIndex: number = -1; // 当前下注按钮索引
    private _nAreaCoinLimitCountMin: number = 100; // 单个区域金币精灵上限最小数量
    private _nAreaCoinLimitCountMax: Readonly<number> = 200; // 单个区域金币精灵上限最大数量

    private _nSendCardsTotalNum: number = 0; // 发牌总数
    private _nSendCardsCallBackNum: number = 0; // 发牌回调计数
    private _nMergeAutoBetNum: number = 0; // 续投回调计数

    private _bWaitting: boolean = false; // 是否正在等待开局倒计时
    private _nWaittingTime: number = 0; // 等待时间

    private _fActExecute_RoundStart: Readonly<number> = 0; // 开局动画 执行时间(2.0s)
    private _fActDelayed_SendCard: Readonly<number> = 0.0; // 发牌动画 延时时间
    private _fActExecute_SendCard: Readonly<number> = 1.0; // 发牌动画 执行时间

    private _fActDelayed_FightBegin: Readonly<number> = 0.0; // 开始下注动画 延时时间
    private _fActExecute_FightBegin: Readonly<number> = 1.0; // 开始下注动画 执行时间

    private _fActExecute_BetClock: Readonly<number> = 0.5; // 下注闹钟动画 执行时间

    private _fActDelayed_FightEnd: Readonly<number> = 0.5; // 停止下注动画 延时时间
    private _fActExecute_FightEnd: Readonly<number> = 1.0; // 停止下注动画 执行时间

    private _fActDelayed_ShowCard_Step_1: Readonly<number> = 0.2; // 翻牌动画 延时时间
    private _fActDelayed_ShowCard_Step_2: Readonly<number> = 0.2; // 翻牌动画 延时时间

    private _fActExecute_ShowCard_Step_1: Readonly<number> = 0.2; // 翻牌动画1 执行时间
    private _fActExecute_ShowCard_Step_2: Readonly<number> = 0.2; // 翻牌动画2 执行时间

    private _fActDelayed_ShowWinFlag: Readonly<number> = 0.5; // 显示win动画 延时时间
    private _fActExecute_WinFlag: Readonly<number> = 2.5; // win动画 执行时间
    private _fActDelayed_HideWinFlag: Readonly<number> = 1.0; // 隐藏win动画 延时时间

    private _fActExecute_WayOut: Readonly<number> = 1.0; // 显示路子动画 执行时间
    private _fActExecute_WayOutLight: Readonly<number> = 1.2; // 显示路子动画闪光 执行时间

    private _fActDelayed_VictoryOrDefeat: Readonly<number> = 0.5; // 庄家完胜完败动画 延时时间
    private _fActExecute_VictoryOrDefeat: Readonly<number> = 2.0; // 庄家完胜完败动画 执行时间

    private _fActDelayed_JackPot: Readonly<number> = 0.5; // jackpot 延时时间
    private _fActExecute_JackPot: Readonly<number> = 2.5; // jackpot 执行时间

    private _fActDelayed_LuckBlow: Readonly<number> = 0.5; // 特殊牌型动画 延时时间
    private _fActExecute_LuckBlow_1: Readonly<number> = 0.0; // 特殊牌型1 小牌动画 执行时间
    private _fActExecute_LuckBlow_2: Readonly<number> = 3.5; // 特殊牌型2 中牌动画 执行时间
    private _fActExecute_LuckBlow_3: Readonly<number> = 7.5; // 特殊牌型3 大牌动画 执行时间
    private _fActDelayed_LuckBlowHightLight: Readonly<number> = 0.5; // 特殊牌型高亮动画 延时时间
    private _fActExecute_LuckBlowHightLight: Readonly<number> = 1.0; // 特殊牌型高亮动画 执行时间

    private _fActDelayed_FlyWinCoin: Readonly<number> = 0.5; // win飞金币 延时时间
    private _fActExecute_FlyWinCoin: Readonly<number> = 1.5; // win飞金币 执行时间
    private _fActExecute_FlyWinCoinEnd: Readonly<number> = 2.0; // win飞金币 总时间

    private _dealerListView: HumanboyDealerListControl = null; // 上庄列表面板
    private _btnStatus: eHumanboyDealerBtnStatus = eHumanboyDealerBtnStatus.HDB_STATUS_NONE; // 上庄按钮状态
    private _eAutoBtnStyle: MiniGameCommonDef.eGameboyAutoBtnStyle =
        MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NONE; // 续投按钮样式
    private _eGameboyScreenType: MiniGameCommonDef.eGameboyScreenType =
        MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW; // 屏幕类型

    private _humanboyGuide: MiniGameGuideControl = null; // 路单引导 实例
    private _humanboyMenu: MiniGameMenuControl = null; // 游戏菜单 实例
    private _humanboyAdvancedSetting: MiniGameAdvancedSettingControl = null; // 高级设置 实例
    private _humanboyAdvancedAuto: HumanboyAdvancedAutoControl = null; // 高级续投 实例

    private _humanboyChart: HumanboyChartControl = null; // 走势图 实例
    private _humanboyJackpot: HumanboyJackpotControl = null; // 彩池 实例
    private _humanboyPlayerList: MiniGamePlayerListControl = null; // 玩家列表 实例
    private _humanboyExchange: MiniGameExchangeControl = null;
    private _humanboyRule: MiniGameRuleControl = null; // 规则 实例
    private _humanboyAudioSetting: MiniGameAudioSettingControl = null; // 设置 实例
    private _humanboyRewardTips: HumanboyRewardTipsControl = null; // 通用奖励提示 实例

    private _atlas_cb_language: cc.SpriteAtlas = null; // 牛仔语言图集
    private _atlas_hb_language: cc.SpriteAtlas = null; // 百人语言图集
    private _atlas_hb_humanboy: cc.SpriteAtlas = null; // 百人其它图集
    private _vCoinOptimizationDeque: Deque<HumanboyCoinOptimization> = new Deque(); // 金币最优队列

    private _isEnterBackground: boolean = false;
    // private _effectMap: Map<string, effectLoop> = new Map();
    private _bSwitchTable: boolean = false;
    private consumingNotify: ConsumingPromptControl = null;

    private _humanboyRoom: pf.Nullable<domain.HumanboyRoom> = null;
    private _walletService: pf.services.WalletService = null;
    private _pushNotificationService: pf.services.PushNotificationService = null;
    private _authService: pf.services.AuthService = null;
    private _luckTurntableService: pf.services.LuckTurntableService = null;
    // private _calmDownService: pf.services.CalmDownService = null;

    private _boundEnterForegroundHandler = this.OnAppEnterForeground.bind(this);
    private _boundEnterBackgroundHandler = this.OnAppEnterBackground.bind(this);
    private _boundUpdateGoldHandler = this._onMsgUpdateWorldServerGold.bind(this);

    private _boundLuckTurntableStartOrEnd = this._onMsgShowLuckButton.bind(this);
    private _boundLuckTurntableResult = this._onMsgTurntableResultNotice.bind(this);

    // private _boundCalmDown = this.onCalmDownShowTip.bind(this);

    private _boundPushNotification = this._onPushNotification.bind(this);
    private _boundRebateClaimNotify = this._onResponseRebateReceiveReward.bind(this);
    private _platform: string = '';

    private _rebateEventStatus: pf.client.IEventStatusClient = null;

    private _lastSoundTime: number = 0;
    private _lastSoundName: string;
    private _rebateActivity: RebateFloatingButtonControl = null;
    private _rebateService: pf.services.RebateService = null;
    private _boundRebateEventStop = this.hideRebateActivity.bind(this);
    private _boundGetRebateEventStatus = this._getRebateEventStatus.bind(this);
    private _boundRebateEventStatusNotify = this._onRebateStatusNotify.bind(this);
    private _rebateCoinsFly: cc.Node = null;
    private _boundBarMaxAnimationEnd = this.onBarMaxAnimationEnd.bind(this);

    private _rebateClaimToast: Function = null;

    private _lastTimeGetRebateEventStatus: number = 0;
    private _lastSystemTime: number = 0;
    private _lastTimeClick = 0;

    protected onLoad(): void {
        // this._humanboyService = pf.serviceManager.get(domain.HumanboyService);
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._humanboyRoom = context.room as domain.HumanboyRoom;

        this._walletService = pf.serviceManager.get(pf.services.WalletService);

        this._pushNotificationService = pf.serviceManager.get(pf.services.PushNotificationService);

        this._authService = pf.serviceManager.get(pf.services.AuthService);

        this._luckTurntableService = pf.serviceManager.get(pf.services.LuckTurntableService);

        // this._calmDownService = pf.serviceManager.get(pf.services.CalmDownService);

        this._platform = context.platform || 'pkw';

        // TODO: testing to adapt screen
        pf.app.setCurrentScene(macros.SCENE_NANE);
        this._rebateService = pf.serviceManager.get(pf.services.RebateService);
    }

    protected start(): void {
        pf.UIUtil.adaptWidget(this.node, true);

        this._init();

        // 进入房间数据同步
        if (this._humanboyRoom.isDataSynced) {
            this._onMsgGameDataSyn();
            // this.onCalmDownShowTip(this._humanboyRoom.syncCalmDownParams);
        } else {
            // this._humanboyRoom.addListener('dataSync', this._onMsgGameDataSyn.bind(this));
        }

        // this._humanboyRoom.addListener('calmDown', this._boundCalmDown);
        if (this._humanboyRoom.isDataSynced) {
            this.OnGameDataSynNotify();
        }
    }

    protected onDestroy(): void {
        pf.bundleManager.releaseAll(macros.BUNDLE_NAME);
        this.GetDialogHub().processClose();
    }

    protected update(dt: number): void {
        // 已流逝的时间
        this._msNowTime += dt;

        // 时间差
        let msDuration: number = this._msNowTime - this._msLastTime;

        // 判断调用定时器后的时间（可能调用了几次定时器）是否与调用定时器前的时间相差1s
        if (msDuration >= this._msInterval) {
            // 弥补帧误差
            this._msLastTime = this._msNowTime - (msDuration - this._msInterval);
            --this._nLeftTime;
        }

        this._updateCoinOptimization(dt);
    }

    /**
     * 初始化
     */
    private _init(): void {
        // TODO: testing to shop and adative
        // 隐藏充值web页面
        // cv.SHOP.msgNode.active = false;
        // cv.viewAdaptive.isselfchange = false; // 小游戏点击充值切换场景操作
        // cv.viewAdaptive.humanboyroomid = 0;

        // 设置跑马灯类型
        // cv.pushNotice.setPushNoticeType(PushNoticeType.PUSH_HUMANBOY);

        // 计算全面屏偏移量
        if (pf.system.view.isFullScreen()) {
            const offset = pf.system.view.iphoneXOffset;
            HumanboyControl.g_fullScreenOffset.x = pf.system.view.isScreenLandscape() ? offset : 0;
            HumanboyControl.g_fullScreenOffset.y = pf.system.view.isScreenLandscape() ? 0 : offset;
        }

        this._initAtlasList();
        this._initUI();
        this._initBetAreaLimit();
        this._initJackPot();
        this._initBtnsEvents();
        this._initDealerInfo();
        this._initPlayersInfo();
        this._initBetAreas();
        this._initCoinPool();
        this._initBetButtons();
        this._initTimelineAnims();
        this._initGuide();
        // this._initBtnTest();                                // 初始化测试按钮

        this._adaptiveScreen(); // 适配刘海屏相关控件
        this._fitSafeArea();
        this._initRedPackage(); // 初始化红包按钮入口

        this._adaptiveBetBtnPanel(); // 适配下注按钮面板布局
        this._adaptBtnMenu();
        this._onMsgSoundSwitch(); // 播放背景音乐
        this._addObserver(); // 添加监听事件

        this._resetAllUI(); // 重置UI
    }

    /**
     * 初始化图集
     */
    private _initAtlasList(): void {
        this._atlas_hb_humanboy = pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_ATLAS);
        this._atlas_hb_language = pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_LANGUAGE_ATLAS);
        this._atlas_cb_language = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_LANGUAGE_ATLAS);
    }

    /**
     * 注册监听事件
     */
    private _addObserver(): void {
        this._humanboyRoom.addListener('dataSync', this._onMsgGameDataSyn.bind(this));
        // cv.MessageCenter.register('switchSceneBegan', this._onMsgSwitchSceneBegan.bind(this), this.node); // 切出该场景

        // cv.MessageCenter.register('on_humanboy_sound_switch_notify', this._onMsgSoundSwitch.bind(this), this.node); // 设置声音改变

        // cv.MessageCenter.register('showMedalMsg', this._onMsgRewardTips.bind(this), this.node); // 红包中奖提示
        this._pushNotificationService.addListener('pushNotification', this._boundPushNotification);

        this._luckTurntableService.addListener('luckTurntableStart', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.addListener('luckTurntableEnd', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.addListener('luckTurntableResult', this._boundLuckTurntableResult);

        // this._calmDownService.addListener('calmDown', this._boundCalmDown);

        this._humanboyRoom.addListener('deal', this._onMsgGameDeal.bind(this)); // 新开一局
        this._humanboyRoom.addListener('gameWillStart', this._onMsgWillStartNotify.bind(this)); // 游戏即将开始
        this._humanboyRoom.addListener('startBet', this._onMsgGameStartBet.bind(this)); // 开始下注
        this._humanboyRoom.addListener('gameRoundEnd', this._onMsgGameRoundEnd.bind(this)); // 一局结束
        this._humanboyRoom.addListener('bet', this._onMsgBet.bind(this)); // 下注
        this._humanboyRoom.addListener('autoBet', this._onMsgMergeAutoBetAct.bind(this)); // 续投成功

        this._humanboyRoom.addListener('roomParamChange', this._onMsgRoomParamChange.bind(this)); // 房间状态变更
        // 下注级别变更
        this._humanboyRoom.addListener('betCoinOptionsChange', this._onMsgBetAmountLevelChange.bind(this)); // 下注级别变更
        this._humanboyRoom.addListener('advanceAutoBetCountSet', this._onMsgAdvanceAutobetSet.bind(this)); // 设置高级续投次数成功
        this._humanboyRoom.addListener('advanceAutoBetCountAdd', this.onMsgAdvanceAutobetAdd.bind(this));
        this._humanboyRoom.addListener(
            'advanceAutoBetLimitReached',
            this._onMsgAdvanceAutobetLimitReached.bind(this) // 自动高继续投上线
        );
        this._humanboyRoom.addListener('advanceAutoBetCancel', this._onMsgAdvanceAutobetCancel.bind(this)); // 取消高级续投成功
        this._humanboyRoom.addListener('advanceAutoBet', this._onMsgAdvanceAutobet.bind(this));
        this._humanboyRoom.addListener('kicked', this._onMsgKick.bind(this)); // 服务器踢人
        this._humanboyRoom.addListener('serverError', this._onMsgGameError.bind(this)); // 游戏错误提示

        this._humanboyRoom.addListener('updateDealerList', this._onMsgDealerList.bind(this)); // 上庄列表
        this._humanboyRoom.addListener('upDealerRequest', this._onMsgDealerUp.bind(this)); // 上庄申请
        this._humanboyRoom.addListener('upDealerApproved', this._onMsgDealerUpNotify.bind(this)); // 上庄通知
        this._humanboyRoom.addListener('upDealerCanceled', this._onMsgDealerCancelWait.bind(this)); // 取消等待队列
        this._humanboyRoom.addListener('upDealerKicked', this._onMsgKickDealerApplyNotify.bind(this)); // 上庄失败被踢

        this._humanboyRoom.addListener('downDealerRequest', this._onMsgDealerDown.bind(this)); // 下庄申请
        this._humanboyRoom.addListener('downDealerApproved', this._onMsgDealerDownNotify.bind(this)); // 下庄申请

        this._humanboyRoom.addListener('leftGameCoin', this._onMsgConsumingNotify.bind(this));

        this._humanboyRoom.addListener('leaveRoom', this.exitGame.bind(this));

        // TODO: testing
        pf.app.addListener('appEnterBackground', this._boundEnterBackgroundHandler);
        pf.app.addListener('appEnterForeground', this._boundEnterForegroundHandler);

        this._walletService.addListener('userGoldNum', this._boundUpdateGoldHandler);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        // context.exitCallback = this.exitGame.bind(this);
        context.exitCallback = this.tryLeaveRoom.bind(this);
        this._rebateService.addListener('eventStatusStop', this._boundRebateEventStop);
        this._rebateService.addListener('refreshEventStatus', this._boundRebateEventStatusNotify);
        this._rebateService.addListener('rebateRewardResult', this._boundRebateClaimNotify);
        this._humanboyRoom.addListener('dataSync', this.OnGameDataSynNotify.bind(this));
    }

    /**
     * 移除监听事件
     */
    private _removeObserver(): void {
        // TODO: testing
        pf.app.removeListener('appEnterBackground', this._boundEnterBackgroundHandler);
        pf.app.removeListener('appEnterForeground', this._boundEnterForegroundHandler);

        this._pushNotificationService.removeListener('pushNotification', this._boundPushNotification);
        this._walletService.removeListener('userGoldNum', this._boundUpdateGoldHandler);

        this._luckTurntableService.removeListener('luckTurntableStart', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.removeListener('luckTurntableEnd', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.removeListener('luckTurntableResult', this._boundLuckTurntableResult);

        // this._calmDownService.removeListener('calmDown', this._boundCalmDown);
        this._rebateService.removeListener('eventStatusStop', this._boundRebateEventStop);
        this._rebateService.removeListener('refreshEventStatus', this._boundRebateEventStatusNotify);
        this._rebateService.removeListener('rebateRewardResult', this._boundRebateClaimNotify);
        this._rebateActivity?.node?.off('barMaxAnimationEnd', this._boundBarMaxAnimationEnd);
    }

    /**
     * 游戏进入后台时触发的事件
     */
    OnAppEnterBackground(): void {
        pf.audioManager.stopAll();
        // TODO: OnAppEnterBackgroun
        // const deviceAPI = pf.nativeManager.get<>(pf.natives.DeviceAPI);
        // if(pf.system.isBrowser() && pf.app.clientType )

        // 私语版本, 切回后台后，将所有音频暂停
        // if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.H5WebPage) {
        // if (cc.sys.os == cc.sys.OS_ANDROID) {
        //     cv.AudioMgr.stopMusic();
        //     cv.AudioMgr.pauseAll();
        // } else {
        //     if (!cv.tools.isPlayMusic()) {
        //         cv.AudioMgr.play(macros.Audio.Silence_Music, true, 0.5, true);
        //     }
        // }
        // cv.tools.setEnterbackState(true);
        // //}

        // this._effectMap.forEach((key: string, value: effectLoop, i?: number) => {
        //     cc.audioEngine.stopEffect(value.audioId);
        //     this.unschedule(value.func);
        // });
        // this._effectMap.clear();
        this._isEnterBackground = true;

        // 解决结算飞金币时疯狂秒切前后台卡死的bug, 原因是依赖"this"的定时器回调后金币对象已被销毁
        // 停止根节点所有定时器和动画回调(暂时只能写在房间同步逻辑之前, 否则会造成音效循环播放bug)
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
    }

    /**
     * 游戏进入前台运行时触发的事件
     */
    OnAppEnterForeground(): void {
        pf.audioManager.playMusic(macros.Audio.BGM);
        // if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.H5WebPage) {
        // cv.tools.setEnterbackState(false);
        // if (cc.sys.os == cc.sys.OS_ANDROID) {
        //     cv.AudioMgr.resumeAll();
        //     this._onMsgSoundSwitch();
        // } else {
        //     if (!cv.tools.isPlayMusic()) {
        //         cv.AudioMgr.stop(cv.AudioMgr.getAudioID(macros.Audio.Silence_Music));
        //     }
        // }
        // }
        this._isEnterBackground = false;
    }

    /**
     * 清除数据等(停止一些循环动画,音频之类的资源的状态等,否则直接切换场景会闪退)
     */
    private _clearData(): void {
        // 移除所有监听者
        this._removeObserver();

        // 停止所有音乐播放
        // this._stopSound('', true);

        // 停止背景音乐
        pf.audioManager.stopAll();
        // cv.AudioMgr.stopMusic();

        // 重置UI状态等
        this._resetAllUI();

        // 清除房间数据结构等
        // humanboyDataMgr.getHumanboyRoom().reset();
    }

    /**
     * 返回房间列表
     */
    private _backToRoomListScene(): void {
        // this.exitGame();
        this.tryLeaveRoom();
    }

    /**
     * 返回主场景
     * @param tips
     */
    private _backToMainScene(tips: string): void {
        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        context.backToMainTips = tips;
        // this.exitGame();
        this.tryLeaveRoom();
    }

    /**
     * 重置所有UI
     */
    private _resetAllUI(): void {
        this._resetGameView();
        this._resetOtherView();
        this._updateGameView();
    }

    /**
     * 重置游戏视图
     */
    private _resetGameView(): void {
        this._resetLeftTime();
        this._resetAllCards();

        this._resetAllBetAreaLimitAmount();
        this._resetAllBetAreas();

        this._restAllTimelineAnims();

        this._showBetClockAction(false, false);
        this._showNextRoundCountDown(false, 0);
        this._showNextRoundEnterCountDown(false, 0);
        this._showWaittingTime(false);

        this._nSendCardsCallBackNum = 0;
        this._nMergeAutoBetNum = 0;

        this.resetPointAni();
    }

    /**
     * 重置其他视图
     */
    private _resetOtherView(): void {
        if (this._dealerListView) {
            this._dealerListView.hide(false);
        }
    }

    /**
     * 更新游戏视图
     */
    private _updateGameView(): void {
        this._updateJackPotNum();
        this._updateBetAmountLevel();
        this._updateBetOddsDetail();
        this._updateBetButtonState();
        this._updateBetAreaTouchEnabled();

        this._updateDealerInfo();
        this._updateSelfInfo();
        this._updateOtherPlayersInfo();
        this._updateAllPlayerWinCount();
    }

    /**
     * 创建金币
     * @param gold
     */
    private _createFlyCoin(gold: number): HumanboyBetCoinControl {
        let node: cc.Node = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.BET_COIN));
        node.zIndex = 0;
        node.setAnchorPoint(0.5, 0.5);
        node.setScale(this._fFlyCoinScaleRate);
        node.setPosition(cc.Vec2.ZERO);

        let coin: HumanboyBetCoinControl = node.getComponent(HumanboyBetCoinControl);
        coin.setShape(this._getBetCoinShapeByAmount(gold));
        coin.setTxtNum(pf.StringUtil.serverGoldToShowNumber(gold));
        coin.btn.enabled = false;

        return coin;
    }

    /**
     * 获取金币外形枚举值
     * @param gold
     */
    private _getBetCoinShapeByAmount(gold: number): number {
        let llRealGold: number = pf.StringUtil.clientGoldByServer(gold);
        let shape: number =
            llRealGold < this._humanboyRoom.llCoinUICritical
                ? HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_COIN
                : HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_BLOCK;
        return shape;
    }

    /**
     * 获取指定区域金币的随机位置
     * @param coin
     * @param nAreaIdx
     * @param bWorldPos
     */
    private _getCoinRandomPos(coin: cc.Node, nAreaIdx: number, bWorldPos: boolean): cc.Vec2 {
        if (!coin || nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return cc.Vec2.ZERO;
        let szPanel: cc.Size = this._vAreasInfo[nAreaIdx].panelCoin.getContentSize();
        let szCoin: cc.Size = cc.size(cc.Size.ZERO);
        szCoin.width = coin.width * coin.scaleX;
        szCoin.height = coin.height * coin.scaleY;

        let half_w: number = szPanel.width / 2;
        let half_h: number = szPanel.height / 2;

        let offset_x: number = Math.floor(half_w - szCoin.width / 2);
        let offset_y: number = Math.floor(half_h - szCoin.height / 2);

        // 以中心锚点为原点, 按照方圆随机位置
        let sign_x: number = pf.StringUtil.randomRange(0, 2) < 1 ? -1 : 1;
        let sign_y: number = pf.StringUtil.randomRange(0, 2) < 1 ? -1 : 1;
        let x: number = sign_x * pf.StringUtil.randomRange(0, offset_x);
        let y: number = sign_y * pf.StringUtil.randomRange(0, offset_y);

        let retPos: cc.Vec2 = cc.v2(x, y);
        if (bWorldPos) {
            this._vAreasInfo[nAreaIdx].panelCoin.convertToWorldSpaceAR(retPos, retPos);
        }
        return retPos;
    }

    /**
     * 从对应区域的金币池中获取金币节点
     * @param nAreaIdx
     * @param gold
     */
    private _getCoinFromPool(nAreaIdx: number, gold: number): HumanboyBetCoinControl {
        let coin: HumanboyBetCoinControl = null;
        if (nAreaIdx >= 0 && nAreaIdx < this._vAreasInfo.length) {
            let vCoinQueue: Deque<HumanboyBetCoinControl> = this._vAreasInfo[nAreaIdx].vCoinQueue;

            // 未达区域金币精灵上限, 则创建新的精灵
            if (vCoinQueue.size() < this._nAreaCoinLimitCountMin) {
                coin = this._createFlyCoin(gold);
                coin.node.zIndex = ++this._llCoinPoolZOrderCount;
                coin.node.setPosition(cc.Vec2.ZERO);

                this._nodeCoinPool.addChild(coin.node);
                vCoinQueue.push_back(coin);
            }
            // 达到上限, 从金币精灵池中取(重复利用)
            else {
                // 出队
                coin = vCoinQueue.pop_front();
                coin = this._resetCoin(coin);
                if (coin) {
                    coin.node.zIndex = ++this._llCoinPoolZOrderCount;

                    coin.setShape(this._getBetCoinShapeByAmount(gold));
                    coin.setTxtNum(pf.StringUtil.serverGoldToShowNumber(gold));

                    // 入队
                    vCoinQueue.push_back(coin);
                }
            }
        }
        return coin;
    }

    /**
     * 获取对应区域空闲的金币节点数量
     * @param nAreaIdx
     */
    private _getFreeCoinCountFromPool(nAreaIdx: number): number {
        let nRet = 0;
        if (nAreaIdx >= 0 && nAreaIdx < this._vAreasInfo.length) {
            let vCoinQueue: Deque<HumanboyBetCoinControl> = this._vAreasInfo[nAreaIdx].vCoinQueue;
            for (let i = 0; i < vCoinQueue.size(); ++i) {
                if (!vCoinQueue.at(i).node.active) ++nRet;
            }
        }
        return nRet;
    }

    /**
     * 刷新"金币最优队列"(每帧创建, 稳定帧率)
     */
    private _updateCoinOptimization(dt: number): void {
        let nTotalCount: number = this._vCoinOptimizationDeque.size();
        if (nTotalCount <= 0) return;

        if (this._humanboyRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() >= 0) {
            let nCount = 0;

            // 剩余时间 > 1s 逐帧喷吐金币
            if (this._getLeftTime() > 1) {
                nCount = nTotalCount / cc.game.getFrameRate();
                nCount = Math.ceil(nCount);
            }
            // 否则, 一次性喷吐剩余金币(弥补金币数量多、卡帧导致喷吐金币不完整的情况)
            else {
                nCount = nTotalCount;
            }

            // console.log(pf.StringUtil.formatC("HumanboyGame_Coin: sec = %02d, dt = %05f, total = %05f, count = %05f", this._getLeftTime(), dt, nTotalCount, nCount));

            for (let i = 0; i < nCount; ++i) {
                let t: HumanboyCoinOptimization = this._vCoinOptimizationDeque.pop_front();

                // 投金币动画
                this._showCoinAnim(t.nAreaIdx, t.nGold, t.nUid, t.bAnim, t.bHeadAnim, t.bPlaySound);

                // 更新区域限红
                this._updateBettAreaLimitAmount(this._getAreaIdxByBetOption(t.nAreaIdx), -t.nGold);

                // 更新玩家金币
                this._updatePlayerGold(t.nUid);

                // 自己筹码变化后判断一下下注筹码状态
                if (this._humanboyRoom.selfPlayer.uid === t.nUid) {
                    this._updateBetButtonState();
                }
            }
        } else {
            // 更新剩余的金币数等(在卡帧情况下, 计时误差等情况下, 飞金币被强行停止, 但数据要保持最新, 因为这是一个逐帧队列, 不是及时更新)
            for (let i = 0; i < nTotalCount; ++i) {
                let t: HumanboyCoinOptimization = this._vCoinOptimizationDeque.pop_front();

                // 更新区域限红
                this._updateBettAreaLimitAmount(this._getAreaIdxByBetOption(t.nAreaIdx), -t.nGold);

                // 更新玩家金币
                this._updatePlayerGold(t.nUid);

                // 自己筹码变化后判断一下下注筹码状态
                if (this._humanboyRoom.selfPlayer.uid === t.nUid) {
                    this._updateBetButtonState();
                }
            }

            // 清除队列
            this._vCoinOptimizationDeque.clear();
        }
    }

    /* eslint max-params: ["error", 7] */
    /** 显示投金币动画和头像抖动动画
     * @param nAreaIdx	对应区域索引
     * @param gold		金额
     * @param uid		玩家id
     * @param anim		是否动画(true:动画到目标位置, false: 直接显示到目标位置)
     * @param headAnim	是否显示头像抖动动画
     * @param playSound	是否播放音效
     * @param cb			回调函数
     */
    private _showCoinAnim(
        nAreaIdx: number,
        gold: number,
        uid: number,
        anim: boolean,
        headAnim: boolean,
        playSound: boolean,
        cb: () => void = null
    ): void {
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        // 开始动画
        if (anim) {
            let vPlayerCoinNodes: cc.Node[] = this._getPlayerCoinNodesByUid(uid);
            if (pf.StringUtil.getArrayLength(vPlayerCoinNodes) === 0) {
                console.log(
                    pf.StringUtil.formatC(
                        'HumanboyMainView.showBetInAnim, cannot find valid headBg, use btnPlayerList, oneBet.uid: %d',
                        uid
                    )
                );
                vPlayerCoinNodes.push(this._btn_playerList);
            }

            let coinFlyFromPos: cc.Vec2 = cc.Vec2.ZERO;
            let coinFlyDestPos: cc.Vec2 = cc.Vec2.ZERO;
            for (let i = 0; i < vPlayerCoinNodes.length; ++i) {
                let fromHead: cc.Node = vPlayerCoinNodes[i];
                fromHead.parent.convertToWorldSpaceAR(fromHead.getPosition(), coinFlyFromPos);

                // 发射时头像抖动动画
                if (
                    headAnim &&
                    fromHead !== this._img_self_gold.node &&
                    cc.director.getActionManager().getNumberOfRunningActionsInTarget(fromHead) <= 0
                ) {
                    // if (headAnim && fromHead != this._img_self_gold.node) {
                    let offset_x = 20;
                    let offset_y = 0;
                    let ac: cc.ActionInterval = null;
                    if (coinFlyFromPos.x < cc.winSize.width / 2) {
                        ac = cc.sequence(
                            cc.moveBy(0.1, cc.v2(-offset_x, 0)),
                            cc.moveBy(0.1, cc.v2(offset_x, offset_y)).easing(cc.easeInOut(1.0))
                        );
                    } else {
                        ac = cc.sequence(
                            cc.moveBy(0.1, cc.v2(offset_x, offset_y)),
                            cc.moveBy(0.1, cc.v2(-offset_x, offset_y)).easing(cc.easeInOut(1.0))
                        );
                    }
                    if (ac) {
                        fromHead.runAction(ac);
                    }
                }

                // 富豪和神算子是自己的情况，只下一个金币和播放一次音效
                if (uid === this._authService.currentUser.userId && i > 0) continue;

                // 飞金币动画方式
                do {
                    let coin: HumanboyBetCoinControl = this._getCoinFromPool(nAreaIdx, gold);
                    if (coin) {
                        coin.node.setPosition(coin.node.parent.convertToNodeSpaceAR(coinFlyFromPos));
                        if (i === 0) {
                            // 下注音效
                            if (playSound) {
                                let llRealGold: number = pf.StringUtil.clientGoldByServer(gold);
                                let sound: string =
                                    llRealGold < this._humanboyRoom.llCoinUICritical
                                        ? macros.Audio.Betin
                                        : macros.Audio.Betin_Many;

                                if (sound !== this._lastSoundName || Date.now() - this._lastSoundTime > 200) {
                                    this._lastSoundTime = Date.now();
                                    this._lastSoundName = sound;
                                    this._playSoundEffect(sound);
                                }
                            }

                            // 动画
                            coinFlyDestPos = this._getCoinRandomPos(coin.node, nAreaIdx, true);
                            coin.node.parent.convertToNodeSpaceAR(coinFlyDestPos, coinFlyDestPos);

                            coin.node.active = true;
                            coin.node.runAction(
                                cc.sequence(
                                    // cc.moveTo(0.3, coinFlyDestPos).easing(cc.easeSineOut()),
                                    cc.moveTo(0.3, coinFlyDestPos),
                                    cc.rotateBy(0.15, 180),
                                    cc.rotateBy(0.15, 180),
                                    cc.callFunc(cb, this)
                                )
                            );
                        } else {
                            // 动画
                            coin.node.active = true;
                            coin.node.runAction(
                                cc.sequence(
                                    // cc.moveTo(0.3, coinFlyDestPos).easing(cc.easeSineOut()),
                                    cc.moveTo(0.3, coinFlyDestPos),
                                    cc.rotateBy(0.15, 180),
                                    cc.rotateBy(0.15, 180),
                                    cc.callFunc((): void => {
                                        coin.node.active = false;
                                    })
                                )
                            );
                        }
                    }
                } while (0);
            }
        } else {
            let coin: HumanboyBetCoinControl = this._getCoinFromPool(nAreaIdx, gold);
            if (coin) {
                let coinFlyDestPos: cc.Vec2 = this._getCoinRandomPos(coin.node, nAreaIdx, true);
                coin.node.setPosition(coin.node.parent.convertToNodeSpaceAR(coinFlyDestPos));
                coin.node.active = true;
            }

            if (cb) cb();
        }
    }

    /**
     * 从指定位置朝目标区域飞金币动画
     * @param world_pos
     * @param nAreaIdx
     * @param gold
     * @param anim
     * @param fDelayedTime
     * @param cb
     */
    private _showCoinAnimFromPos(
        world_pos: cc.Vec2,
        nAreaIdx: number,
        gold: number,
        anim: boolean,
        fDelayedTime: number,
        cb: () => void = null
    ): void {
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let coin: HumanboyBetCoinControl = this._getCoinFromPool(nAreaIdx, gold);
        if (!coin) return;

        let coinFlyFromPos: cc.Vec2 = cc.Vec2.ZERO;
        coin.node.parent.convertToNodeSpaceAR(world_pos, coinFlyFromPos);
        let coinFlyDestPos: cc.Vec2 = this._getCoinRandomPos(coin.node, nAreaIdx, true);
        coin.node.parent.convertToNodeSpaceAR(coinFlyDestPos, coinFlyDestPos);
        if (anim) {
            coin.node.setPosition(coinFlyFromPos);
            this.scheduleOnce((elapsed: number): void => {
                coin.node.active = true;
                coin.node.runAction(
                    cc.sequence(
                        cc.moveTo(0.5, coinFlyDestPos).easing(cc.easeOut(0.8)),
                        cc.rotateBy(0.15, 180),
                        cc.rotateBy(0.15, 180),
                        cc.callFunc(cb, this)
                    )
                );
            }, fDelayedTime);
        } else {
            coin.node.active = true;
            coin.node.setPosition(coinFlyDestPos);
            if (cb) cb();
        }
    }

    /**
     * 重置指定金币节点
     * @param coin
     */
    private _resetCoin(coin: HumanboyBetCoinControl): HumanboyBetCoinControl {
        if (!coin || !cc.isValid(coin, true)) return null;

        coin.node.zIndex = 0;
        coin.node.opacity = 0xff;
        coin.node.angle = 0;
        coin.node.setPosition(cc.Vec2.ZERO);
        coin.node.stopAllActions();
        coin.node.active = false;

        coin.txtBetNode.opacity = 0xff;
        coin.txtBetNode.active = true;

        coin.btn.enabled = false;
        coin.imgMask.node.active = false;

        return coin;
    }

    /**
     * 重置指定下注区域的所有金币节点
     * @param betOption
     */
    private _resetBetAreaCoins(betOption: network.BetZoneOption): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;
        let count: number = this._vAreasInfo[nAreaIdx].vCoinQueue.size();
        for (let i = 0; i < count; ++i) {
            let coin: HumanboyBetCoinControl = this._vAreasInfo[nAreaIdx].vCoinQueue.at(i);
            this._resetCoin(coin);
        }
    }

    /**
     * 重置所有下注区域的金币节点
     */
    private _resetAllBetAreaCoins(): void {
        for (const area of this._vAreasInfo) {
            this._resetBetAreaCoins(area.eZone);
        }

        // 重置金币池节点深度计数
        this._llCoinPoolZOrderCount = 0;

        // 清理"金币最优队列"
        this._vCoinOptimizationDeque.clear();
    }

    /**
     * 恢复所有区域的金币显示与否
     * @param bShowCoin
     */
    private _recoverAreasCoin(bShowCoin: boolean): void {
        this._humanboyRoom.betZones.forEach((betZone: domain.BetZone, option: network.BetZoneOption) => {
            let nAreaIdx: number = this._getAreaIdxByBetOption(option);
            if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

            let llTotalAmount = 0;
            let vTotalBetDetail: number[] = betZone.optionInfo.amounts;

            for (const detail of vTotalBetDetail) {
                llTotalAmount += detail;
                if (bShowCoin) {
                    this._showCoinAnim(nAreaIdx, detail, 0, false, false, false);
                }
            }

            // 更新区域限红
            this._updateBettAreaLimitAmount(option, -llTotalAmount);
        });
    }

    /**
     * 获取当前选中的下注额
     */
    private _getCurBetLevel(): number {
        if (this._nCurBetBtnIndex < 0) return 0;
        return this._humanboyRoom.betSettings.betCoinOptions[this._nCurBetBtnIndex];
        //     let vBetCoinOption: number[] = this._humanboyRoom.betSettings.betCoinOptions;
        //     return vBetCoinOption[this._nCurBetBtnIndex];
    }

    /**
     * 通过下注选项获取下注区域索引
     * @param betOption
     */
    private _getAreaIdxByBetOption(betOption: number): number {
        for (const area of this._vAreasInfo) {
            if (area.eZone === betOption) {
                return area.index;
            }
        }
        return -1;
    }

    /**
     * 通过下注区域索引获取下注选项
     * @param betOption
     */
    private _getBetOptionByAreaIdx(areaIdx: number): network.BetZoneOption {
        for (const area of this._vAreasInfo) {
            if (area.index === areaIdx) {
                return area.eZone;
            }
        }
        return network.BetZoneOption.BetZoneOption_DUMMY;
    }

    /**
     * 飞金币动画以金币精灵为起始点/终点
     * @param uid
     */
    private _getPlayerCoinNodesByUid(uid: number): cc.Node[] {
        let ret: cc.Node[] = [];
        // 富豪和神算子是自己时, 记录一次
        if (uid === this._authService.currentUser.userId) {
            ret.push(this._img_self_gold.node);
        }

        // 左右侧玩家列表, 再次检测记录一次
        for (const info of this._vOtherPlayerInfo) {
            if (uid > 0 && info.uid === uid) {
                ret.push(info.nodeHead);
            }
        }

        return ret;
    }

    /**
     * 飞金币动画以头像精灵为起始点/终点
     * @param uid
     */
    private _getPlayerHeadNodesByUid(uid: number): cc.Node[] {
        let ret: cc.Node[] = [];

        // 富豪和神算子是自己时, 记录一次
        if (uid === this._authService.currentUser.userId) {
            ret.push(this._img_self_head.node);
        }

        // 左右侧玩家列表, 再次检测记录一次
        for (const info of this._vOtherPlayerInfo) {
            if (uid > 0 && info.uid === uid) {
                ret.push(info.nodeHead);
            }
        }

        return ret;
    }

    /**
     * 根据下注金额拆分多个筹码金额
     * @param gold
     */
    private _getBetDetailAmounts(gold: number): number[] {
        let vAmountLevels: number[] = this._humanboyRoom.roomParams.amountLevel;
        return MiniGameCommonDef.disinteBetAmounts(gold, vAmountLevels);
    }

    // playEffectForPath(path: string): void {
    //     if (this._effectMap.has(path)) {
    //         let obj = this._effectMap.get(path);
    //         let state = cc.audioEngine.getState(obj.audioId);
    //         let isPlay: boolean = state == cc.audioEngine.AudioState.PLAYING;
    //         if (obj.bGoOn === true && isPlay) return;
    //         let currentTime: number = new Date().getTime();
    //         if (isPlay === false) {
    //             if (state == cc.audioEngine.AudioState.PAUSED) {
    //                 console.log(' PAUSED', ', ', obj);
    //                 cc.audioEngine.resumeEffect(obj.audioId);

    //                 obj.startPlayTime = currentTime;
    //                 this.schedule(obj.func, obj.duringTime);
    //             } else {
    //                 console.log(' !PAUSED state = ', state, ', ', obj);
    //             }
    //             return;
    //         }
    //         console.log(' PLAYING', ', ', obj);
    //         if (currentTime > obj.startPlayTime + obj.duringTime * 0.5 * 1000) {
    //             obj.bGoOn = true;
    //         }
    //     } else {
    //         let clip: cc.AudioClip = cv.resMgr.get(path, cc.AudioClip);
    //         let audioId = cc.audioEngine.playEffect(clip, true);
    //         let duringTime = cc.audioEngine.getDuration(audioId);
    //         let obj = new effectLoop();
    //         obj.audioId = audioId;
    //         obj.bGoOn = false;
    //         obj.duringTime = duringTime;
    //         obj.startPlayTime = new Date().getTime();
    //         obj.func = (delay: number) => {
    //             let obj = this._effectMap.get(path);
    //             obj.startPlayTime = new Date().getTime();
    //             if (obj.bGoOn === false) {
    //                 cc.audioEngine.pauseEffect(obj.audioId);
    //                 console.log('yyx123 setCurrentTime');
    //                 cc.audioEngine.setCurrentTime(obj.audioId, 0);
    //                 this.unschedule(obj.func);
    //             }
    //             obj.bGoOn = false;
    //         };
    //         this._effectMap.set(path, obj);
    //         cc.audioEngine.setFinishCallback(audioId, () => {
    //             console.log('yyx123 setFinishCallback');
    //             cc.audioEngine.stopEffect(audioId);
    //             this.unschedule(obj.func);
    //             this._effectMap.delete(path);
    //         });

    //         this.schedule(obj.func, obj.duringTime);
    //     }
    // }

    ingorePutInQuenue(fileName: string) {
        // 私语平台，开始下注，停止下注不放在队列播放
        if (
            fileName !== macros.Audio.Begin_Bet &&
            fileName !== macros.Audio.End_Bet &&
            fileName !== macros.Audio.Win_Lose &&
            fileName !== macros.Audio.Special_Card_Type_Big &&
            fileName !== macros.Audio.Time_Tick
        ) {
            return true;
        }

        return false;
    }

    /**
     * 播放音效
     * @param fileName
     * @param loop
     */
    private _playSoundEffect(fileName: string, loop: boolean = false): void {
        pf.audioManager.playSoundEffect(fileName, loop);
        // if (cv.tools.isSoundEffectOpen() && this._isEnterBackground == false) {
        //     if (!this._mapSounds.has(fileName)) {
        //         this._mapSounds.set(fileName, true);
        //     }

        //     if (cc.sys.isBrowser && this.ingorePutInQuenue(fileName)) {
        //         this.playEffectForPath(fileName);
        //     } else {
        //         cv.AudioMgr.playEffect(fileName, loop);
        //     }
        // }
    }

    /**
     * 暂停音频
     * @param fileName
     * @param all
     */
    // private _pauseSound(fileName: string, all: boolean = false): void {
    //     if (all) {
    //         this._mapSounds.forEach((key: string, value: boolean, i?: number): any => {
    //             let audioID: number = cv.AudioMgr.getAudioID(key);
    //             cv.AudioMgr.pause(audioID);
    //         });
    //     } else {
    //         if (!this._mapSounds.has(fileName)) return;
    //         let audioID: number = cv.AudioMgr.getAudioID(fileName);
    //         cv.AudioMgr.pause(audioID);
    //     }
    // }

    /**
     * 恢复音频
     * @param fileName
     * @param all
     */
    // private _resumeSound(fileName: string, all: boolean = false): void {
    //     if (all) {
    //         this._mapSounds.forEach((key: string, value: boolean, i?: number): any => {
    //             let audioID: number = cv.AudioMgr.getAudioID(key);
    //             cv.AudioMgr.resume(audioID);
    //         });
    //     } else {
    //         if (!this._mapSounds.has(fileName)) return;
    //         let audioID: number = cv.AudioMgr.getAudioID(fileName);
    //         cv.AudioMgr.resume(audioID);
    //     }
    // }

    /**
     * 停止播放音频
     * @param fileName
     * @param all
     */
    // private _stopSound(fileName: string, all: boolean = false): void {
    //     if (all) {
    //         this._mapSounds.forEach((key: string, value: boolean, i?: number): any => {
    //             let audioID: number = cv.AudioMgr.getAudioID(key);
    //             cv.AudioMgr.stop(audioID);
    //         });
    //     } else {
    //         if (!this._mapSounds.has(fileName)) return;
    //         let audioID: number = cv.AudioMgr.getAudioID(fileName);
    //         cv.AudioMgr.stop(audioID);
    //     }
    // }

    /**
     * 动态游戏提示
     * @param strText
     */
    private _showGameToast(strText: string): void {
        let toast: HumanboyToastControl = cc
            .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.HUMANBOY_TOAST))
            .getComponent(HumanboyToastControl);
        toast.txt.string = strText;
        this.node.addChild(toast.node, eHumanboyLocalZorder.HL_ZORDER_PANEL_SERVER_TOAST);

        let seq_move: cc.ActionInterval = cc.sequence(cc.delayTime(0.1), cc.moveBy(1.0, cc.v2(0, 120)));
        let seq_fade: cc.ActionInterval = cc.sequence(
            cc.delayTime(0.4),
            cc.fadeOut(0.8).easing(cc.easeInOut(1.0)),
            cc.destroySelf()
        );
        toast.node.runAction(seq_move);
        toast.node.runAction(seq_fade);
    }

    /**
     * 设置筹码单选按钮选中状态
     * @param betBtnIdx
     * @param isCheckCoin
     */
    private _setBetButtonSelected(betBtnIdx: number, isCheckCoin: boolean = true): void {
        if (betBtnIdx < 0 || betBtnIdx >= this._nBetBtnNum) return;

        this._resetAllBetButtons(true);
        this._updateBetButtonState();

        let vBetCoinOption: number[] = this._humanboyRoom.betSettings.betCoinOptions; // 房间下注级别
        let curCoin: number = this._humanboyRoom.selfPlayer.curCoin; // 当前自身携带金币

        if (betBtnIdx >= 0 && betBtnIdx < this._nBetBtnNum) {
            // 钱是否够按钮上的金额
            if (isCheckCoin) {
                let llAmountLevel = 0;
                if (betBtnIdx < vBetCoinOption.length) {
                    llAmountLevel = vBetCoinOption[betBtnIdx];
                }

                if (curCoin > 0 && curCoin >= llAmountLevel) {
                    this._nCurBetBtnIndex = betBtnIdx;
                    this._vBetButtons[betBtnIdx].node.setScale(this._fBetBtnTarScaleRate);
                }
            } else {
                this._nCurBetBtnIndex = betBtnIdx;
                this._vBetButtons[betBtnIdx].node.setScale(this._fBetBtnTarScaleRate);
            }
        }
    }

    /**
     * 初始化UI
     */
    private _initUI(): void {
        this._panel_top = this.node.getChildByName('panel_top');
        this._panel_bottom = this.node.getChildByName('panel_bottom');

        this._btn_menu = this.node.getChildByName('btn_menu').getComponent(cc.Button);
        this._btn_record = this.node.getChildByName('btn_record').getComponent(cc.Button);

        this._panel_jackpot = this._panel_top.getChildByName('panel_jackpot');
        this._panel_dealer_extra = this._panel_top.getChildByName('panel_dealer_extra');

        this._panel_self = this._panel_bottom.getChildByName('panel_self');
        this._panel_betbtn = this._panel_bottom.getChildByName('panel_betbtn');

        this._panel_left_playerlist = this.node.getChildByName('panel_left_playerlist');
        this._panel_right_playerlist = this.node.getChildByName('panel_right_playerlist');

        this._img_count_down = this.node.getChildByName('img_count_down').getComponent(cc.Sprite);
        this._img_count_down.node.zIndex = eHumanboyLocalZorder.HL_ZORDER_PANEL_COUNT_DOWN;
        this._img_count_down.node.active = false;

        const canvas = this.getComponent(cc.Canvas);

        const fitScale = canvas.fitWidth
            ? pf.system.view.width / canvas.designResolution.width
            : pf.system.view.height / canvas.designResolution.height;

        // 百人只支持横屏(所以这里所有检测都是建立在横屏基础上)
        // 计算目标设备分辨率用哪一套UI(目前总共3套: 常规, 窄屏, 宽屏)
        // 以下计算宽窄屏要以实际UI对应的宽高总和与屏幕对应的宽高做比较
        // 例如: 窄屏判断逻辑是 h : w > 2; 但若干机型满足条件的同时实际屏幕宽却比游戏的ui累加宽小, 就摆不下了, 此时认为该窄屏为"伪窄屏"
        // 因此要根据实际游戏核心排版元素去计算作比较(宽屏逻辑也以此类推)
        do {
            let panel_game: cc.Node = this.node.getChildByName('panel_game');
            let panel_game_broad: cc.Node = this.node.getChildByName('panel_game_broad');
            let panel_game_narrow: cc.Node = this.node.getChildByName('panel_game_narrow');
            this._eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL;

            // 窄屏检测

            if (pf.system.view.isNarrowScreen()) {
                // let panel_left_playerlist: cc.Node = this.node.getChildByName('panel_left_playerlist');
                // let panel_right_playerlist: cc.Node = this.node.getChildByName('panel_right_playerlist');

                // 左右列表宽度 + 桌布宽度 + 两边刘海宽度 + 缓冲宽度
                let total_w = 0;
                let offset_w = 0;
                total_w += 2 * HumanboyControl.g_fullScreenOffset.x;
                total_w += panel_game_narrow.width;
                total_w += this._panel_left_playerlist.width - 50;
                total_w += this._panel_right_playerlist.width - 50;
                total_w += offset_w;

                if (total_w * fitScale <= pf.system.view.width) {
                    this._eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW;
                }
            }
            // 宽屏检测
            else if (pf.system.view.isWideScreen()) {
                // 顶栏面板高度 + 桌布高度 + 底栏面板高度 + 缓冲高度
                let total_h = 0;
                let offset_h = 0;

                total_h += this._panel_top.height;
                total_h += panel_game_broad.height;
                total_h += this._panel_bottom.height;
                total_h += offset_h;

                if (total_h * fitScale <= pf.system.view.height) {
                    this._eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD;
                }
            }

            switch (this._eGameboyScreenType) {
                case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD:
                    panel_game.removeFromParent(true);
                    pf.UIUtil.destroyNode(panel_game);
                    panel_game_narrow.removeFromParent(true);
                    pf.UIUtil.destroyNode(panel_game_narrow);
                    this._panel_game = panel_game_broad;

                    pf.addressableAssetManager
                        .loadAsset(macros.Dynamic_Assets.HUMANBOY_TABLE_BOARD_SPRITE)
                        .then((asset: cc.SpriteFrame) => {
                            this._panel_game.getComponent(cc.Sprite).spriteFrame = asset;
                        });
                    break;

                case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW:
                    panel_game.removeFromParent(true);
                    panel_game_broad.removeFromParent(true);
                    pf.UIUtil.destroyNode(panel_game);
                    pf.UIUtil.destroyNode(panel_game_broad);
                    this._panel_game = panel_game_narrow;
                    pf.addressableAssetManager
                        .loadAsset(macros.Dynamic_Assets.HUMANBOY_TABLE_NARROW_SPRITE)
                        .then((asset: cc.SpriteFrame) => {
                            this._panel_game.getComponent(cc.Sprite).spriteFrame = asset;
                        });
                    break;

                case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL:
                default:
                    panel_game_broad.removeFromParent(true);
                    panel_game_narrow.removeFromParent(true);
                    pf.UIUtil.destroyNode(panel_game_broad);
                    pf.UIUtil.destroyNode(panel_game_narrow);
                    this._panel_game = panel_game;
                    pf.addressableAssetManager
                        .loadAsset(macros.Dynamic_Assets.HUMANBOY_TABLE_SPRITE)
                        .then((asset: cc.SpriteFrame) => {
                            this._panel_game.getComponent(cc.Sprite).spriteFrame = asset;
                        });
                    break;
            }

            this._panel_game.active = true;
        } while (false);
    }

    /**
     * 初始化引导面板
     */
    private _initGuide(): void {
        let strStoreGuideKey = 'humanboy_has_show_guide';
        if (pf.localStorage.getItem(strStoreGuideKey) !== 'true') {
            if (!this._humanboyGuide) {
                this._humanboyGuide = cc
                    .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_GUIDE))
                    .getComponent(MiniGameGuideControl);
                this.node.addChild(this._humanboyGuide.node, eHumanboyLocalZorder.HL_ZORDER_PANEL_GUID);
            }

            this._humanboyGuide.setTouchSizeScale(0, 3);
            this._humanboyGuide.setDescString(pf.languageManager.getString('Humanboy_game_guide_text'));
            this._humanboyGuide.show(
                this._vAreasInfo[0].panelWayOut,
                (): void => {
                    let hasShowGuide = 'true';
                    pf.localStorage.setItem(strStoreGuideKey, hasShowGuide);
                    this._showChart(network.BetZoneOption.POS1);
                },
                true
            );
        }
    }

    /**
     * 初始化JP入口显示
     */
    private _initJackPot(): void {
        let jp_digits_count = 7;
        for (let i = 0; i < jp_digits_count; ++i) {
            let panel_jackpot: cc.Node = this._panel_jackpot.getChildByName('panel');
            let jpNumber_inst: cc.Node = cc.instantiate(this.prefab_dz_jackPotNumber);
            panel_jackpot.addChild(jpNumber_inst);

            let pJackpotNumber: JackPotNumberControl = jpNumber_inst.getComponent(JackPotNumberControl);
            pJackpotNumber.init(false);
            for (let j = 0; j < 10; ++j) {
                let fileName: string = pf.StringUtil.formatC('um_%d', j);
                let frame: cc.SpriteFrame = pf.addressableAssetManager
                    .getAsset<cc.SpriteAtlas>(macros.Assets.HUMANBOY_NUMBER_ATLAS)
                    .getSpriteFrame(fileName);
                pJackpotNumber.setNumberImg(j, frame);
            }

            pJackpotNumber.setGameStyle(0.31);
            let part_w: number = pJackpotNumber.root.width * pJackpotNumber.root.scaleX;
            let part_h: number = pJackpotNumber.root.height * pJackpotNumber.root.scaleY;
            let offset: number = (panel_jackpot.width - part_w * jp_digits_count) / (jp_digits_count - 1);
            let x: number =
                -panel_jackpot.width * panel_jackpot.anchorX +
                part_w * pJackpotNumber.root.anchorX +
                (part_w + offset) * i;
            let y = 0;
            pJackpotNumber.node.setPosition(cc.v2(x, y));
            pJackpotNumber.hideBg();

            this._vJackPotNumberList.push(pJackpotNumber);
        }
    }

    /**
     * 初始化按钮事件
     */
    private _initBtnsEvents(): void {
        // 菜单按钮
        do {
            this._btn_menu.node.on('click', (event: cc.Event): void => {
                pf.audioManager.playSoundEffect(macros.Audio.Button);

                if (!this._humanboyMenu) {
                    if (this._platform === 'pkw') {
                        this._humanboyMenu = cc
                            .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_MENU))
                            .getComponent(MiniGameMenuControl);
                    } else if (this._platform === 'wpk') {
                        this._humanboyMenu = cc
                            .instantiate(
                                pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_MENU_WITHOUT_EXCHANGE)
                            )
                            .getComponent(MiniGameMenuControl);
                    }

                    this.node.addChild(this._humanboyMenu.node, eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING);

                    // 菜单 - 兑换
                    this._humanboyMenu.getBtnExchange().node.on('click', (event: cc.Event): void => {
                        this._playSoundEffect(macros.Audio.Button);
                        this._humanboyMenu.hide(false);

                        if (this._walletService.getWallet().usdt <= 0) {
                            cr.commonResourceAgent.toastMessage.showMsg(
                                pf.languageManager.getString('USDTView_ex_coin_error_0_usdt')
                            );
                            return;
                        }

                        if (!this._humanboyExchange) {
                            // this._humanboyExchange = cc
                            //     .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_EXCHANGE))
                            //     .getComponent(MiniGameExchangeControl);
                            pf.addressableAssetManager
                                .loadAsset(macros.Dynamic_Assets.MINI_GAME_EXCHANGE)
                                .then((asset: cc.Prefab) => {
                                    this._humanboyExchange = cc
                                        .instantiate(asset)
                                        .getComponent(MiniGameExchangeControl);
                                    this.node.addChild(
                                        this._humanboyExchange.node,
                                        eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING
                                    );
                                });
                        } else {
                            this._humanboyExchange.openView();
                        }
                    });

                    // 菜单 - 规则
                    this._humanboyMenu.getBtnRule().node.on('click', (event: cc.Event): void => {
                        this._playSoundEffect(macros.Audio.Button);
                        this._humanboyMenu.hide(false);

                        if (!this._humanboyRule) {
                            // this._humanboyRule = cc
                            //     .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_RULE))
                            //     .getComponent(MiniGameRuleControl);
                            // this.node.addChild(this._humanboyRule.node, eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING);
                            pf.addressableAssetManager
                                .loadAsset(macros.Dynamic_Assets.MINI_GAME_RULE)
                                .then((asset: cc.Prefab) => {
                                    this._humanboyRule = cc.instantiate(asset).getComponent(MiniGameRuleControl);
                                    this.node.addChild(
                                        this._humanboyRule.node,
                                        eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING
                                    );
                                    this._humanboyRule.openView(macros.RULE_URL);
                                });
                        } else this._humanboyRule.openView(macros.RULE_URL);
                    });

                    // 菜单 - 音效设置
                    this._humanboyMenu.getBtnSoundSetting().node.on('click', (event: cc.Event): void => {
                        this._playSoundEffect(macros.Audio.Button);
                        this._humanboyMenu.hide(false);

                        if (!this._humanboyAudioSetting) {
                            // this._humanboyAudioSetting = cc
                            //     .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_AUDIO_SETTING))
                            //     .getComponent(MiniGameAudioSettingControl);
                            // this.node.addChild(
                            //     this._humanboyAudioSetting.node,
                            //     eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING
                            // );
                            pf.addressableAssetManager
                                .loadAsset(macros.Dynamic_Assets.MINI_GAME_AUDIO_SETTING)
                                .then((asset: cc.Prefab) => {
                                    this._humanboyAudioSetting = cc
                                        .instantiate(asset)
                                        .getComponent(MiniGameAudioSettingControl);
                                    this.node.addChild(
                                        this._humanboyAudioSetting.node,
                                        eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING
                                    );
                                });
                        } else {
                            this._humanboyAudioSetting.initSwitch();
                            this._humanboyAudioSetting.node.active = true;
                        }
                    });

                    // 菜单 - 高级设置
                    this._humanboyMenu.getBtnAdvancedSetting().node.on('click', (event: cc.Event): void => {
                        this._playSoundEffect(macros.Audio.Button);
                        this._humanboyMenu.hide(false);

                        if (!this._humanboyAdvancedSetting) {
                            // this._humanboyAdvancedSetting = cc
                            //     .instantiate(
                            //         pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_ADVANCED_SETTING)
                            //     )
                            //     .getComponent(MiniGameAdvancedSettingControl);
                            // this.node.addChild(
                            //     this._humanboyAdvancedSetting.node,
                            //     eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING
                            // );
                            pf.addressableAssetManager
                                .loadAsset(macros.Dynamic_Assets.MINI_GAME_ADVANCED_SETTING)
                                .then((asset: cc.Prefab) => {
                                    this._humanboyAdvancedSetting = cc
                                        .instantiate(asset)
                                        .getComponent(MiniGameAdvancedSettingControl);
                                    this.node.addChild(
                                        this._humanboyAdvancedSetting.node,
                                        eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING
                                    );
                                    this._humanboyAdvancedSetting.show();
                                });
                        } else this._humanboyAdvancedSetting.show();
                    });

                    // 菜单 - 退出
                    this._humanboyMenu.getBtnExit().node.on('click', (event: cc.Event): void => {
                        this._playSoundEffect(macros.Audio.Button);
                        this._humanboyMenu.hide(false);

                        let iUsedAutoBetCount: number = this._humanboyRoom.betSettings.usedAutoBetCount;
                        let iSelectAutoBetCount: number = this._humanboyRoom.betSettings.selectAutoBetCount;
                        if (iSelectAutoBetCount > 0) {
                            let dialogNode = cc.instantiate(
                                pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_DIALOG)
                            );
                            const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                            this.node.addChild(dialogNode, eHumanboyLocalZorder.HL_ZORDER_PANEL_SERVER_TOAST);

                            const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                            const stringContent = pf.StringUtil.formatC(
                                pf.languageManager.getString('Cowboy_auto_bet_exit_tips'),
                                iUsedAutoBetCount,
                                iSelectAutoBetCount
                            );
                            const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_exit_game');
                            const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_resume_game');
                            const cbLeftBtn = (dialog: IMiniGameDialog) => {
                                // this.exitGame();
                                this.tryLeaveRoom();
                            };
                            const cbRightBtn = (dialog: IMiniGameDialog) => {
                                miniGameDialog?.close();
                            };
                            const stringCenter = pf.languageManager.getString('MiniGame_AddAutoBet_Text');
                            const cbCenterBtn = (dialog: MiniGameDialog) => {
                                this.showAutoAddBetList(dialog);
                            };

                            const _onUpdateContent = (dialog: IMiniGameDialog) => {
                                if (legacyDialog) {
                                    legacyDialog.txt_content.string = pf.StringUtil.calculateAutoWrapString(
                                        legacyDialog.txt_content.node,
                                        pf.StringUtil.formatC(
                                            pf.languageManager.getString('Cowboy_auto_bet_exit_tips'),
                                            this._humanboyRoom.betSettings.usedAutoBetCount,
                                            this._humanboyRoom.betSettings.selectAutoBetCount
                                        )
                                    );
                                }

                                if (this._humanboyRoom.betSettings.reachLimitBet) {
                                    miniGameDialog.blockCenterButton();
                                }
                            };
                            const miniGameDialogConfig: IMiniGameDialogConfig = {
                                miniDialog: miniGameDialog,
                                stringContent,
                                stringLeftBtn,
                                stringRightBtn,
                                cbLeftBtn,
                                cbRightBtn,
                                isReachedMax: this._humanboyRoom.betSettings.reachLimitBet,
                                legacyDialog,
                                isShowBtnCenter: true,
                                stringCenterButton: stringCenter,
                                cbCenterBtn,
                                onUpdateContent: _onUpdateContent
                            };

                            ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                        } else {
                            // const nodeExit: cc.Node = cc.instantiate(
                            //     pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_EXIT)
                            // );
                            // this.node.addChild(nodeExit, eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING);
                            pf.addressableAssetManager
                                .loadAsset(macros.Dynamic_Assets.MINI_GAME_EXIT)
                                .then((asset: cc.Prefab) => {
                                    const nodeExit: cc.Node = cc.instantiate(asset);
                                    this.node.addChild(nodeExit, eHumanboyLocalZorder.HL_ZORDER_PANEL_SETTING);
                                });
                        }
                    });
                }

                this._humanboyMenu.show(true);
                this._humanboyMenu.setMenuPosition(
                    cc.v2(this._btn_menu.node.x, this._btn_menu.node.y - this._btn_menu.node.height / 2)
                );
            });
        } while (0);

        // 玩家列表按钮
        do {
            this._btn_playerList = this._panel_bottom.getChildByName('btn_playerlist');
            this._btn_playerList.on('click', (event: cc.Event): void => {
                this._playSoundEffect(macros.Audio.Button);
                this._humanboyRoom.getPlayerList().then((resp) => {
                    this._onMsgPlayerList(resp.players, resp.playerNum);
                });
            });
        } while (0);

        // 表格记录按钮
        do {
            let btn_record: cc.Node = this.node.getChildByName('btn_record');
            btn_record.on('click', (event: cc.Event): void => {
                this._playSoundEffect(macros.Audio.Button);
                this._showChart(network.BetZoneOption.BetZoneOption_DUMMY);
            });
        } while (0);

        // jackpot
        do {
            this._panel_jackpot.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
                pf.audioManager.playSoundEffect(macros.Audio.Button);
                this._playSoundEffect(macros.Audio.Button);

                if (!this._humanboyJackpot) {
                    this._humanboyJackpot = cc.instantiate(this.prefab_hb_jackPot).getComponent(HumanboyJackpotControl);
                    this.node.addChild(this._humanboyJackpot.node, eHumanboyLocalZorder.HL_ZORDER_PANEL_JACKPOT);
                    this._humanboyJackpot.show(true);
                    // this._humanboyJackpot.node.setPosition(cc.v2(this._humanboyJackpot.node.x, this._humanboyJackpot.node.y));
                } else {
                    // this._humanboyJackpot.node.active = true;
                    this._humanboyJackpot.show(true);
                    if (
                        this._humanboyJackpot.getViewType() ===
                        HumanboyJackpotControl.eHumanboyJackpotListViewType.JACKPOT_TYPE_RULE
                    ) {
                        this._humanboyJackpot.setViewType(this._humanboyJackpot.getViewType());
                    }
                }
                // this._humanboyJackpot.setShieldLayerEnabled(true);
            });
        } while (0);
    }

    private advanceAutoAddBet: cc.Node = null;
    private showAutoAddBetList(dialog: MiniGameDialog) {
        if (!this.advanceAutoAddBet) {
            this.advanceAutoAddBet = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_ADVANCED_AUTO)
            );
            this.node.addChild(this.advanceAutoAddBet, eHumanboyLocalZorder.HL_ZORDER_ADVANCE_AUTO_ADD_SELECT);
        }
        const miniGameAdvanceAuto = this.advanceAutoAddBet.getComponent(MiniGameAdvancedAuto);
        const advanceAuto = new ConcreteAdvancedAuto(miniGameAdvanceAuto);
        cc.log('showAutoAddBetList', advanceAuto, miniGameAdvanceAuto);
        advanceAuto.adaptSelectPanelPos(dialog.btn_center.node);
        advanceAuto.showSelectPanel(true);
        advanceAuto.setCountUpdateCallback(() => {
            dialog.updateCenterButton();
        });
    }

    /**
     * 更新 JP 数额
     */
    private _updateJackPotNum(): void {
        let llJackpotLeftMoney: number = this._humanboyRoom.jackpot.leftMoney;
        let strAmount: string = Math.round(pf.StringUtil.serverGoldToShowNumber(llJackpotLeftMoney)).toString();
        let iLen: number = strAmount.length;
        let iCount: number = this._vJackPotNumberList.length;
        for (let i = 0; i < iCount; ++i) {
            let index: number = iCount - 1 - i;
            if (i < iLen) {
                this._vJackPotNumberList[index].showNum(pf.Util.Number(strAmount.substr(iLen - i - 1, 1)));
            } else {
                this._vJackPotNumberList[index].showNum(0);
            }
        }

        this._humanboyJackpot?.updateJackpotNum();
    }

    /**
     * 初始化金币池
     */
    private _initCoinPool(): void {
        this._nodeCoinPool = new cc.Node();
        this._nodeCoinPool.setContentSize(cc.winSize);
        this.node.addChild(this._nodeCoinPool, eHumanboyLocalZorder.HL_ZORDER_COIN_POOL);
    }

    /**
     * 初始化测试按钮
     */
    // private _initBtnTest(): void {
    //     let tmp_test_node: cc.Node = this.node.getChildByName('tmp_test_node');
    //     if (!tmp_test_node) {
    //         tmp_test_node = new cc.Node();
    //         tmp_test_node.setAnchorPoint(cc.v2(0.5, 0.5));
    //         this.node.addChild(tmp_test_node, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE);

    //         let img: cc.Sprite = tmp_test_node.addComponent(cc.Sprite);
    //         cv.resMgr.setSpriteFrame(img.node, 'zh_CN/internal/image/default_btn_normal', (): void => {
    //             img.type = cc.Sprite.Type.SLICED;
    //             img.sizeMode = cc.Sprite.SizeMode.CUSTOM;
    //             img.trim = false;
    //             // img.srcBlendFactor = cc.macro.BlendFactor.SRC_ALPHA;
    //             // img.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;

    //             // 设置大小和位置(在Sprite组件生效后,节点的大小和位置才有效, 这里是异步加载资源, 因此在回调里面设置)
    //             let sz: cc.Size = cc.size(this._vOtherPlayerInfo[0].imgBg.node.getContentSize());
    //             tmp_test_node.setContentSize(sz.width, sz.height / 2);
    //             let wpos: cc.Vec2 = cc.Vec2.ZERO;
    //             this._vOtherPlayerInfo[0].imgBg.node.convertToWorldSpaceAR(cc.Vec2.ZERO, wpos);
    //             tmp_test_node.setPosition(tmp_test_node.parent.convertToNodeSpaceAR(wpos));

    //             // 添加按钮组件
    //             let btn: cc.Button = tmp_test_node.addComponent(cc.Button);
    //             btn.transition = cc.Button.Transition.SCALE;

    //             // 添加lab文本
    //             let lab_node: cc.Node = new cc.Node();
    //             tmp_test_node.addChild(lab_node);
    //             lab_node.setAnchorPoint(cc.v2(0.5, 0.5));
    //             lab_node.setPosition(cc.Vec2.ZERO);

    //             let txt: cc.Label = lab_node.addComponent(cc.Label);
    //             txt.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
    //             txt.verticalAlign = cc.Label.VerticalAlign.CENTER;
    //             txt.fontSize = 30;
    //             txt.overflow = cc.Label.Overflow.SHRINK;
    //             txt.node.color = cc.Color.BLACK;
    //             txt.node.setContentSize(txt.node.parent.getContentSize());
    //             txt.string = 'BtnTest';
    //         });
    //     }

    //     // 添加点击事件
    //     let step: number = 2;
    //     tmp_test_node.active = true;
    //     tmp_test_node.on('click', (event: cc.Event): void => {
    //         switch (step) {
    //             // 模拟切后台重连
    //             case 0:
    //                 {
    //                     if (cv.netWork.isConnect() && cv.dataHandler.getUserData().m_bIsLoginServerSucc) {
    //                         cv.dataHandler.getUserData().m_bIsLoginServerSucc = true;
    //                         cv.roomManager.RequestJoinRoom(cv.Enum.GameId.HumanBoy, cv.roomManager.getCurrentRoomID());
    //                     } else {
    //                         cv.roomManager.RequestJoinRoom(
    //                             cv.roomManager.getCurrentGameID(),
    //                             humanboyDataMgr.getHumanboyRoom().u32RoomId
    //                         );
    //                     }
    //                 }
    //                 break;

    //             // 模拟中奖提示
    //             case 1:
    //                 {
    //                     let strTips: string[] = [
    //                         '测试 ! 六！Cược Biubiu 666 Fuck you !',
    //                         'Congratulations"昵称最多容纳10个字"和哈哈哈!',
    //                         '"666"Server is about to be 六！Cược 六 vượt quá giới hạn đỏ, 666 fuck you!',
    //                         '恭喜"昵称最多容纳10个字"在初级场 Congratulations 狂赢9999.99万金币！Cược vượt quá giới hạn đỏ, cược tiếp ván này thất bại, 哈哈! 牛批!'
    //                     ];
    //                     let idx: number = Math.floor(pf.StringUtil.randomRange(0, strTips.length));
    //                     this._onMsgRewardTips(strTips[idx]);
    //                 }
    //                 break;

    //             // 模拟批量飞金币
    //             case 2:
    //                 {
    //                     let vBetCoinOption: number[] = this._humanboyRoom.betSettings.betCoinOptions;
    //                     let nAmountIdx: number = Math.floor(pf.StringUtil.randomRange(0, vBetCoinOption.length));

    //                     let nCount: number = Math.floor(pf.StringUtil.randomRange(0, 100));
    //                     for (let i = 0; i < nCount; ++i) {
    //                         let t: tHumanboyCoinOptimization = new tHumanboyCoinOptimization();
    //                         t.nAreaIdx = Math.floor(pf.StringUtil.randomRange(0, this._vAreasInfo.length));
    //                         t.nGold = vBetCoinOption[nAmountIdx];
    //                         t.nUid = this._authService.currentUser.userId;
    //                         t.bAnim = true;
    //                         t.bHeadAnim = true;
    //                         t.bPlaySound = true;
    //                         this._vCoinOptimizationDeque.push_back(t);
    //                     }
    //                 }
    //                 break;

    //             // 其他事件
    //             default:
    //                 {
    //                 }
    //                 break;
    //         }
    //     });
    // }

    /**
     * 初始化庄家面板
     */
    private _initDealerInfo(): void {
        // 庄家信息
        this._panel_dealer = this._panel_top.getChildByName('panel_dealer');
        let iChildrenCount: number = this._panel_dealer.childrenCount;
        for (let i = 0; i < iChildrenCount; ++i) {
            let panel_dealer_head: cc.Node = this._panel_dealer.getChildByName(
                pf.StringUtil.formatC('panel_dealer_head_%d', i)
            );
            if (!panel_dealer_head) continue;

            let tDealerInfo: HumanboyDealerInfo = new HumanboyDealerInfo();
            tDealerInfo.index = i;
            tDealerInfo.imgIcon = panel_dealer_head.getChildByName('img_icon').getComponent(cc.Sprite);
            tDealerInfo.txtName = panel_dealer_head
                .getChildByName('img_name')
                .getChildByName('txt')
                .getComponent(cc.Label);
            tDealerInfo.txtGold = panel_dealer_head
                .getChildByName('img_gold')
                .getChildByName('txt')
                .getComponent(cc.Label);
            tDealerInfo.imgGold = panel_dealer_head
                .getChildByName('img_gold')
                .getChildByName('img')
                .getComponent(cc.Sprite);
            tDealerInfo.imgHead = panel_dealer_head
                .getChildByName('panel_head')
                .getChildByName('img_head')
                .getComponent(cc.Sprite);
            tDealerInfo.avatarControl = panel_dealer_head
                .getChildByName('panel_head')
                .getChildByName('Avatar')
                .getComponent(AvatarControl);
            tDealerInfo.imgHeadBox = panel_dealer_head
                .getChildByName('panel_head')
                .getChildByName('img_head_box')
                .getComponent(cc.Sprite);
            tDealerInfo.imgBeDealerNum = panel_dealer_head.getChildByName('img_count').getComponent(cc.Sprite);
            tDealerInfo.rtxtBeDealerNum = tDealerInfo.imgBeDealerNum.node
                .getChildByName('txt')
                .getComponent(cc.RichText);
            this._vDealerInfo.push(tDealerInfo);

            // 设置庄家图标
            tDealerInfo.imgIcon.spriteFrame = this._atlas_hb_language.getSpriteFrame('humanboy_dealer_icon');

            // 设置庄家默认头像框
            tDealerInfo.imgHead.spriteFrame = null;
            tDealerInfo.imgHeadBox.spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_HEAD_SYSTEM_BOX_CIRCLE
            );

            // 头像添加点击标签
            tDealerInfo.imgHeadBox.addComponent(TagControl).nTag = tDealerInfo.index;

            // 添加庄家头像点击事件
            tDealerInfo.imgHeadBox.node.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
                this._playSoundEffect(macros.Audio.Button);
                let eType: eHumanboyDealerListViewType = eHumanboyDealerListViewType.HDLV_TYPE_CANDIDATE;
                if (event.target) {
                    let info: TagControl = event.target.getComponent(TagControl);
                    if (info) {
                        let index: number = info.nTag;
                        if (index >= 0 && index < pf.StringUtil.getArrayLength(this._humanboyRoom.dealers)) {
                            eType = eHumanboyDealerListViewType.HDLV_TYPE_CANDIDATE;
                        } else {
                            eType = eHumanboyDealerListViewType.HDLV_TYPE_WATTING;
                        }
                    }
                }

                this._showDealerListView(true, eType);
            });
        }

        // 庄家额外显示面板面板
        do {
            this._rtxt_dealer_extra = this._panel_dealer_extra.getChildByName('txt').getComponent(cc.RichText);

            // 上庄按钮
            this._btn_dealer_extra = this._panel_dealer_extra.getChildByName('btn').getComponent(cc.Button);
            this._btn_dealer_extra.node.on('click', this._onClickDealerBtn, this);
        } while (false);

        // 庄家牌
        do {
            // 底牌遮罩图
            this._img_dealer_card_shield = this._panel_dealer.getChildByName('img_dealer_card_shield');
            if (this._img_dealer_card_shield) this._img_dealer_card_shield.active = false;

            // 牌精灵
            let panel_dealer_card: cc.Node = this._panel_dealer.getChildByName('panel_dealer_card');
            let panel_card: cc.Node = panel_dealer_card.getChildByName('panel_card');
            if (panel_card) {
                for (let i = 0; i < 5; ++i) {
                    let sp_card: cc.Node = panel_card.getChildByName(pf.StringUtil.formatC('card_%d', i));
                    sp_card.active = false;

                    const card = this.createPokerCard();
                    card.ResetFromNode(sp_card);
                    this._vDealerCardNode.push(card);
                    this._vDealerCardSrcPos.push(cc.v2(card.node.position));
                    ++this._nSendCardsTotalNum;
                }
            }

            // 牌型
            let img_card_type: cc.Node = panel_dealer_card.getChildByName('img_card_type');
            this._img_dealer_card_type = img_card_type.getChildByName('img').getComponent(cc.Sprite);
        } while (false);

        // 闹钟
        do {
            this._img_bet_clock = this._panel_dealer.getChildByName('img_bet_clock');
            this._img_bet_clock.active = false;
            this._src_bet_clock_pos = cc.v2(this._img_bet_clock.position);
        } while (false);
    }

    private createPokerCard(): PokerCardControl {
        const ctrl = cc
            .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.POKER_CARD))
            .getComponent(PokerCardControl);
        ctrl.init();
        ctrl.setCardBackSpriteFrame(pf.addressableAssetManager.getAsset(macros.Assets.CARD_BACK));
        return ctrl;
    }

    /**
     * 更新庄家信息
     */
    private _updateDealerInfo(): void {
        const vDealerInfoData = this._humanboyRoom.dealers;
        for (let i = 0; i < this._vDealerInfo.length; ++i) {
            let txtName: cc.Label = this._vDealerInfo[i].txtName;
            let txtGold: cc.Label = this._vDealerInfo[i].txtGold;
            let imgGold: cc.Sprite = this._vDealerInfo[i].imgGold;
            let imgHead: cc.Sprite = this._vDealerInfo[i].imgHead;
            const avatarControl = this._vDealerInfo[i].avatarControl;
            let imgHeadBox: cc.Sprite = this._vDealerInfo[i].imgHeadBox;
            let imgBeDealerNum: cc.Sprite = this._vDealerInfo[i].imgBeDealerNum;
            let rtxtBeDealerNum: cc.RichText = this._vDealerInfo[i].rtxtBeDealerNum;
            if (i < vDealerInfoData.length) {
                let tDealerData: Readonly<domain.DealerPlayer> = vDealerInfoData[i];
                switch (tDealerData.uid) {
                    // 系统庄
                    case 0:
                        if (i === 0) {
                            avatarControl.head.spriteFrame = pf.addressableAssetManager.getAsset(
                                macros.Assets.HUMANBOY_HEAD_SYSTEM_CIRCLE
                            );
                            imgHeadBox.spriteFrame = pf.addressableAssetManager.getAsset(
                                macros.Assets.HUMANBOY_HEAD_SYSTEM_BOX_CIRCLE
                            );

                            txtName.string = pf.languageManager.getString('Humanboy_game_apply_dealer_system');
                            txtGold.string = this._humanboyRoom.transGoldShortString(tDealerData.stockCoin);
                            imgGold.node.active = true;
                            rtxtBeDealerNum.string = '';
                            imgBeDealerNum.node.active = false;
                        } else {
                            avatarControl.head.spriteFrame = pf.addressableAssetManager.getAsset(
                                macros.Assets.HUMANBOY_HEAD_NONE_CIRCLE
                            );
                            imgHeadBox.spriteFrame = pf.addressableAssetManager.getAsset(
                                macros.Assets.HUMANBOY_HEAD_PLAYER_BOX_CIRCLE
                            );
                            txtName.string = pf.languageManager.getString('Humanboy_game_apply_dealer_system');
                            txtGold.string = '';
                            imgGold.node.active = false;
                            rtxtBeDealerNum.string = '';
                            imgBeDealerNum.node.active = false;
                        }
                        break;
                    // 选庄中
                    case 1:
                        avatarControl.head.spriteFrame = pf.addressableAssetManager.getAsset(
                            macros.Assets.HUMANBOY_HEAD_DEALER_CHOOSE_CIRCLE
                        );
                        imgHeadBox.spriteFrame = pf.addressableAssetManager.getAsset(
                            macros.Assets.HUMANBOY_HEAD_PLAYER_BOX_CIRCLE
                        );
                        txtName.string = pf.languageManager.getString('Humanboy_game_dealer_choose');
                        txtGold.string = '';
                        imgGold.node.active = false;
                        rtxtBeDealerNum.string = '';
                        imgBeDealerNum.node.active = false;
                        break;
                    // 玩家庄
                    default:
                        {
                            const headPath = cr.CommonUtil.getHeadPath(tDealerData, this._authService.currentUser.userId);
                            avatarControl.loadHeadImage(headPath, tDealerData.plat);

                            imgHeadBox.spriteFrame = pf.addressableAssetManager.getAsset(
                                macros.Assets.HUMANBOY_HEAD_PLAYER_BOX_CIRCLE
                            );

                            txtName.string = tDealerData.name;
                            txtGold.string = this._humanboyRoom.transGoldShortString(tDealerData.stockCoin);
                            imgGold.node.active = true;
                            let str_color: string = pf.StringUtil.formatC(
                                pf.languageManager.getString('Humanboy_game_dealer_up_count_1'),
                                tDealerData.beDealerNum,
                                this._humanboyRoom.roomParams.dealerCount
                            );
                            pf.StringUtil.setRichTextString(rtxtBeDealerNum.node, str_color);
                            imgBeDealerNum.node.active = true;
                        }
                        break;
                }
            } else {
                avatarControl.head.spriteFrame = pf.addressableAssetManager.getAsset(
                    macros.Assets.HUMANBOY_HEAD_NONE_CIRCLE
                );
                imgHeadBox.spriteFrame = pf.addressableAssetManager.getAsset(
                    macros.Assets.HUMANBOY_HEAD_PLAYER_BOX_CIRCLE
                );
                txtName.string = pf.languageManager.getString('Humanboy_game_apply_dealer_system');
                txtGold.string = '';
                imgGold.node.active = false;
                rtxtBeDealerNum.string = '';
                imgBeDealerNum.node.active = false;
            }
        }
        // 更新庄家/非庄家 额外显示面板
        this._updateDealerExtraInfo();
    }

    /**
     * 更新庄家(非庄家)额外显示信息
     */
    private _updateDealerExtraInfo(): void {
        let bOnDealerList: boolean = this._humanboyRoom.onDealerList;
        if (bOnDealerList) {
            this._btnStatus = eHumanboyDealerBtnStatus.HDB_STATUS_DEALER_DOWN;
        } else {
            this._btnStatus = eHumanboyDealerBtnStatus.HDB_STATUS_DEALER_UP;
        }

        switch (this._btnStatus) {
            // 上庄
            case eHumanboyDealerBtnStatus.HDB_STATUS_DEALER_UP:
                {
                    this._btn_dealer_extra.getComponent(cc.Sprite).spriteFrame =
                        this._atlas_hb_language.getSpriteFrame('humanboy_dealer_btn_up');
                    this._panel_betbtn.active = true;

                    // 上庄底分
                    let llMoneyperstock: number = this._humanboyRoom.roomParams.moneyPerStock;
                    let strMoneyperstock: string = this._humanboyRoom.transGoldShortString(llMoneyperstock, 8);
                    this._rtxt_dealer_extra.string = pf.StringUtil.formatC(
                        pf.languageManager.getString('Humanboy_game_dealer_limit_score'),
                        strMoneyperstock
                    );
                }
                break;

            // 下庄
            case eHumanboyDealerBtnStatus.HDB_STATUS_DEALER_DOWN:
                {
                    this._btn_dealer_extra.getComponent(cc.Sprite).spriteFrame =
                        this._atlas_hb_language.getSpriteFrame('humanboy_dealer_btn_down');

                    // 若自己是庄家, 额外面板的坐庄次数显示自己上庄局数
                    let vDealerInfoData = this._humanboyRoom.dealers;
                    if (vDealerInfoData.length > 0) {
                        let tDealerData: Readonly<domain.DealerPlayer> = vDealerInfoData[0];
                        if (tDealerData.uid !== 0 && tDealerData.uid !== 1) {
                            let str_color: string = pf.StringUtil.formatC(
                                pf.languageManager.getString('Humanboy_game_dealer_up_count_2'),
                                tDealerData.beDealerNum,
                                this._humanboyRoom.roomParams.dealerCount
                            );
                            pf.StringUtil.setRichTextString(this._rtxt_dealer_extra.node, str_color);
                        }
                    }
                }
                break;

            default:
                break;
        }
    }

    /**
     * 重置庄家牌
     * @param visible
     */
    private _resetDealerCards(visible: boolean = true): void {
        for (let i = 0; i < this._vDealerCardNode.length; ++i) {
            this._vDealerCardNode[i].node.stopAllActions();
            this._vDealerCardNode[i].SetFace(false);
            this._vDealerCardNode[i].node.setPosition(this._vDealerCardSrcPos[i]);
            this._vDealerCardNode[i].node.active = visible;
        }

        this._img_dealer_card_type.node.parent.active = false;
    }

    /**
     * 重置指定区域闲家牌
     * @param betOption
     * @param visible
     */
    private _resetPlayerCards(betOption: network.BetZoneOption, visible: boolean /* false */): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let pArea: HumanboyAreaInfo = this._vAreasInfo[nAreaIdx];
        if (!pArea.panelCard) return;

        pArea.panelCard.active = visible;
        pArea.imgCardTypeBg.node.active = false;
        pArea.imgCardTypeTxt.node.active = false;

        for (let i = 0; i < pArea.vCardsNode.length; ++i) {
            pArea.vCardsNode[i].node.stopAllActions();
            pArea.vCardsNode[i].SetFace(false);
            pArea.vCardsNode[i].node.setPosition(pArea.vCardsSrcPos[i]);
            pArea.vCardsNode[i].node.active = visible;
        }
    }

    /**
     * 重置所有区域闲家牌
     * @param visible
     */
    private _resetAllPlayerCards(visible: boolean = false): void {
        for (const area of this._vAreasInfo) {
            let pArea: HumanboyAreaInfo = area;
            this._resetPlayerCards(pArea.eZone, visible);
        }
    }

    /**
     * 重置所有牌(闲家 + 庄家)
     * @param visible
     */
    private _resetAllCards(visible: boolean = false): void {
        this._nSendCardsCallBackNum = 0;
        this._resetAllPlayerCards(visible);
        this._resetDealerCards(visible);

        this._img_dealer_card_shield.active = !visible;
    }

    /**
     * 初始化玩家列表信息
     */
    private _initPlayersInfo(): void {
        // 自己
        do {
            let panel_self: cc.Node = this._panel_bottom.getChildByName('panel_self');
            let img_name: cc.Node = panel_self.getChildByName('img_name');
            let img_gold: cc.Node = panel_self.getChildByName('img_gold');
            this._txt_self_name = img_name.getChildByName('txt').getComponent(cc.Label);
            this._txt_self_gold = img_gold.getChildByName('txt').getComponent(cc.Label);
            this._img_self_gold = img_gold.getChildByName('img').getComponent(cc.Sprite);
            this._img_self_head = panel_self.getChildByName('img_head').getComponent(cc.Sprite);
            this._selfAvatar = panel_self.getChildByName('Avatar').getComponent(AvatarControl);

            // 设置默认头像框
            this._img_self_head.spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_HEAD_PLAYER_BOX_CIRCLE
            );

            // 充值
            let btn_add: cc.Node = img_gold.getChildByName('btn');
            btn_add.on('click', (event: cc.Event): void => {
                this._playSoundEffect(macros.Audio.Button);
                this.recharge();
            });
        } while (false);

        this.setLeftAndRightList();
        // 其他玩家
        do {
            let count = 0;
            let max_count = 5;
            // let total_h: number = this._panel_game.height;
            switch (this._eGameboyScreenType) {
                case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD:
                    count = 5;
                    // total_h -= 50;
                    break;

                case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW:
                case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL:
                default:
                    count = 4;
                    break;
            }

            // panel_left_playerlist.setContentSize(panel_left_playerlist.width, total_h);
            // panel_right_playerlist.setContentSize(panel_right_playerlist.width, total_h);

            // 排版从上至下
            // let start_x: number = 0;
            // let start_y: number = panel_left_playerlist.height * (1 - panel_left_playerlist.anchorY);
            // let offset_img: number = (total_h - panel_left_playerlist.getChildByName("img_bg_0").height * count) / (count - 1);
            // let offset_head: number = 15;
            // let offset_coin: number = -65;
            // let offset_left_flag: number = 50;
            // let offset_right_flag: number = 52;
            // let v2LeftImg: cc.Vec2 = cc.v2(start_x, start_y);
            // let v2RightImg: cc.Vec2 = cc.v2(start_x, start_y);

            // 目前头像列表由于 drawcall 因素, 打乱了ui层级结构, 不能以预制件等动态节点 add
            // 因此在场景文件中排列出了最大数量情况, 具体有多余的动态删除
            for (let i = 0; i < max_count; ++i) {
                // 左列表(富豪榜)
                do {
                    let img_bg: cc.Node = this._panel_left_playerlist.getChildByName(`img_bg_${i}`);
                    let node_head: cc.Node = this._panel_left_playerlist.getChildByName(`node_head_${i}`);
                    let text_coin: cc.Node = this._panel_left_playerlist.getChildByName(`text_coin_${i}`);

                    if (i < count) {
                        // 排版计算
                        // v2LeftImg.y -= img_bg.height * (1 - img_bg.anchorY) * img_bg.scaleY;
                        // img_bg.setPosition(v2LeftImg);
                        // v2LeftImg.y -= img_bg.height * img_bg.anchorY * img_bg.scaleY;
                        // v2LeftImg.y -= offset_img;

                        // let v2Head: cc.Vec2 = cc.v2(v2LeftImg.x, offset_head);
                        // img_bg.convertToWorldSpaceAR(v2Head, v2Head);
                        // panel_left_playerlist.convertToNodeSpaceAR(v2Head, v2Head);
                        // node_head.setPosition(v2Head);

                        // let v2Coin: cc.Vec2 = cc.v2(v2LeftImg.x, offset_coin);
                        // img_bg.convertToWorldSpaceAR(v2Coin, v2Coin);
                        // panel_left_playerlist.convertToNodeSpaceAR(v2Coin, v2Coin);
                        // text_coin.setPosition(v2Coin);

                        // 填充数据
                        let player: HumanboyPlayerInfo = new HumanboyPlayerInfo();
                        player.imgBg = img_bg.getComponent(cc.Sprite);
                        player.nodeHead = node_head;
                        player.txtCoin = text_coin.getComponent(cc.Label);
                        const avatar = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.AVATAR));
                        node_head.addChild(avatar);

                        player.avatarControl = avatar.getComponent(AvatarControl);

                        if (i === 0) {
                            player.imgFlag = this._panel_left_playerlist
                                .getChildByName('nb_flag')
                                .getComponent(cc.Sprite);
                            let nb_flag_desc: cc.Label = player.imgFlag.node
                                .getChildByName('nb_flag_desc')
                                .getComponent(cc.Label);
                            nb_flag_desc.string = pf.StringUtil.formatC(
                                pf.languageManager.getString('Cowboy_fuhao_no_text'),
                                1
                            );

                            // let v2Flag: cc.Vec2 = cc.v2(player.imgFlag.node.x, offset_left_flag);
                            // img_bg.convertToWorldSpaceAR(v2Flag, v2Flag);
                            // panel_left_playerlist.convertToNodeSpaceAR(v2Flag, v2Flag);
                            // player.imgFlag.node.setPosition(v2Flag.x+5,v2Flag.y);
                        }

                        this._vOtherPlayerInfo.push(player);
                    } else {
                        this._panel_left_playerlist.removeChild(img_bg);
                        this._panel_left_playerlist.removeChild(node_head);
                        this._panel_left_playerlist.removeChild(text_coin);
                        pf.UIUtil.destroyNode(img_bg);
                        pf.UIUtil.destroyNode(node_head);
                        pf.UIUtil.destroyNode(text_coin);
                    }
                } while (false);

                // 右列表(神算子)
                do {
                    let img_bg: cc.Node = this._panel_right_playerlist.getChildByName(`img_bg_${i}`);
                    let node_head: cc.Node = this._panel_right_playerlist.getChildByName(`node_head_${i}`);
                    let text_coin: cc.Node = this._panel_right_playerlist.getChildByName(`text_coin_${i}`);

                    if (i < count) {
                        // 排版计算
                        // v2RightImg.y -= img_bg.height * (1 - img_bg.anchorY) * img_bg.scaleY;
                        // img_bg.setPosition(v2RightImg);
                        // v2RightImg.y -= img_bg.height * img_bg.anchorY * img_bg.scaleY;
                        // v2RightImg.y -= offset_img;

                        // let v2Head: cc.Vec2 = cc.v2(v2RightImg.x, offset_head);
                        // img_bg.convertToWorldSpaceAR(v2Head, v2Head);
                        // panel_right_playerlist.convertToNodeSpaceAR(v2Head, v2Head);
                        // node_head.setPosition(v2Head);

                        // let v2Coin: cc.Vec2 = cc.v2(v2RightImg.x, offset_coin);
                        // img_bg.convertToWorldSpaceAR(v2Coin, v2Coin);
                        // panel_right_playerlist.convertToNodeSpaceAR(v2Coin, v2Coin);
                        // text_coin.setPosition(v2Coin);

                        // 填充数据
                        let player: HumanboyPlayerInfo = new HumanboyPlayerInfo();
                        player.imgBg = img_bg.getComponent(cc.Sprite);
                        player.nodeHead = node_head;
                        player.txtCoin = text_coin.getComponent(cc.Label);

                        const avatar = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.AVATAR));
                        node_head.addChild(avatar);
                        player.avatarControl = avatar.getComponent(AvatarControl);

                        if (i === 0) {
                            player.imgFlag = this._panel_right_playerlist
                                .getChildByName('nb_flag')
                                .getComponent(cc.Sprite);
                            let nb_flag_desc: cc.Label = player.imgFlag.node
                                .getChildByName('nb_flag_desc')
                                .getComponent(cc.Label);
                            nb_flag_desc.string = pf.languageManager.getString('Cowboy_shensuanzi_text');

                            // let v2Flag: cc.Vec2 = cc.v2(player.imgFlag.node.x, offset_right_flag);
                            // img_bg.convertToWorldSpaceAR(v2Flag, v2Flag);
                            // panel_right_playerlist.convertToNodeSpaceAR(v2Flag, v2Flag);
                            // player.imgFlag.node.setPosition(v2Flag.x+5,v2Flag.y);
                        }

                        this._vOtherPlayerInfo.push(player);
                    } else {
                        this._panel_right_playerlist.removeChild(img_bg);
                        this._panel_right_playerlist.removeChild(node_head);
                        this._panel_right_playerlist.removeChild(text_coin);
                        pf.UIUtil.destroyNode(img_bg);
                        pf.UIUtil.destroyNode(node_head);
                        pf.UIUtil.destroyNode(text_coin);
                    }
                } while (false);
            }

            // 头像
            for (const info of this._vOtherPlayerInfo) {
                info.imgBg.spriteFrame = this._atlas_hb_humanboy.getSpriteFrame('humanboy_icon_seat_bg_1');
                info.nodeHead.getChildByName('img').getComponent(cc.Sprite).spriteFrame = null;
                info.txtCoin.node.zIndex = eHumanboyLocalZorder.HL_ZORDER_IMG_HEAD_TXT;
                if (info.imgFlag) info.imgFlag.node.zIndex = eHumanboyLocalZorder.HL_ZORDER_IMG_HEAD_FLAG;
            }
        } while (false);
    }

    /**
     * 更新个人信息
     */
    private _updateSelfInfo(): void {
        // 昵称
        pf.StringUtil.setShrinkString(this._txt_self_name.node, this._humanboyRoom.selfPlayer.name, true);

        // 金币
        let llCurCoin: number = this._humanboyRoom.selfPlayer.curCoin;
        this._txt_self_gold.string = this._humanboyRoom.transGoldShortString(llCurCoin);

        // 头像
        this._selfAvatar.loadHeadImage(this._humanboyRoom.selfPlayer.head, this._humanboyRoom.selfPlayer.plat);
        this._humanboyRoom.setPlayerBeforeSettlementGold(this._authService.currentUser.userId, llCurCoin);
    }

    /**
     * 更新其他人信息
     */
    private _updateOtherPlayersInfo(): void {
        // 这里按照服务器发的gamePlayers顺序放
        for (let i = 0; i < this._vOtherPlayerInfo.length; ++i) {
            let vOtherPlayerInfo: Readonly<pf.services.GamePlayer[]> = this._humanboyRoom.otherPlayers;
            if (i < vOtherPlayerInfo.length) {
                let info: Readonly<pf.services.GamePlayer> = vOtherPlayerInfo[i];
                this._vOtherPlayerInfo[i].uid = info.uid;

                // 头像更新
                const headUrl: string = cr.CommonUtil.getHeadPath(info, this._authService.currentUser.userId);
                this._vOtherPlayerInfo[i].imgBg.spriteFrame =
                    this._atlas_hb_humanboy.getSpriteFrame('humanboy_icon_seat_bg_1');
                this._vOtherPlayerInfo[i].nodeHead.active = true;
                this._vOtherPlayerInfo[i].avatarControl.loadHeadImage(headUrl, info.plat);

                this._vOtherPlayerInfo[i].txtCoin.string = this._humanboyRoom.transGoldShortString(info.curCoin);
                if (this._vOtherPlayerInfo[i].imgFlag) this._vOtherPlayerInfo[i].imgFlag.node.active = true;

                this._humanboyRoom.setPlayerBeforeSettlementGold(info.uid, info.curCoin);
            } else {
                this._vOtherPlayerInfo[i].uid = 0;
                this._vOtherPlayerInfo[i].imgBg.spriteFrame =
                    this._atlas_hb_humanboy.getSpriteFrame('humanboy_icon_seat_bg_2');
                this._vOtherPlayerInfo[i].nodeHead.active = false;
                this._vOtherPlayerInfo[i].txtCoin.string = '';
                if (this._vOtherPlayerInfo[i].imgFlag) this._vOtherPlayerInfo[i].imgFlag.node.active = false;

                // 移除连胜节点
                let str_tag: string = 'win_player_win_count_' + this._vOtherPlayerInfo[i].nodeHead.uuid;
                let strNode = cc.find(str_tag, this.node);
                if (strNode && cc.isValid(strNode, true)) {
                    this.node.removeChild(strNode);
                    strNode.destroy();
                }
            }
        }
    }

    /**
     * 更新玩家金币信息(同步服务器最新数据)
     * @param uid
     */
    private _updatePlayerGold(uid: number): void {
        if (uid === this._authService.currentUser.userId) {
            let llCurCoin: number = this._humanboyRoom.selfPlayer.curCoin;
            this._txt_self_gold.string = this._humanboyRoom.transGoldShortString(llCurCoin);

            this._humanboyRoom.setPlayerBeforeSettlementGold(uid, llCurCoin);
        }

        for (const info of this._vOtherPlayerInfo) {
            if (info.uid === uid) {
                // 神算子/富豪是自己的情況
                if (uid === this._authService.currentUser.userId) {
                    info.txtCoin.string = this._txt_self_gold.string;
                } else {
                    const player = this._humanboyRoom.getOtherPlayer(uid);
                    if (player) {
                        let llCurCoin: number = player.curCoin;
                        info.txtCoin.string = this._humanboyRoom.transGoldShortString(llCurCoin);

                        this._humanboyRoom.setPlayerBeforeSettlementGold(uid, llCurCoin);
                    }
                }
            }
        }
    }

    /**
     * 更新玩家临时金币显示(动画临时变化)
     * @param uid
     * @param amount
     */
    private _updatePlayerTempGold(uid: number, amount: number): void {
        if (uid === this._authService.currentUser.userId) {
            this._txt_self_gold.string = this._humanboyRoom.transGoldShortString(amount);
        }

        for (const info of this._vOtherPlayerInfo) {
            if (info.uid === uid) {
                // 神算子/富豪是自己的情況
                if (uid === this._authService.currentUser.userId) {
                    info.txtCoin.string = this._txt_self_gold.string;
                } else {
                    const player = this._humanboyRoom.getOtherPlayer(uid);
                    if (player) {
                        info.txtCoin.string = this._humanboyRoom.transGoldShortString(amount);
                    }
                }
            }
        }
    }

    /**
     * 更新所有玩家临时金币显示(动画临时变化)
     */
    private _updateAllPlayerTempGold(): void {
        // let vSettles: humanboy_proto.PlayerSettle[] = [];
        // pf.StringUtil.deepCopy(humanboyDataMgr.getHumanboyRoom().vPlayerSettles, vSettles);
        // vSettles.push(humanboyDataMgr.getHumanboyRoom().tOtherPlayerSettle);

        // for (let i = 0; i < vSettles.length; ++i) {
        this._humanboyRoom.roundInfo.getAllPlayerSettles().forEach((playerSettle) => {
            let uid: number = playerSettle.uid;
            let llWinAmount: number = playerSettle.pos4WinAmount;
            let llLuckWinAmount: number = playerSettle.posLuckWinAmount;

            let llBscGold: number = this._humanboyRoom.getPlayerBeforeSettlementGold(uid);
            llBscGold += llWinAmount;
            llBscGold += llLuckWinAmount;

            this._updatePlayerTempGold(uid, llBscGold);
            this._humanboyRoom.setPlayerBeforeSettlementGold(uid, llBscGold);
        });
    }

    /**
     * 初始化下注区域
     */
    private _initBetAreas(): void {
        // 闲家四门区域
        let vBetOptionArea: network.BetZoneOption[] = [];
        vBetOptionArea.push(network.BetZoneOption.POS1); // 闲家1
        vBetOptionArea.push(network.BetZoneOption.POS2); // 闲家2
        vBetOptionArea.push(network.BetZoneOption.POS3); // 闲家3
        vBetOptionArea.push(network.BetZoneOption.POS4); // 闲家4

        // 幸运一击区域
        vBetOptionArea.push(network.BetZoneOption.POS_LUCK_1); // 庄家通吃/通赔
        vBetOptionArea.push(network.BetZoneOption.POS_LUCK_2); // 一对
        vBetOptionArea.push(network.BetZoneOption.POS_LUCK_3); // 两对
        vBetOptionArea.push(network.BetZoneOption.POS_LUCK_4); // 三条
        vBetOptionArea.push(network.BetZoneOption.POS_LUCK_5); // 顺子/同花
        vBetOptionArea.push(network.BetZoneOption.POS_LUCK_6); // 葫芦/金刚/同花顺/皇家

        let panel_area: cc.Node = this._panel_game.getChildByName('panel_area');
        let panel_border: cc.Node = this._panel_game.getChildByName('panel_border');
        let panel_way_out: cc.Node = this._panel_game.getChildByName('panel_way_out');
        let panel_card: cc.Node = this._panel_game.getChildByName('panel_card');
        let panel_coin: cc.Node = this._panel_game.getChildByName('panel_coin');
        let panel_txt: cc.Node = this._panel_game.getChildByName('panel_txt');

        for (let i = 0; i < vBetOptionArea.length; ++i) {
            let areaInfo: HumanboyAreaInfo = new HumanboyAreaInfo();
            areaInfo.eZone = vBetOptionArea[i];
            areaInfo.index = i;
            areaInfo.panelArea = panel_area.getChildByName(pf.StringUtil.formatC('area_%d', i));
            areaInfo.panelBorder = panel_border.getChildByName(pf.StringUtil.formatC('border_%d', i));
            areaInfo.panelCoin = panel_coin.getChildByName(pf.StringUtil.formatC('coin_%d', i));

            // 赔率
            let fnt_odd: cc.Node = panel_txt.getChildByName(pf.StringUtil.formatC('fnt_odd_%d', i));
            if (fnt_odd) {
                areaInfo.txtOdds = fnt_odd.getComponent(cc.Label);
            }

            // 牌区
            areaInfo.panelCard = panel_card.getChildByName(pf.StringUtil.formatC('card_node_%d', i));
            if (areaInfo.panelCard) {
                areaInfo.imgCardTypeBg = panel_card
                    .getChildByName(pf.StringUtil.formatC('img_card_type_bg_%d', i))
                    .getComponent(cc.Sprite);
                areaInfo.imgCardTypeTxt = panel_card
                    .getChildByName(pf.StringUtil.formatC('img_card_type_txt_%d', i))
                    .getComponent(cc.Sprite);

                // 初始化牌组精灵, 位置等
                let children: cc.Node[] = areaInfo.panelCard.children;
                let count: number = areaInfo.panelCard.childrenCount;
                for (let j = 0; j < count; ++j) {
                    let sp_card: cc.Node = children[j];
                    sp_card.active = false;

                    const card = this.createPokerCard();
                    card.ResetFromNode(sp_card);

                    areaInfo.vCardsNode.push(card);
                    areaInfo.vCardsSrcPos.push(cc.v2(card.node.position));
                    ++this._nSendCardsTotalNum;
                }
            }

            // 总注/限红
            areaInfo.txtTotalBetNum = panel_txt
                .getChildByName(pf.StringUtil.formatC('txt_total_bet_num_%d', i))
                .getComponent(cc.Label);

            // 个人注
            areaInfo.txtSelfBetNum = panel_txt
                .getChildByName(pf.StringUtil.formatC('txt_self_bet_num_%d', i))
                .getComponent(cc.Label);
            areaInfo.txtSelfBetNum.node.color = cc.color(255, 255, 0);

            // 初始化路子面板
            do {
                areaInfo.panelWayOut = panel_way_out.getChildByName(pf.StringUtil.formatC('way_out_%d', i));
                areaInfo.panelWayOut.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.Button);
                    this._showChart(areaInfo.eZone);
                });

                // 路子显示风格
                // 三条以下: 纯图片
                if (areaInfo.eZone < network.BetZoneOption.POS_LUCK_4) {
                    areaInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                }
                // 三条: 图片文字混合显示
                else if (areaInfo.eZone === network.BetZoneOption.POS_LUCK_4) {
                    areaInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO;
                    areaInfo.iWayOutLoseLimitCount = 200;
                }
                // 三条以上: 纯文字
                else if (areaInfo.eZone > network.BetZoneOption.POS_LUCK_4) {
                    areaInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                    areaInfo.iWayOutLoseLimitCount = 200;
                }

                // 路子球状图片
                let count: number = areaInfo.panelWayOut.childrenCount;
                for (let i_wayout_index = 0; i_wayout_index < count; ++i_wayout_index) {
                    let strImgName: string = pf.StringUtil.formatC('img_%d', i_wayout_index);
                    let img: cc.Node = areaInfo.panelWayOut.getChildByName(strImgName);
                    if (img) {
                        img.active = false;
                        areaInfo.vWayOutImg.push(img.getComponent(cc.Sprite));
                        areaInfo.vWayOutImgSrcPos.push(cc.v2(img.position));
                    }
                }

                // 文本
                let txt_way_out: cc.Node = panel_txt.getChildByName(pf.StringUtil.formatC('txt_way_out_%d', i));
                if (txt_way_out) {
                    areaInfo.rtxtWayOut = txt_way_out.getComponent(cc.RichText);
                    areaInfo.rtxtWayOut.node.active = false;
                    areaInfo.rtxtWayOut.handleTouchEvent = false;
                }
            } while (false);

            // push 区域数组
            this._vAreasInfo.push(areaInfo);
            areaInfo.panelArea.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
                this._onClickAreaCoinPanel(areaInfo.index);
            });
        }
    }

    /**
     * 更新下注区域下注数量
     * @param betOption
     * @param llTotalAmount
     * @param llSelfAmount
     */
    private _updateBetAreaBetsNum(
        betOption: network.BetZoneOption,
        llTotalAmount: number = -1,
        llSelfAmount: number = -1
    ): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;
        let totalBet = 0;
        let selfBet = 0;

        let zoneData: domain.BetZone = this._humanboyRoom.betZones.get(betOption);
        if (zoneData) {
            totalBet = zoneData.optionInfo.totalBet;
            selfBet = zoneData.optionInfo.selfBet;
        }
        const totalAmount = llTotalAmount < 0 ? totalBet : llTotalAmount;
        const selfAmount = llSelfAmount < 0 ? selfBet : llSelfAmount;
        // 自己下注
        if (selfAmount > 0) {
            let str_amount: string = this._humanboyRoom.transGoldShortString(selfAmount, 5);
            this._vAreasInfo[nAreaIdx].txtSelfBetNum.string = str_amount;
        } else {
            this._vAreasInfo[nAreaIdx].txtSelfBetNum.string = '';
        }
        // 总注
        let str_total: string = this._humanboyRoom.transGoldShortString(totalAmount, 5);
        this._vAreasInfo[nAreaIdx].txtTotalBetNum.string = str_total;
        if (betOption < network.BetZoneOption.POS_LUCK) {
            // 限红
            let llLimitAmount = 0;
            if (this._mapBetAreaLimitAmount.has(betOption)) {
                llLimitAmount = this._mapBetAreaLimitAmount.get(betOption);
            }
            let str_limit: string = this._humanboyRoom.transGoldShortString(llLimitAmount, 10);
            // 共享限红

            if (this._humanboyRoom.roomParams.shareLimitAmount > 0) {
                // 四门总注
                let llAllTotalAmount = 0;
                this._humanboyRoom.betZones.forEach((value: domain.BetZone, key: network.BetZoneOption) => {
                    if (key >= network.BetZoneOption.POS_LUCK) return;
                    llAllTotalAmount += value.optionInfo.totalBet;
                });
                let str_all_total: string = this._humanboyRoom.transGoldShortString(llAllTotalAmount, 10);
                this._setBetAreaLimitAmount(str_all_total, str_limit);
            }
            // 非共享限红
            else {
                this._vAreasInfo[nAreaIdx].txtTotalBetNum.string = pf.StringUtil.formatC('%s/%s', str_total, str_limit);
            }
        }
    }

    /**
     * 初始化总限红描述
     */
    private _initBetAreaLimit(): void {
        let panel_txt: cc.Node = this._panel_game.getChildByName('panel_txt');
        this._txt_shared_limit_word = panel_txt.getChildByName('txt_shared_limit_word').getComponent(cc.Label);
        this._txt_shared_limit_amount = panel_txt.getChildByName('txt_shared_limit_amount').getComponent(cc.Label);

        this._txt_shared_limit_word.string = pf.languageManager.getString('Humanboy_game_shared_limit_text');
    }

    /**
     * 设置总限红金额
     * @param strTotal
     * @param strLimit
     */
    private _setBetAreaLimitAmount(strTotal: string, strLimit: string): void {
        this._txt_shared_limit_amount.string = pf.StringUtil.formatC('%s/%s', strTotal, strLimit);

        // 适配位置
        let szWord: cc.Size = pf.UIUtil.getLabelStringSize(this._txt_shared_limit_word);
        let szScore: cc.Size = pf.UIUtil.getLabelStringSize(this._txt_shared_limit_amount);

        let parentNode: cc.Node = this._txt_shared_limit_word.node.parent;
        let total_w: number = parentNode.width;
        let offset_w = 10;
        let start_x: number =
            (total_w - (szWord.width + offset_w + szScore.width)) / 2 - parentNode.anchorX * parentNode.width;

        let pos_x: number = start_x + this._txt_shared_limit_word.node.anchorX * szWord.width;
        let pos_y: number = this._txt_shared_limit_word.node.y;
        this._txt_shared_limit_word.node.setPosition(pos_x, pos_y);

        pos_x += this._txt_shared_limit_word.node.anchorX * szWord.width;
        pos_x += offset_w;
        pos_x += this._txt_shared_limit_amount.node.anchorX * szScore.width;
        this._txt_shared_limit_amount.node.setPosition(pos_x, pos_y);
    }

    /**
     * 重置下注区域限红
     */
    private _resetAllBetAreaLimitAmount(): void {
        this._mapBetAreaLimitAmount.clear();
        this._mapBetAreaLimitAmount.set(network.BetZoneOption.POS1, 0);
        this._mapBetAreaLimitAmount.set(network.BetZoneOption.POS2, 0);
        this._mapBetAreaLimitAmount.set(network.BetZoneOption.POS3, 0);
        this._mapBetAreaLimitAmount.set(network.BetZoneOption.POS4, 0);

        // 先填充所有区域限红
        const vOddsDetail = this._humanboyRoom.roomParams.oddsDetails;
        for (const detail of vOddsDetail) {
            let option: network.BetZoneOption = detail.option;
            if (this._mapBetAreaLimitAmount.has(option)) {
                this._mapBetAreaLimitAmount[option] = detail.limit;
            }
        }

        // 四门闲家共享的下注额限额 (0 代表不共享 > 0 代表四门闲共享一个限额)
        let llLimitAmount: number = this._humanboyRoom.roomParams.shareLimitAmount;
        if (llLimitAmount > 0) {
            this._mapBetAreaLimitAmount.forEach((value, key): void => {
                this._mapBetAreaLimitAmount.set(key, llLimitAmount);
            });
        }

        // 共享限红文本显隐状态;
        let bActive: boolean = llLimitAmount > 0;
        this._txt_shared_limit_word.node.active = bActive;
        this._txt_shared_limit_amount.node.active = bActive;
    }

    /**
     * 更新区域限红
     * @param betOption
     * @param disAmount
     */
    private _updateBettAreaLimitAmount(betOption: network.BetZoneOption, disAmount: number): void {
        let cb: Function = (op: network.BetZoneOption, dis: number): void => {
            if (this._mapBetAreaLimitAmount.has(op)) {
                let n: number = this._mapBetAreaLimitAmount.get(op);
                this._mapBetAreaLimitAmount.set(op, n + dis);
                if (this._mapBetAreaLimitAmount.get(op) <= 0) {
                    this._mapBetAreaLimitAmount.set(op, 0);
                }
            }
        };

        // 共享限红
        if (this._humanboyRoom.roomParams.shareLimitAmount > 0) {
            if (betOption < network.BetZoneOption.POS_LUCK) {
                for (const area of this._vAreasInfo) {
                    let option: network.BetZoneOption = area.eZone;

                    // 改为总限红显示不变, 此处注释
                    // cb(option, disAmount);

                    this._updateBetAreaBetsNum(option);
                }
            } else {
                this._updateBetAreaBetsNum(betOption);
            }
        }
        // 非共享
        else {
            cb(betOption, disAmount);
            this._updateBetAreaBetsNum(betOption);
        }
    }

    /**
     * 更新下注区域是否可触摸
     */
    private _updateBetAreaTouchEnabled(): void {
        for (const area of this._vAreasInfo) {
            let pArea: HumanboyAreaInfo = area;
            let bEnabled = true;
            bEnabled = bEnabled && this._humanboyRoom.gameState.roundState === network.RoundState.BET;
            // bEnabled = bEnabled && !humanboyDataMgr.getHumanboyRoom().bOnDealerList;
            bEnabled = bEnabled && this._getLeftTime() > 0;
            if (bEnabled) {
                pArea.panelArea.resumeSystemEvents(false);
            } else {
                pArea.panelArea.pauseSystemEvents(false);
            }
        }
    }

    /**
     * 重置指定下注区域
     * @param betOption
     */
    private _resetBetArea(betOption: network.BetZoneOption): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let pArea: HumanboyAreaInfo = this._vAreasInfo[nAreaIdx];

        // 填充下注文本默认值
        this._updateBetAreaBetsNum(betOption, 0, 0);

        pArea.panelArea.getComponent(cc.BlockInputEvents).enabled = true;
        if (pArea.panelCard) {
            pArea.panelCard.active = false;
            pArea.imgCardTypeBg.node.active = false;
            pArea.imgCardTypeTxt.node.active = false;
        }

        // 重置牌组精灵
        this._resetPlayerCards(betOption, false);

        // 重置金币池
        this._resetBetAreaCoins(betOption);

        // 隐藏win动画
        this._hideWinFlagAnim(betOption);
    }

    /**
     * 重置所有下注区域
     */
    private _resetAllBetAreas(): void {
        for (const area of this._vAreasInfo) {
            this._resetBetArea(area.eZone);
        }

        // 重置金币池节点深度计数
        this._llCoinPoolZOrderCount = 0;

        // 清理"金币最优队列"
        this._vCoinOptimizationDeque.clear();
    }

    /**
     * 初始化下注按钮
     */
    private _initBetButtons(): void {
        for (let i = 0; i < this._nBetBtnNum; ++i) {
            let betCoin: HumanboyBetCoinControl = this._panel_betbtn
                .getChildByName(pf.StringUtil.formatC('btn_bet_%d', i))
                .getComponent(HumanboyBetCoinControl);
            betCoin.node.setScale(this._fBetBtnSrcScaleRate);
            betCoin.node.on('click', (event: cc.Event): void => {
                this._playSoundEffect(macros.Audio.Button);
                this._setBetButtonSelected(i);
            });
            this._vBetButtons.push(betCoin);
        }

        // 初始化高级续投面板
        if (!this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto = cc
                .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_ADVANCED_AUTO))
                .getComponent(HumanboyAdvancedAutoControl);
            this.node.addChild(
                this._humanboyAdvancedAuto.node,
                eHumanboyLocalZorder.HL_ZORDER_PANEL_ADVANCE_AUTO_SELECT
            );
        }

        // 续投按钮
        this._btn_betAuto = this._panel_betbtn.getChildByName('btn_bet_auto').getComponent(cc.Button);
        this._btn_betAuto.node.on('click', (event: cc.Event): void => {
            this._playSoundEffect(macros.Audio.Button);

            switch (this._eAutoBtnStyle) {
                // 常规续投点击
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                    this._humanboyRoom.autoBet().then(() => {
                        this._onMsgAutoBet();
                    });
                    // {
                    //     cv.humanboyNet.requestAutoBet();
                    // }
                    break;

                // 高级续投已激活(再次点击 弹出高级续投选项面板)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                    // if (this._humanboyRoom.gameState.roundState === humanboy.RoundState.BET) {
                    this._humanboyAdvancedAuto.adaptSelectPanelPos(this._btn_betAuto.node);
                    this._humanboyAdvancedAuto.showSelectPanel(true);
                    // }
                    break;

                // 高级续投中(再次点击取消)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                    {
                        let iUsedAutoBetCount: number = this._humanboyRoom.betSettings.usedAutoBetCount;
                        let iSelectAutoBetCount: number = this._humanboyRoom.betSettings.selectAutoBetCount;

                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_DIALOG)
                        );
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, eHumanboyLocalZorder.HL_ZORDER_PANEL_SERVER_TOAST);

                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const stringContent = pf.StringUtil.formatC(
                            pf.languageManager.getString('Cowboy_auto_bet_stop_tips'),
                            iUsedAutoBetCount,
                            iSelectAutoBetCount
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_stop_auto_bet');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_resume_auto_bet');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            this._humanboyRoom.cancelAdavnceAutoBet();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            miniGameDialog?.close();
                        };
                        const stringCenter = pf.languageManager.getString('MiniGame_AddAutoBet_Text');
                        const cbCenterBtn = (dialog: MiniGameDialog) => {
                            this.showAutoAddBetList(dialog);
                        };

                        const _onUpdateContent = (dialog: IMiniGameDialog) => {
                            if (legacyDialog) {
                                legacyDialog.txt_content.string = pf.StringUtil.calculateAutoWrapString(
                                    legacyDialog.txt_content.node,
                                    pf.StringUtil.formatC(
                                        pf.languageManager.getString('Cowboy_auto_bet_stop_tips'),
                                        this._humanboyRoom.betSettings.usedAutoBetCount,
                                        this._humanboyRoom.betSettings.selectAutoBetCount
                                    )
                                );
                            }
                            if (this._humanboyRoom.betSettings.reachLimitBet) {
                                miniGameDialog.blockCenterButton();
                            }
                        };
                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: this._humanboyRoom.betSettings.reachLimitBet,
                            legacyDialog,
                            isShowBtnCenter: true,
                            stringCenterButton: stringCenter,
                            cbCenterBtn,
                            onUpdateContent: _onUpdateContent
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                    }
                    break;

                default:
                    break;
            }
        });

        // 清屏按钮
        this._btn_betClean = this._panel_betbtn.getChildByName('btn_bet_clean').getComponent(cc.Button);
        this._btn_betClean.normalSprite = this._atlas_cb_language.getSpriteFrame('clean_screen_normal');
        this._btn_betClean.pressedSprite = this._atlas_cb_language.getSpriteFrame('clean_screen_normal');
        this._btn_betClean.hoverSprite = this._atlas_cb_language.getSpriteFrame('clean_screen_normal');
        this._btn_betClean.disabledSprite = this._atlas_cb_language.getSpriteFrame('clean_screen_gray');
        this._btn_betClean.node.on('click', (event: cc.Event): void => {
            pf.audioManager.playSoundEffect(macros.Audio.Button);
            this._resetAllBetAreaCoins();
        });

        // 默认选中第一个下注按钮
        this._setBetButtonSelected(0, false);
    }

    /**
     * 初始化红包等相关按钮入口
     */
    private _initRedPackage(): void {
        // 红包节按钮
        this._btn_redpacket_festival = this._panel_betbtn.getChildByName('btn_redpacket_festival');
        this._btn_redpacket_festival.getComponent(cc.Sprite).spriteFrame = null;
        this._btn_redpacket_festival.active = false;

        // 红包节按钮提示层
        this._btn_redpacket_festival_layer = cc.instantiate(this._btn_redpacket_festival);
        this.node.addChild(this._btn_redpacket_festival_layer, eHumanboyLocalZorder.HL_ZORDER_PANEL_RED_PACKET);

        let wpos: cc.Vec2 = cc.Vec2.ZERO;
        this._btn_redpacket_festival.convertToWorldSpaceAR(cc.Vec2.ZERO, wpos);
        this._btn_redpacket_festival_layer.setPosition(
            this._btn_redpacket_festival_layer.parent.convertToNodeSpaceAR(wpos)
        );

        // 初始执行一次
        this._onMsgShowLuckButton(pf.client.RedPacketLotteryMode.Classical);
    }

    /**
     * 重置指定下注按钮
     * @param index
     * @param enabled
     */
    private _resetBetButton(index: number, enabled: boolean): void {
        if (index < 0 || index >= this._vBetButtons.length) return;

        this._vBetButtons[index].btn.enabled = enabled;
        this._vBetButtons[index].node.setScale(this._fBetBtnSrcScaleRate);
        this._vBetButtons[index].txtBetNode.active = true;
        this._vBetButtons[index].imgMask.node.active = false;

        if (this._nCurBetBtnIndex === index) this._nCurBetBtnIndex = -1;
    }

    /**
     * 重置下注按钮
     * @param enabled
     */
    private _resetAllBetButtons(enabled: boolean): void {
        for (let i = 0; i < this._vBetButtons.length; ++i) {
            this._resetBetButton(i, enabled);
        }
    }

    private _updateBetAmountLevel(): void {
        let vBetCoinOption: number[] = this._humanboyRoom.betSettings.betCoinOptions;
        for (let i = 0; i < vBetCoinOption.length; ++i) {
            if (i < this._nBetBtnNum) {
                let llAmountLevel: number = pf.StringUtil.clientGoldByServer(vBetCoinOption[i]);
                this._vBetButtons[i].setTxtNum(pf.StringUtil.numberToShowNumber(llAmountLevel));
                if (llAmountLevel < this._humanboyRoom.llCoinUICritical) {
                    this._vBetButtons[i].setShape(HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_COIN);
                } else {
                    this._vBetButtons[i].setShape(HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_BLOCK);
                }
            } else {
                console.error(
                    pf.StringUtil.formatC(
                        'HumanboyMainView._updateBetAmountLevel vBetCoinOption must be %d, size: %d',
                        this._nBetBtnNum,
                        vBetCoinOption.length
                    )
                );
            }
        }

        switch (this._humanboyRoom.betSettings.autoBetLevel) {
            case pf.client.session.AutoBetLevel.Level_Normal:
                this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL);
                break;

            case pf.client.session.AutoBetLevel.Level_Advance:
                if (this._humanboyRoom.betSettings.selectAutoBetCount > 0) {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING);
                } else {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE);
                }
                break;

            default:
                break;
        }

        this._adaptiveBetBtnPanel();
    }

    /**
     * 更新赔率
     */
    private _updateBetOddsDetail(): void {
        const vOddsDetail = this._humanboyRoom.roomParams.oddsDetails;
        for (const detail of vOddsDetail) {
            let llOdds: number = detail.odds;
            let option: network.BetZoneOption = detail.option;
            for (const area of this._vAreasInfo) {
                if (area.eZone > network.BetZoneOption.POS_LUCK && area.eZone === option) {
                    if (area.txtOdds) {
                        let str_odds: string = pf.StringUtil.clientGoldByServer(llOdds).toString();
                        str_odds = pf.StringUtil.formatC(
                            '%s%s',
                            str_odds,
                            pf.languageManager.getString('Humanboy_game_fnt_table_odd')
                        );
                        area.txtOdds.string = str_odds;
                    }
                }
            }
        }
    }

    /**
     * 更新下注按钮状态
     * @param bCheckCoin 是否检测金币数量(默认:true, 之所以提出来一个参数, 是因为一局结束通知也调用了该函数, 为了避免一局结束提前通过按钮状态知道输赢结果, 显示更友好)
     */
    private _updateBetButtonState(bCheckCoin: boolean = true): void {
        // 检测下注按钮禁用与否

        let vBetCoinOption: number[] = this._humanboyRoom.betSettings.betCoinOptions; // 房间下注级别
        let curCoin: number = this._humanboyRoom.selfPlayer.curCoin; // 当前自身携带金币
        for (let i = 0; i < vBetCoinOption.length; ++i) {
            // 钱是否够按钮上的金额
            if (curCoin >= vBetCoinOption[i]) {
                this._vBetButtons[i].btn.enabled = true;
                this._vBetButtons[i].btn.interactable = true;
                this._vBetButtons[i].setTxtColor(HumanboyBetCoinControl.eHumanboyBetCoinTxtColor.YELLOW);
            } else {
                this._vBetButtons[i].btn.enabled = false;
                this._vBetButtons[i].btn.interactable = false;
                this._vBetButtons[i].setTxtColor(HumanboyBetCoinControl.eHumanboyBetCoinTxtColor.GRAY);
            }
        }

        // 检测下注按钮可触摸与否
        let bEffective: boolean =
            this._humanboyRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0;
        for (const button of this._vBetButtons) {
            button.imgMask.node.active = !bEffective;
            button.imgMask.getComponent(cc.BlockInputEvents).enabled = true;
        }

        // 更新续投按钮状态
        this._updateAutoBetBtnStatus();

        // 更新清屏按钮状态
        this._updateCleanBtnStatus();
    }

    /**
     * 更新续投按钮状态
     */
    private _updateAutoBetBtnStatus(): void {
        switch (this._eAutoBtnStyle) {
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                if (this._humanboyRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0) {
                    // 当前一局下过注
                    if (this._humanboyRoom.roundInfo.hasBetInCurRound) {
                        this._btn_betAuto.interactable = false;
                    } else {
                        let canAuto: boolean = this._humanboyRoom.betSettings.canAutoBet;
                        this._btn_betAuto.interactable = canAuto;
                    }
                } else {
                    this._btn_betAuto.interactable = false;
                }
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                // 当前一局下过注
                if (this._humanboyRoom.roundInfo.hasBetInCurRound) {
                    this._btn_betAuto.interactable = true;
                } else {
                    let canAuto = this._humanboyRoom.betSettings.canAutoBet;
                    this._btn_betAuto.interactable = canAuto;
                }
                break;
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                this._btn_betAuto.interactable = true;
                break;

            default:
                break;
        }
    }

    /**
     * 更新清屏按钮状态
     */
    private _updateCleanBtnStatus(): void {
        let bEnable = false;
        if (this._humanboyRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0) {
            bEnable = true;
        }
        this._btn_betClean.getComponent(cc.Button).interactable = bEnable;
    }

    /**
     * 设置续投按钮样式
     * @param eAutoBtnStyle
     */
    private _setAutoBetBtnStytle(eAutoBtnStyle: MiniGameCommonDef.eGameboyAutoBtnStyle) {
        // 隐藏高级续投子面板
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.hideAdvanceAutoTips();
            this._humanboyAdvancedAuto.hideAdvanceAutoCount();
            this._humanboyAdvancedAuto.hideSelectPanel(false);
        }

        // 更新续投按钮样式
        this._eAutoBtnStyle = eAutoBtnStyle;
        switch (this._eAutoBtnStyle) {
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NONE:
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                this._btn_betAuto.normalSprite = this._atlas_cb_language.getSpriteFrame('autobet_normal');
                this._btn_betAuto.pressedSprite = this._atlas_cb_language.getSpriteFrame('autobet_normal');
                this._btn_betAuto.hoverSprite = this._atlas_cb_language.getSpriteFrame('autobet_normal');
                this._btn_betAuto.disabledSprite = this._atlas_cb_language.getSpriteFrame('autobet_gray');
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                this._btn_betAuto.normalSprite = this._atlas_cb_language.getSpriteFrame('autobet_block_normal');
                this._btn_betAuto.pressedSprite = this._atlas_cb_language.getSpriteFrame('autobet_block_normal');
                this._btn_betAuto.hoverSprite = this._atlas_cb_language.getSpriteFrame('autobet_block_normal');
                this._btn_betAuto.disabledSprite = this._atlas_cb_language.getSpriteFrame('autobet_block_gray');
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                this._btn_betAuto.normalSprite = this._atlas_cb_language.getSpriteFrame('autobet_block_using');
                this._btn_betAuto.pressedSprite = this._atlas_cb_language.getSpriteFrame('autobet_block_using');
                this._btn_betAuto.hoverSprite = this._atlas_cb_language.getSpriteFrame('autobet_block_using');
                this._btn_betAuto.disabledSprite = this._atlas_cb_language.getSpriteFrame('autobet_block_gray');

                if (this._humanboyAdvancedAuto) {
                    this._humanboyAdvancedAuto.adaptAdvanceAutoCountPos(this._btn_betAuto.node);
                    this._humanboyAdvancedAuto.showAdvanceAutoCount();
                }
                break;

            default:
                break;
        }

        let img_betAuto: cc.Sprite = this._btn_betAuto.getComponent(cc.Sprite);
        img_betAuto.type = cc.Sprite.Type.SIMPLE;
        img_betAuto.sizeMode = cc.Sprite.SizeMode.RAW;
    }

    /**
     * 获取续投按钮样式
     */
    private _getAutoBetBtnStytle(): MiniGameCommonDef.eGameboyAutoBtnStyle {
        return this._eAutoBtnStyle;
    }

    /**
     * 检测高级续投请求
     */
    private _checkAdvanceAutoReq(): void {
        if (
            this._humanboyRoom.gameState.roundState === network.RoundState.BET &&
            this._getAutoBetBtnStytle() === MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING
        ) {
            if (this._humanboyAdvancedAuto) {
                this._humanboyAdvancedAuto.hideAdvanceAutoTips();
            }

            if (this._humanboyRoom.betSettings.usedAutoBetCount < this._humanboyRoom.betSettings.selectAutoBetCount) {
                this._humanboyRoom
                    .advanceAutoBet()
                    .then(() => {
                        this._onMsgAdvanceAutobet(network.ErrorCode.OK);
                    })
                    .catch((err: pf.ServerError) => {
                        this._onMsgAdvanceAutobet(err.errorCode);
                    });
            }
        }
    }

    /**
     * 重置下个状态的截止时间
     */
    private _resetLeftTime(): void {
        this._nLeftTime = this._humanboyRoom.gameState.leftSeconds;

        this._msNowTime = 0;
        this._msLastTime = 0;

        // let date: Date = new Date();
        // let tv_usec: number = date.getMilliseconds();
        // this._msLastTime = tv_usec;
    }

    /**
     * 获取下个状态的截止时间
     */
    private _getLeftTime(): number {
        return this._nLeftTime;
    }

    /**
     * 显/隐下注计倒计时闹钟动画
     * @param bShow
     * @param bAnim
     */
    private _showBetClockAction(bShow: boolean, bAnim: boolean = true): void {
        // 复原
        this._img_bet_clock.active = true;
        this._img_bet_clock.stopAllActions();
        this._img_bet_clock.setPosition(this._src_bet_clock_pos);

        let worldPos: cc.Vec2 = cc.v2(0, cc.winSize.height / 2 + this._img_bet_clock.height / 2);
        this.node.convertToWorldSpaceAR(worldPos, worldPos);
        let nodePos: cc.Vec2 = cc.Vec2.ZERO;
        this._img_bet_clock.parent.convertToNodeSpaceAR(worldPos, nodePos);
        let pos: cc.Vec2 = cc.v2(this._src_bet_clock_pos.x, nodePos.y);

        let ftn_clock: cc.Label = this._img_bet_clock.getChildByName('txt').getComponent(cc.Label);
        ftn_clock.string = '1';

        if (bShow) {
            if (bAnim) {
                this._img_bet_clock.setPosition(pos);
                let mt: cc.ActionInterval = cc.moveTo(this._fActExecute_BetClock, this._src_bet_clock_pos);
                let ebo: cc.ActionInterval = mt.easing(cc.easeBackOut());
                this._img_bet_clock.runAction(ebo);
            } else {
                this._img_bet_clock.setPosition(this._src_bet_clock_pos);
            }
            ftn_clock.string = pf.StringUtil.formatC('%lld', this._getLeftTime());
        } else {
            if (bAnim) {
                this._img_bet_clock.setPosition(this._src_bet_clock_pos);
                let mt: cc.ActionInterval = cc.moveTo(this._fActExecute_BetClock, pos);
                let ebi: cc.ActionInterval = mt.easing(cc.easeBackIn());
                this._img_bet_clock.runAction(
                    cc.sequence(
                        ebi,
                        cc.callFunc((): void => {
                            this._img_bet_clock.active = false;
                        })
                    )
                );
            } else {
                this._img_bet_clock.setPosition(this._src_bet_clock_pos);
                this._img_bet_clock.active = false;
            }
        }
    }

    /**
     * 更新下注计时器面板
     */
    private _updateTimeBetClock(): void {
        if (this._humanboyRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0) {
            this.schedule(this._onTimeBetClock, 1.0);
            this._showBetClockAction(true, true);
        } else {
            this.unschedule(this._onTimeBetClock);
            this._showBetClockAction(false, true);
        }
    }

    /**
     * 更新下注倒计时
     * @param f32Delta
     */
    private _onTimeBetClock(f32Delta: number): void {
        if (this._humanboyRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0) {
            // this._playSoundEffect(macros.Audio.Time_Tick);
            this._playSoundEffect(macros.Audio.Time_Tick);
            this._showBetClockAction(true, false);
        } else {
            this.unschedule(this._onTimeBetClock);
            this._showBetClockAction(false, true);
        }
    }

    /**
     * 下一轮准备
     */
    private _showNextRoundPrepare(): void {
        // 维护状态:非0代表系统即将维护
        if (this._humanboyRoom.roundInfo.stopWorld !== 0) {
            this._showGameToast(pf.languageManager.getString('Humanboy_server_will_stop_text'));
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(2.0),
                    cc.callFunc((): void => {
                        this._backToRoomListScene();
                    }, this)
                )
            );
        } else {
            this._resetAllCards();
            this._resetAllBetAreaLimitAmount();
            this._resetAllBetAreas();
            this._restAllTimelineAnims();
            this._updateAllWayOut();

            // 更新庄家信息
            this._updateDealerInfo();

            // 恢复上庄列表面板中的"我要上庄按钮"点击状态
            if (this._dealerListView) {
                this._dealerListView.checkBtnDealerEnable();
            }

            // 下一局即将开始
            this._updateNextRoundCountDown();
        }
    }

    /**
     * 更新开局倒计时
     */
    private _updateNextRoundCountDown(): void {
        if (this._bWaitting) return;

        let nLeftTime: number = this._getLeftTime();
        if (this._humanboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND2 && nLeftTime > 0) {
            this.schedule(this._onTimeNextRoundCountDown, 1.0);
            this._showNextRoundCountDown(true, nLeftTime);
        } else {
            this.unschedule(this._onTimeNextRoundCountDown);
            this._showNextRoundCountDown(false, 0);
        }
    }

    /**
     * 更新开局倒计时
     * @param f32Delta
     */
    private _onTimeNextRoundCountDown(f32Delta: number): void {
        let eCurState: network.RoundState = this._humanboyRoom.gameState.roundState;
        let nLeftTime: number = this._getLeftTime();
        if (eCurState === network.RoundState.WAIT_NEXT_ROUND2 && nLeftTime > 0) {
            this._showNextRoundCountDown(true, nLeftTime);
        } else {
            this.unschedule(this._onTimeNextRoundCountDown);
            this._showNextRoundCountDown(false, 0);
        }
    }

    /**
     * 下一轮提示动画
     * @param bShow
     * @param fDelta
     */
    private _showNextRoundCountDown(bShow: boolean, fDelta: number): void {
        this._img_count_down.node.active = bShow;
        let txt: cc.Label = this._img_count_down.node.getChildByName('txt').getComponent(cc.Label);
        if (bShow) {
            txt.string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_game_tips_wait_next_round_text'),
                fDelta
            );
        } else {
            txt.string = '0';
        }
    }

    /**
     * 耐心等待下一局动画
     * @param bShow
     * @param fDelta
     */
    private _showNextRoundEnterCountDown(bShow: boolean, fDelta: number): void {
        this._img_count_down.node.active = bShow;
        let txt: cc.Label = this._img_count_down.node.getChildByName('txt').getComponent(cc.Label);
        if (bShow) {
            txt.string = pf.StringUtil.formatC(
                pf.languageManager.getString('Humanboy_game_tips_wait_next_enter_text'),
                fDelta
            );
        } else {
            txt.string = '0';
        }
    }

    /**
     * 显示刚进入房间等待时间
     * @param bShow
     */
    private _showWaittingTime(bShow: boolean): void {
        this._nWaittingTime = this._getLeftTime();
        if (
            bShow &&
            this._humanboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND &&
            this._nWaittingTime >= 0
        ) {
            this._bWaitting = true;
            this.schedule(this._onTimeWaitting, 1.0);
            this._showNextRoundEnterCountDown(true, this._nWaittingTime);
        } else {
            this._bWaitting = false;
            this._nWaittingTime = 0;
            this.unschedule(this._onTimeWaitting);
            this._showNextRoundEnterCountDown(false, 0);
        }
    }

    private _onTimeWaitting(f32Delta: number): void {
        let eCurState: network.RoundState = this._humanboyRoom.gameState.roundState;
        if (--this._nWaittingTime >= 0) {
            this._showNextRoundEnterCountDown(true, this._nWaittingTime);
        } else {
            this._showWaittingTime(false);
        }
    }

    /**
     * 加载时间轴动画文件
     */
    private _initTimelineAnims(): void {
        this._nodeAnim = new cc.Node();
        this._nodeAnim.setContentSize(cc.winSize);
        this._nodeAnim.setAnchorPoint(cc.v2(0.5, 0.5));
        this._nodeAnim.setPosition(cc.Vec2.ZERO);
        this.node.addChild(this._nodeAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE);

        // win 旗子动画
        do {
            for (const area of this._vAreasInfo) {
                let size: cc.Size = area.panelCoin.getContentSize();
                let pos: cc.Vec2 = cc.Vec2.ZERO;
                area.panelCoin.convertToWorldSpaceAR(cc.v2(0, -170), pos);
                let nodeWinAnim = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.WIN_FLAG));
                this._nodeAnim.addChild(nodeWinAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_1);

                nodeWinAnim.parent.convertToNodeSpaceAR(pos, pos);
                nodeWinAnim.setPosition(pos);
                nodeWinAnim.active = false;

                this._vNodeWinFlagAnims.push(nodeWinAnim);
                this._vAtlWinFlagActions.push(nodeWinAnim.getComponent(cc.Animation));
            }
        } while (false);

        // 开始下注动画
        do {
            this._nodeFightBeginAnim = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.START_BETS));
            this._nodeAnim.addChild(this._nodeFightBeginAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_1);

            this._nodeFightBeginAnim.setPosition(cc.Vec2.ZERO);
            this._nodeFightBeginAnim.active = false;

            this._atlFightBeginAction = this._nodeFightBeginAnim.getComponent(cc.Animation);

            // 切换语言, 切换资源
            let img_start_bet_node: cc.Node = this._nodeFightBeginAnim.getChildByName('Sprite_2');
            img_start_bet_node.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_GAME_RUN_SPRITE_002
            );
            // cv.resMgr.setSpriteFrame(
            //     img_start_bet_node,
            //     cv.config.getLanguagePath('game/humanboy/animation/game_round/002')
            // );

            let img_choose_box_node: cc.Node = this._nodeFightBeginAnim.getChildByName('Sprite_7');
            img_choose_box_node.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_GAME_RUN_SPRITE_004
            );
            // cv.resMgr.setSpriteFrame(
            //     img_choose_box_node,
            //     cv.config.getLanguagePath('game/humanboy/animation/game_round/004')
            // );
        } while (false);

        // 停止下注动画
        do {
            this._nodeFightEndAnim = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.END_BETS));
            this._nodeAnim.addChild(this._nodeFightEndAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_1);

            this._nodeFightEndAnim.setPosition(cc.Vec2.ZERO);
            this._nodeFightEndAnim.active = false;

            this._atlFightEndAction = this._nodeFightEndAnim.getComponent(cc.Animation);

            // 切换语言, 切换资源
            let img_clos_bet_node: cc.Node = this._nodeFightEndAnim.getChildByName('Sprite_2');
            img_clos_bet_node.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_GAME_RUN_SPRITE_003
            );
            // cv.resMgr.setSpriteFrame(
            //     img_clos_bet_node,
            //     cv.config.getLanguagePath('game/humanboy/animation/game_round/003')
            // );

            let img_times_up_node: cc.Node = this._nodeFightEndAnim.getChildByName('Sprite_7');
            img_times_up_node.getComponent(cc.Sprite).spriteFrame = pf.addressableAssetManager.getAsset(
                macros.Assets.HUMANBOY_GAME_RUN_SPRITE_005
            );
            // cv.resMgr.setSpriteFrame(
            //     img_times_up_node,
            //     cv.config.getLanguagePath('game/humanboy/animation/game_round/005')
            // );
        } while (false);

        // 庄家完胜完败(该预制件比较特殊, 不仅资源分语言, 模型也分语言)
        do {
            let prefab_hb_dealer_victory: cc.Prefab = null;
            let prefab_hb_dealer_defeat: cc.Prefab = null;

            switch (pf.languageManager.currentLanguage) {
                case pf.LANGUAGE_GROUPS.zh_CN:
                    prefab_hb_dealer_victory = this.prefab_hb_dealer_victory_zh_CN;
                    prefab_hb_dealer_defeat = this.prefab_hb_dealer_defeat_zh_CN;
                    break;

                default:
                    prefab_hb_dealer_victory = this.prefab_hb_dealer_victory_en_US;
                    prefab_hb_dealer_defeat = this.prefab_hb_dealer_defeat_en_US;
                    break;
            }

            // 完胜
            this._nodeDealerVictoryAnim = cc.instantiate(prefab_hb_dealer_victory);
            this._nodeAnim.addChild(this._nodeDealerVictoryAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_1);
            this._nodeDealerVictoryAnim.setPosition(cc.Vec2.ZERO);
            this._nodeDealerVictoryAnim.active = false;
            this._atlDealerVictoryAction = this._nodeDealerVictoryAnim.getComponent(cc.Animation);

            // 完败
            this._nodeDealerDefeatAnim = cc.instantiate(prefab_hb_dealer_defeat);
            this._nodeAnim.addChild(this._nodeDealerDefeatAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_1);
            this._nodeDealerDefeatAnim.setPosition(cc.Vec2.ZERO);
            this._nodeDealerDefeatAnim.active = false;
            this._atlDealerDefeatAction = this._nodeDealerDefeatAnim.getComponent(cc.Animation);
        } while (false);

        // 路单闪光动画
        do {
            let pos: cc.Vec2 = cc.Vec2.ZERO;
            this._btn_record.node.parent.convertToWorldSpaceAR(this._btn_record.node.getPosition(), pos);

            this._nodeWayoutLightAnim = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.WAY_OUT));
            this._nodeAnim.addChild(this._nodeWayoutLightAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_1);

            this._nodeWayoutLightAnim.setPosition(this._nodeWayoutLightAnim.parent.convertToNodeSpaceAR(pos));
            this._nodeWayoutLightAnim.active = false;

            this._atlWayoutLightAction = this._nodeWayoutLightAnim.getComponent(cc.Animation);
        } while (false);
    }

    /**
     * 隐藏所有timeline动画
     */
    private _restAllTimelineAnims(): void {
        for (const area of this._vAreasInfo) {
            this._hideWinFlagAnim(area.eZone);
        }

        this._atlFightBeginAction.stop();
        this._nodeFightBeginAnim.active = false;

        this._atlFightEndAction.stop();
        this._nodeFightEndAnim.active = false;

        this._atlDealerVictoryAction.stop();
        this._nodeDealerVictoryAnim.active = false;

        this._atlDealerDefeatAction.stop();
        this._nodeDealerDefeatAnim.active = false;

        this._atlWayoutLightAction.stop();
        this._nodeWayoutLightAnim.active = false;

        this._clearJackPotAnim();
        this._clearSpecialCardTypeAnim();
        this._clearSpecialCardTypeHighLightAnim();
        this._hideAllWinPlayerLightAnim();

        this._nodeAnim.stopAllActions();
    }

    /**
     * 按指定动画的持续时间计算该动画播放速度(应用于所有动画剪辑)
     * @param anim
     * @param executeTime
     */
    private _getAnimClipSpeedByDuring(animClip: cc.AnimationClip, executeTime: number): number {
        let speed: number = animClip.speed;
        if (executeTime > 0) {
            let totalFrames: number = animClip.sample * animClip.duration;
            speed = totalFrames / cc.game.getFrameRate() / executeTime;
        }
        return speed;
    }

    /**
     * 开局动画
     */
    private _showRoundStartAnim(): void {
        // 开始发牌
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._fActDelayed_SendCard),
                cc.callFunc((): void => {
                    this._sendAllCardsAnim();
                })
            )
        );

        // 开始下注与发完牌动画一致
        let fDelayedTime: number =
            this._fActDelayed_SendCard +
            this._fActExecute_SendCard -
            this._fActDelayed_FightBegin -
            this._fActExecute_FightBegin;
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(fDelayedTime),
                cc.callFunc((): void => {
                    this._showFightBeginAnim();
                }, this)
            )
        );
    }

    /**
     * 开始下注动画
     */
    private _showFightBeginAnim(): void {
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._fActDelayed_FightBegin),
                cc.callFunc((): void => {
                    this._playSoundEffect(macros.Audio.Begin_Bet);
                    this._nodeFightBeginAnim.active = true;

                    let start_bets: cc.AnimationClip = this._atlFightBeginAction.defaultClip;
                    start_bets.speed = this._getAnimClipSpeedByDuring(start_bets, this._fActExecute_FightBegin);
                    start_bets.wrapMode = cc.WrapMode.Normal;

                    this._atlFightBeginAction.play();
                    this._atlFightBeginAction.on(
                        cc.Animation.EventType.FINISHED,
                        (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                            this._atlFightBeginAction.off(cc.Animation.EventType.FINISHED);
                            this._nodeFightBeginAnim.active = false;
                        },
                        this
                    );
                }, this)
            )
        );
    }

    /**
     * 停止下注 => 翻牌 => win标记检测 => 庄家完胜完败检测 => jackpot检测 => 特殊牌型检测 => 飞庄家金币 => 飞闲家金币 => 清屏 => 等待下一局
     */
    private _showRoundEndAnim(): void {
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._fActDelayed_FightEnd),
                cc.callFunc((): void => {
                    this._playSoundEffect(macros.Audio.End_Bet);
                    this._nodeFightEndAnim.active = true;

                    let end_bets: cc.AnimationClip = this._atlFightEndAction.defaultClip;
                    end_bets.speed = this._getAnimClipSpeedByDuring(end_bets, this._fActExecute_FightEnd);
                    end_bets.wrapMode = cc.WrapMode.Normal;

                    this.playPointAni();
                    this._atlFightEndAction.play();
                    this._atlFightEndAction.on(
                        cc.Animation.EventType.FINISHED,
                        (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                            this._atlFightEndAction.off(cc.Animation.EventType.FINISHED);
                            this._nodeFightEndAnim.active = false;
                            this._showAllCardsAnim();
                        },
                        this
                    );
                }, this)
            )
        );
    }

    /**
     * 发牌阶段(包含庄家,闲家等发牌动作)
     */
    private _sendAllCardsAnim(): void {
        this._resetAllCards();
        this._sendDealerCardsAnim(this._sendOneCardCallBack);
        this._sendPlayerCardsAnim(this._sendOneCardCallBack);
    }

    /**
     * 翻牌阶段(包含庄家,闲家等翻牌动作)
     * @param anim
     */
    private _showAllCardsAnim(anim: boolean = true): void {
        let vPlayerHoleCard = this._humanboyRoom.roundInfo.playerHoleCard;

        let fDelayedTime1 = 0;
        let fDelayedTime2 = 0;
        for (const card of vPlayerHoleCard) {
            let option: network.BetZoneOption = card.option;
            switch (option) {
                // 庄
                case network.BetZoneOption.HOST:
                    if (anim) {
                        fDelayedTime1 = this._fActDelayed_ShowCard_Step_1;
                        fDelayedTime2 =
                            fDelayedTime1 + this._fActExecute_ShowCard_Step_1 + this._fActExecute_ShowCard_Step_2;
                    }

                    // 翻牌
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime1),
                            cc.callFunc((): void => {
                                this._playSoundEffect(macros.Audio.Kaipai);
                                this._showDealerCardsAnim(card, this._showOneCardCallBack, anim);
                            }, this)
                        )
                    );

                    // 翻牌型
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime2),
                            cc.callFunc((): void => {
                                this._showCardType(option);
                            }, this)
                        )
                    );
                    break;

                // 闲
                case network.BetZoneOption.POS1:
                case network.BetZoneOption.POS2:
                case network.BetZoneOption.POS3:
                case network.BetZoneOption.POS4:
                    if (anim) {
                        fDelayedTime1 =
                            this._fActDelayed_ShowCard_Step_2 +
                            fDelayedTime1 +
                            this._fActExecute_ShowCard_Step_1 +
                            this._fActExecute_ShowCard_Step_2;
                        fDelayedTime2 =
                            this._fActDelayed_ShowCard_Step_2 +
                            fDelayedTime1 +
                            this._fActExecute_ShowCard_Step_1 +
                            this._fActExecute_ShowCard_Step_2;
                    }

                    // 翻牌
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime1),
                            cc.callFunc((): void => {
                                this._playSoundEffect(macros.Audio.Kaipai);
                                this._showPlayerCardsAnim(card, this._showOneCardCallBack, anim);
                            }, this)
                        )
                    );

                    // 翻牌型, 显示win动画
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime2),
                            cc.callFunc((): void => {
                                this._showCardType(option);

                                let zoneData: domain.BetZone = this._humanboyRoom.betZones.get(option);
                                if (zoneData && zoneData.optionResult.result < 0) {
                                    this._showWinFlagAnim(option);
                                }
                            }, this)
                        )
                    );
                    break;

                default:
                    break;
            }
        }
    }

    /**
     * 发完一张牌回调
     */
    private _sendOneCardCallBack(): void {
        // 发完所有牌 . 开战动画
        if (++this._nSendCardsCallBackNum >= this._nSendCardsTotalNum) {
            this._nSendCardsCallBackNum = 0;
            this._img_dealer_card_shield.active = false;
            // this._showFightBeginAnim();
        }
    }

    /**
     * 翻完一张牌回调
     */
    private _showOneCardCallBack(): void {
        // 开完牌
        if (++this._nSendCardsCallBackNum >= this._nSendCardsTotalNum) {
            this._nSendCardsCallBackNum = 0;
            this._showCardTypeFinish();
        }
    }

    /**
     * 发庄家牌动画
     * @param func
     */
    private _sendDealerCardsAnim(func: () => void): void {
        let fInterval: number = this._fActExecute_SendCard / (this._vDealerCardNode.length + 1);
        for (let i = 0; i < this._vDealerCardNode.length; ++i) {
            let card = this._vDealerCardNode[i];
            let pos: cc.Vec2 = this._vDealerCardSrcPos[i];
            card.node.setPosition(pos.x, pos.y - card.node.height / 2);

            let delayTime: number = i * fInterval;
            this.scheduleOnce((elapsed: number): void => {
                card.node.active = true;
                card.node.runAction(
                    cc.sequence(
                        cc.callFunc((): void => {
                            this._playSoundEffect(macros.Audio.Fapai);
                        }, this),
                        cc.moveTo(fInterval, pos).easing(cc.easeInOut(1.0)),
                        cc.callFunc(func, this)
                    )
                );
            }, delayTime);
        }
    }

    /**
     * 发闲家牌动画
     * @param func
     */
    private _sendPlayerCardsAnim(func: () => void): void {
        let fInterval: number = this._fActExecute_SendCard / (this._vAreasInfo.length + 1);
        for (const area of this._vAreasInfo) {
            if (area.panelCard) area.panelCard.active = true;
            for (let j = 0; j < area.vCardsNode.length; ++j) {
                let card = area.vCardsNode[j];
                let pos: cc.Vec2 = area.vCardsSrcPos[j];
                card.node.setPosition(pos.x, pos.y - card.node.height / 2);

                let delayTime: number = j * fInterval;
                this.scheduleOnce((elapsed: number): void => {
                    card.node.active = true;
                    card.node.runAction(
                        cc.sequence(cc.moveTo(fInterval, pos).easing(cc.easeInOut(1.0)), cc.callFunc(func, this))
                    );
                }, delayTime);
            }
        }
    }

    /**
     * 翻庄家牌动画
     * @param playerHoleCard
     * @param func
     * @param anim
     */
    private _showDealerCardsAnim(playerHoleCard: domain.PlayerHoleCard, func: () => void, anim: boolean = true): void {
        let vCardItem = playerHoleCard.holeCards;
        for (let i = 0; i < this._vDealerCardNode.length; ++i) {
            if (i >= vCardItem.length) break;
            let card = this._vDealerCardNode[i];
            let pos1: cc.Vec2 = this._vDealerCardSrcPos[0];
            let pos2: cc.Vec2 = this._vDealerCardSrcPos[i];

            if (anim) {
                card.node.runAction(
                    cc.sequence(
                        cc.moveTo(this._fActExecute_ShowCard_Step_1, pos1),
                        cc.callFunc((): void => {
                            card.SetContent(vCardItem[i].number, vCardItem[i].suit);
                            card.SetFace(true);
                        }, this),
                        cc.moveTo(this._fActExecute_ShowCard_Step_2, pos2),
                        cc.callFunc(func, this)
                    )
                );
            } else {
                card.node.setPosition(pos2);
                card.SetContent(vCardItem[i].number, vCardItem[i].suit);
                card.SetFace(true);
            }
        }
    }

    /**
     * 翻闲家牌动画
     * @param playerHoleCard
     * @param func
     * @param anim
     */
    private _showPlayerCardsAnim(playerHoleCard: domain.PlayerHoleCard, func: () => void, anim: boolean = true): void {
        let option: network.BetZoneOption = playerHoleCard.option;
        let nAreaIdx: number = this._getAreaIdxByBetOption(option);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let vCardItem = playerHoleCard.holeCards;
        for (let i = 0; i < this._vAreasInfo[nAreaIdx].vCardsNode.length; ++i) {
            if (i >= vCardItem.length) break;
            let card = this._vAreasInfo[nAreaIdx].vCardsNode[i];
            let pos1: cc.Vec2 = this._vAreasInfo[nAreaIdx].vCardsSrcPos[0];
            let pos2: cc.Vec2 = this._vAreasInfo[nAreaIdx].vCardsSrcPos[i];

            if (anim) {
                card.node.runAction(
                    cc.sequence(
                        cc.moveTo(this._fActExecute_ShowCard_Step_1, pos1),
                        cc.callFunc((): void => {
                            card.SetContent(vCardItem[i].number, vCardItem[i].suit);
                            card.SetFace(true);
                        }, this),
                        cc.moveTo(this._fActExecute_ShowCard_Step_2, pos2),
                        cc.callFunc(func, this)
                    )
                );
            } else {
                card.node.setPosition(pos2);
                card.SetContent(vCardItem[i].number, vCardItem[i].suit);
                card.SetFace(true);
            }
        }
    }

    /**
     * 显示牌型(比牌)
     * @param betOption
     */
    private _showCardType(betOption: network.BetZoneOption): void {
        let str_card_type = '';

        const vPlayerHoleCard = this._humanboyRoom.roundInfo.playerHoleCard;
        for (const card of vPlayerHoleCard) {
            if (card.option === betOption) {
                // 与庄家比大小的结果 = 0 相同 > 0 庄家赢 < 0 闲家赢
                // let result: number = vPlayerHoleCard[i].result;

                // 成牌牌型
                switch (card.level) {
                    case network.CardResult.CardResult_Dummy:
                        break;
                    case network.CardResult.GAO_PAI:
                        str_card_type = 'humanboy_card_type_high_card';
                        break;
                    case network.CardResult.YI_DUI:
                        str_card_type = 'humanboy_card_type_pair';
                        break;
                    case network.CardResult.LIAN_DUI:
                        str_card_type = 'humanboy_card_type_two_pair';
                        break;
                    case network.CardResult.SAN_TIAO:
                        str_card_type = 'humanboy_card_type_three_of_a_kind';
                        break;
                    case network.CardResult.SHUN_ZI:
                        str_card_type = 'humanboy_card_type_straight';
                        break;
                    case network.CardResult.TONG_HUA:
                        str_card_type = 'humanboy_card_type_flush';
                        break;
                    case network.CardResult.HU_LU:
                        str_card_type = 'humanboy_card_type_gourd';
                        break;
                    case network.CardResult.SI_TIAO:
                        str_card_type = 'humanboy_card_type_four_of_a_kind';
                        break;
                    case network.CardResult.TONG_HUA_SHUN:
                        str_card_type = 'humanboy_card_type_straight_flush';
                        break;
                    case network.CardResult.HUANG_TONG:
                        str_card_type = 'humanboy_card_type_royal_flush';
                        break;
                    default:
                        break;
                }

                switch (card.option) {
                    // 庄
                    case network.BetZoneOption.HOST:
                        this._img_dealer_card_type.node.parent.active = true;
                        this._img_dealer_card_type.spriteFrame = this._atlas_hb_language.getSpriteFrame(str_card_type);
                        break;

                    // 闲
                    case network.BetZoneOption.POS1:
                    case network.BetZoneOption.POS2:
                    case network.BetZoneOption.POS3:
                    case network.BetZoneOption.POS4:
                        {
                            let nAreaIdx: number = this._getAreaIdxByBetOption(card.option);
                            if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) break;

                            this._vAreasInfo[nAreaIdx].imgCardTypeBg.node.active = true;
                            this._vAreasInfo[nAreaIdx].imgCardTypeTxt.node.active = true;
                            this._vAreasInfo[nAreaIdx].imgCardTypeTxt.spriteFrame =
                                this._atlas_hb_language.getSpriteFrame(str_card_type);
                        }
                        break;

                    default:
                        break;
                }
                break;
            }
        }
    }

    /**
     * 显示牌型(比牌)
     */
    private _showAllCardType(): void {
        const vPlayerHoleCard = this._humanboyRoom.roundInfo.playerHoleCard;
        for (const card of vPlayerHoleCard) {
            this._showCardType(card.option);
        }
    }

    /* eslint complexity: ["error", { max: 30 }] */
    /* eslint-disable no-fallthrough */
    /**
     * 比牌完毕
     * @param nStep
     */
    private _showCardTypeFinish(nStep: number = 0): void {
        let fDelayedTime = 0; // 总体节奏时间
        let bShowDealerScore = false; // 庄家是否飘分

        switch (nStep) {
            // 庄家完胜完败检测(1 - 代表庄家完胜, 2 - 代表庄家玩败)
            case 0: {
                let fActDelayed_VictoryOrDefeat = 0;
                let fActExecute_VictoryOrDefeat = 0;

                let uDealerWinAll: number = this._humanboyRoom.roundInfo.dealerWinAll;
                if (uDealerWinAll === 1 || uDealerWinAll === 2) {
                    fActDelayed_VictoryOrDefeat = this._fActDelayed_VictoryOrDefeat;
                    fActExecute_VictoryOrDefeat = this._fActExecute_VictoryOrDefeat;

                    fDelayedTime += fActDelayed_VictoryOrDefeat;
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime),
                            cc.callFunc((): void => {
                                this._showDealerVictoryOrDefeatAnim();
                            }, this)
                        )
                    );
                    fDelayedTime += fActExecute_VictoryOrDefeat;
                }
            }

            // 显示幸运一击区域win动画
            case 1: {
                let fTmpDelayedTime1: number = fDelayedTime;
                let fTmpDelayedTime2: number = fDelayedTime;

                // 击中"庄家通吃"win动画
                let zoneData: domain.BetZone = this._humanboyRoom.betZones.get(network.BetZoneOption.POS_LUCK_1);
                if (zoneData && zoneData.optionResult.result < 0) {
                    fTmpDelayedTime1 += this._fActDelayed_ShowWinFlag;
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fTmpDelayedTime1),
                            cc.callFunc((): void => {
                                this._showWinFlagAnim(network.BetZoneOption.POS_LUCK_1);
                            }, this)
                        )
                    );
                }

                // 击中"特殊牌型"win动画
                if (this._isHitSpecialCardType()) {
                    fTmpDelayedTime2 += this._fActDelayed_ShowWinFlag;
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fTmpDelayedTime2),
                            cc.callFunc((): void => {
                                this._showSpecialCardTypeHighLightAnim();
                                this._showWinFlagAnim(this._getHitSpecialCardTypeZone());
                            }, this)
                        )
                    );
                }

                fDelayedTime = Math.max(fTmpDelayedTime1, fTmpDelayedTime2);
            }

            // 路单动画
            case 2: {
                fDelayedTime += this._fActDelayed_ShowWinFlag;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._showAllWayOutAnim();
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecute_WayOut;
            }

            // 特殊牌型检测
            case 3: {
                if (this._isHitSpecialCardType()) {
                    // 该牌型是否有动画
                    if (this._getSpecialCardTypeExecuteTime() > 0) {
                        fDelayedTime += this._fActDelayed_LuckBlow;
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(fDelayedTime),
                                cc.callFunc((): void => {
                                    this._showSpecialCardTypeAnim();
                                }, this)
                            )
                        );
                        fDelayedTime += this._getSpecialCardTypeExecuteTime();
                    }
                }
            }

            // 飞庄家金币 + 系统收取幸运一击未击中的金币
            case 4: {
                let fDealerCoinRecycleDelayedTime = fDelayedTime; // 庄家收取四门金币延时时间
                let fSystemCoinRecycleDelayedTime = fDelayedTime; // 系统收取四门金币延时时间

                let fPlayerCoinRecycleDelayedTime1 = fDelayedTime; // 闲家收取四门金币延时时间
                let fPlayerCoinRecycleDelayedTime2 = fDelayedTime; // 闲家收取系统金币延时时间

                let fMaxDelayedTime1 = 0; // 情况1 最大延时时间
                let fMaxDelayedTime2 = 0; // 情况2 最大延时时间

                let bBetInNormalZone = false; // 四门闲区是否下过注
                let bBetInLuckZone = false; // 幸运一击区域是否下过注
                let mapDealerWinAreaOption: Map<network.BetZoneOption, number> = new Map(); // 庄家赢的区域(四门)
                let mapSystemWinAreaOption: Map<network.BetZoneOption, number> = new Map(); // 系统赢的区域(幸运一击)

                this._humanboyRoom.betZones.forEach((zoneData: domain.BetZone, option: network.BetZoneOption): void => {
                    if (option < network.BetZoneOption.POS_LUCK) {
                        if (zoneData.optionInfo.totalBet > 0) bBetInNormalZone = true;
                        if (zoneData.optionResult.result > 0 && zoneData.optionInfo.totalBet > 0) {
                            mapDealerWinAreaOption.set(option, zoneData.optionInfo.totalBet);
                        }
                    } else {
                        if (zoneData.optionInfo.totalBet > 0) bBetInLuckZone = true;
                        if (zoneData.optionResult.result > 0 && zoneData.optionInfo.totalBet > 0) {
                            mapSystemWinAreaOption.set(option, zoneData.optionInfo.totalBet);
                        }
                    }
                });

                // 庄家有赢, 则回收四门区域的金币
                if (mapDealerWinAreaOption.size > 0) {
                    fDealerCoinRecycleDelayedTime += this._fActDelayed_FlyWinCoin;

                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDealerCoinRecycleDelayedTime),
                            cc.callFunc((): void => {
                                this._playSoundEffect(macros.Audio.Get_win_coin);
                                this._showDealerWinFlagsAndFlyCoinsAnim(mapDealerWinAreaOption);
                            }, this)
                        )
                    );

                    // 检测庄家飘分
                    if (!bShowDealerScore) {
                        bShowDealerScore = true;

                        // 庄家飘分
                        let fTmpTime = fDealerCoinRecycleDelayedTime + this._fActDelayed_FlyWinCoin;
                        this._showDealerAddCoinAnim(fTmpTime);

                        // 更新庄家列表战绩
                        fTmpTime += this._fActDelayed_FlyWinCoin;
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(fTmpTime),
                                cc.callFunc((): void => {
                                    this._humanboyRoom.updateDealerList();
                                }, this)
                            )
                        );
                    }

                    fDealerCoinRecycleDelayedTime += this._fActExecute_FlyWinCoin;
                }

                // 系统有赢, 则回收幸运一击区域的金币
                if (mapSystemWinAreaOption.size > 0) {
                    fSystemCoinRecycleDelayedTime += this._fActDelayed_FlyWinCoin;
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fSystemCoinRecycleDelayedTime),
                            cc.callFunc((): void => {
                                this._humanboyRoom.betZones.forEach(
                                    (zoneData: domain.BetZone, option: network.BetZoneOption): string => {
                                        if (option <= network.BetZoneOption.POS_LUCK) return;
                                        if (zoneData.optionResult.result > 0 && zoneData.optionInfo.totalBet > 0) {
                                            this._hideBetAreaCoinsAnim(option, this._fActExecute_FlyWinCoin);
                                        }
                                    }
                                );
                            }, this)
                        )
                    );
                    fSystemCoinRecycleDelayedTime += this._fActExecute_FlyWinCoin;
                }

                fMaxDelayedTime1 = Math.max(fDealerCoinRecycleDelayedTime, fSystemCoinRecycleDelayedTime);

                // 闲家收取四门赢金币
                do {
                    let bWinRecover = false; // 四门区域有钱回收
                    let bLuckWinRecover = false; // 幸运一击区域有钱回收

                    // let vSettles: humanboy_proto.PlayerSettle[] = [];
                    // pf.StringUtil.deepCopy(humanboyDataMgr.getHumanboyRoom().vPlayerSettles, vSettles);
                    // vSettles.push(humanboyDataMgr.getHumanboyRoom().tOtherPlayerSettle);
                    // const vSettles = this._humanboyRoom.roundInfo.playerSettles;
                    // for (let i = 0; i < vSettles.size; ++i) {
                    this._humanboyRoom.roundInfo.getAllPlayerSettles().forEach((playerSettle) => {
                        // let playerSettle = vSettles[i];
                        if (playerSettle.pos4WinAmount > 0) {
                            bWinRecover = true;
                        }

                        if (playerSettle.posLuckWinAmount > 0) {
                            bLuckWinRecover = true;
                        }

                        if (bWinRecover && bLuckWinRecover) return;
                    });

                    // 四门有击中, 则从庄家吐金币到四门对应区域
                    if (bWinRecover) {
                        fPlayerCoinRecycleDelayedTime1 += this._fActDelayed_FlyWinCoin;

                        // 庄家吐金币
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(fPlayerCoinRecycleDelayedTime1),
                                cc.callFunc((): void => {
                                    this._showCoinToNormalAreaFromDealer();
                                }, this)
                            )
                        );

                        // 检测庄家飘分
                        if (!bShowDealerScore) {
                            bShowDealerScore = true;
                            let fTmpTime = fPlayerCoinRecycleDelayedTime1 + this._fActDelayed_FlyWinCoin;

                            // 庄家飘分
                            this._showDealerAddCoinAnim(fTmpTime);

                            // 更新庄家列表战绩
                            fTmpTime += this._fActDelayed_FlyWinCoin;
                            this._nodeAnim.runAction(
                                cc.sequence(
                                    cc.delayTime(fTmpTime),
                                    cc.callFunc((): void => {
                                        this._humanboyRoom.updateDealerList();
                                    }, this)
                                )
                            );
                        }

                        fPlayerCoinRecycleDelayedTime1 += this._fActExecute_FlyWinCoin;
                    }

                    // 幸运一击有击中, 则从系统吐金币到幸运一击对应区域
                    if (bLuckWinRecover) {
                        fPlayerCoinRecycleDelayedTime2 += this._fActDelayed_FlyWinCoin;
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(fPlayerCoinRecycleDelayedTime2),
                                cc.callFunc((): void => {
                                    this._showCoinToLuckBlowAreaFromSystem();
                                }, this)
                            )
                        );
                        fPlayerCoinRecycleDelayedTime2 += this._fActExecute_FlyWinCoin;
                    }

                    fMaxDelayedTime2 = Math.max(fPlayerCoinRecycleDelayedTime1, fPlayerCoinRecycleDelayedTime2);

                    // 从对应区域飞金币给玩家
                    if (bWinRecover || bLuckWinRecover) {
                        // fMaxDelayedTime2 += this._fActDelayed_FlyWinCoin;
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(fMaxDelayedTime2),
                                cc.callFunc((): void => {
                                    this._hideAllWinFlagAnim();
                                    this._playSoundEffect(macros.Audio.Get_win_coin);
                                    this._showAllAreaWinFlagsAndFlyCoinAnim();
                                }, this)
                            )
                        );
                        fMaxDelayedTime2 += this._fActExecute_FlyWinCoin;
                    }

                    // 更新临时金币显示
                    do {
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(fMaxDelayedTime2),
                                cc.callFunc((): void => {
                                    // 更新玩家临时金币显示
                                    this._updateAllPlayerTempGold();

                                    // 更新所有玩家连胜状态
                                    this._updateAllPlayerWinCount(true);

                                    // 维护状态:非0代表系统即将维护

                                    if (this._humanboyRoom.roundInfo.stopWorld !== 0) {
                                        let bTrue = this._humanboyRoom.roundInfo.idleRoomId > 0;
                                        if (!bTrue) {
                                            this._showGameToast(
                                                pf.languageManager.getString('Humanboy_server_will_stop_text')
                                            );
                                        }
                                        this._nodeAnim.runAction(
                                            cc.sequence(
                                                cc.delayTime(2.0),
                                                cc.callFunc((): void => {
                                                    if (bTrue) {
                                                        this.showSwitchTable();
                                                    } else {
                                                        this._backToRoomListScene();
                                                    }
                                                }, this)
                                            )
                                        );
                                    }
                                }, this)
                            )
                        );
                    } while (0);
                } while (0);

                // 计算最大延时时间
                fDelayedTime = Math.max(fMaxDelayedTime1, fMaxDelayedTime2);
            }

            // jackpot检测
            case 5: {
                // 更新 Jackpot
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._updateJackPotNum();
                        })
                    )
                );

                // Jackpot 动画
                if (this._isHitJackpotAward()) {
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime),
                            cc.callFunc((): void => {
                                this._showJackPotAnim();
                            }, this)
                        )
                    );
                    fDelayedTime += this._getJackpotAwardExecuteTime();
                }
            }

            // 所有流程完毕, 统一更新至服务器最新数据
            case 6: {
                fDelayedTime += 0.5;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            // 更新自己信息
                            this._updateSelfInfo();

                            // 更新其他人信息
                            this._updateOtherPlayersInfo();
                        }, this)
                    )
                );
            }

            default:
                break;
        }
    }

    /**
     * 显示 win 动画
     * @param betOption
     * @param pauseEnd
     */
    private _showWinFlagAnim(betOption: network.BetZoneOption, pauseEnd: boolean = false): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        this._vNodeWinFlagAnims[nAreaIdx].active = true;

        // 沿用旧的win动画
        let action: cc.Animation = this._vAtlWinFlagActions[nAreaIdx];
        let animation0: cc.AnimationState = action.getAnimationState('animation0');
        let animation1: cc.AnimationState = action.getAnimationState('animation1');

        animation0.speed = this._getAnimClipSpeedByDuring(animation0.clip, this._fActExecute_WinFlag);
        animation0.wrapMode = cc.WrapMode.Normal;

        action.play(animation0.name);
        action.on(
            cc.Animation.EventType.FINISHED,
            (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                action.off(cc.Animation.EventType.FINISHED);
                animation1.wrapMode = cc.WrapMode.Loop;
                animation1.play();
            },
            this
        );
    }

    /**
     * 隐藏win动画
     * @param betOption
     */
    private _hideWinFlagAnim(betOption: network.BetZoneOption): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let vClips: cc.AnimationClip[] = this._vAtlWinFlagActions[nAreaIdx].getClips();
        for (const clip of vClips) {
            this._vAtlWinFlagActions[nAreaIdx].stop(clip.name);
        }
        this._vNodeWinFlagAnims[nAreaIdx].active = false;
    }

    /**
     * 显示所有区域win标记
     */
    private _showAllWinFlagAnim(): void {
        this._humanboyRoom.betZones.forEach((zoneData: domain.BetZone, option: network.BetZoneOption) => {
            if (zoneData.optionResult.result >= 0) return;
            this._showWinFlagAnim(option);
        });
    }

    /**
     * 隐藏所有区域win标记
     */
    private _hideAllWinFlagAnim(): void {
        this._humanboyRoom.betZones.forEach((zoneData: domain.BetZone, option: network.BetZoneOption) => {
            if (zoneData.optionResult.result >= 0) return;
            this._hideWinFlagAnim(option);
        });
    }

    /**
     * 路单滚动动画
     * @param betOption
     */
    private _showWayOutMoveAnim(betOption: network.BetZoneOption): void {
        let nAreaIdx = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let panelWayOut: cc.Node = this._vAreasInfo[nAreaIdx].panelWayOut;
        let vWayOutImg: cc.Sprite[] = this._vAreasInfo[nAreaIdx].vWayOutImg;
        let vWayOutImgSrcPos: cc.Vec2[] = this._vAreasInfo[nAreaIdx].vWayOutImgSrcPos;
        if (!panelWayOut || vWayOutImg.length <= 0) return;

        // 裁剪右移 模式
        // let tarPos: cc.Vec2 = cc.v2(cc.Vec2.ZERO);
        // for (let i = 0; i < vWayOutImg.length; ++i) {
        //     if (i === 0) {
        //         tarPos.x = vWayOutImgSrcPos[i].x - vWayOutImg[nAreaIdx].node.width * vWayOutImg[nAreaIdx].node.scaleX;
        //         tarPos.y = vWayOutImgSrcPos[i].y;
        //     }
        //     else {
        //         tarPos.x = vWayOutImgSrcPos[i - 1].x;
        //         tarPos.y = vWayOutImgSrcPos[i - 1].y;
        //     }

        //     vWayOutImg[i].node.runAction(cc.sequence(cc.moveTo(0.3, tarPos), cc.callFunc((): void => {
        //         if (i === vWayOutImg.length - 1) {
        //             this._updateWayOut(betOption, 0);
        //         }
        //     }, this)));
        // }

        // 缩小渐隐右移 模式
        let st: cc.ActionInterval = cc.scaleTo(0.2, 0);
        let fo: cc.ActionInterval = cc.fadeOut(0.3);
        let spawn: cc.FiniteTimeAction = cc.spawn(st, fo);
        vWayOutImg[0].node.runAction(
            cc.sequence(
                spawn,
                cc.callFunc((): void => {
                    vWayOutImg[0].node.active = false;
                    let tarPos: cc.Vec2 = cc.v2(cc.Vec2.ZERO);
                    for (let i = 0; i < vWayOutImg.length; ++i) {
                        if (i === 0) continue;

                        tarPos.x = vWayOutImgSrcPos[i - 1].x;
                        tarPos.y = vWayOutImgSrcPos[i - 1].y;
                        vWayOutImg[i].node.runAction(
                            cc.sequence(
                                cc.moveTo(0.5, tarPos),
                                cc.callFunc((): void => {
                                    if (i === vWayOutImg.length - 1) {
                                        this._updateWayOut(betOption, 0);
                                        vWayOutImg[0].node.setScale(1.0);
                                        vWayOutImg[0].node.opacity = 0xff;
                                        vWayOutImg[0].node.active = true;
                                    }
                                }, this)
                            )
                        );
                    }
                })
            )
        );
    }

    /**
     * 显示路单图片动画
     * @param betOption
     */
    private _showWayOutImgAnim(betOption: network.BetZoneOption): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let vWayOutImg: cc.Sprite[] = this._vAreasInfo[nAreaIdx].vWayOutImg;
        if (vWayOutImg.length <= 0) return;

        let zoneData: domain.BetZone = this._humanboyRoom.betZones.get(betOption);
        if (!zoneData) return;

        // 隐藏路单文本
        let rtxtWayOut: cc.RichText = this._vAreasInfo[nAreaIdx].rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 与庄家比大小的结果 = 0 相同, > 0 庄家赢, < 0 闲家赢
        let result: number = zoneData.optionResult.result;

        let fileName_fire = '';
        let fileName_ball = '';

        // 输
        if (result > 0) {
            fileName_fire = 'humanboy_icon_fire_white';
            fileName_ball =
                betOption < network.BetZoneOption.POS_LUCK
                    ? 'humanboy_icon_circle_gray'
                    : 'humanboy_icon_circle_small_gray';
        }
        // 平
        else if (result === 0) {
            fileName_fire = 'humanboy_icon_fire_green';
            fileName_ball = 'humanboy_icon_circle_green';
        }
        // 赢
        else {
            fileName_fire = 'humanboy_icon_fire_red';
            fileName_ball =
                betOption < network.BetZoneOption.POS_LUCK
                    ? 'humanboy_icon_circle_red'
                    : 'humanboy_icon_circle_small_red';
        }

        // 计算空闲路子索引
        let freeIndex: number = vWayOutImg.length;
        for (let i = 0; i < vWayOutImg.length; ++i) {
            if (!vWayOutImg[i].node.active) {
                freeIndex = i;
                break;
            }
        }

        // 四门路子飞的动画
        if (betOption < network.BetZoneOption.POS_LUCK) {
            let img_fire: cc.Sprite = new cc.Node().addComponent(cc.Sprite);
            this._nodeAnim.addChild(img_fire.node, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_0);
            img_fire.node.active = true;
            img_fire.spriteFrame = this._atlas_hb_humanboy.getSpriteFrame(fileName_fire);

            // 计算旋转角度
            let start_pos: cc.Vec2 = cc.Vec2.ZERO;
            this._vAreasInfo[nAreaIdx].panelCoin.convertToWorldSpaceAR(cc.Vec2.ZERO, start_pos);
            let end_pos_index: number = Math.min(freeIndex, vWayOutImg.length - 1);
            let end_pos: cc.Vec2 = cc.Vec2.ZERO;
            vWayOutImg[end_pos_index].node.convertToWorldSpaceAR(cc.Vec2.ZERO, end_pos);

            let v1: cc.Vec2 = cc.v2(start_pos.x - start_pos.x, 0 - start_pos.y);
            let v2: cc.Vec2 = cc.v2(end_pos.x - start_pos.x, end_pos.y - start_pos.y);
            let rotation: number = (v2.signAngle(v1) / Math.PI) * 180;
            img_fire.node.angle = -rotation;

            // action
            img_fire.node.parent.convertToNodeSpaceAR(start_pos, start_pos);
            img_fire.node.parent.convertToNodeSpaceAR(end_pos, end_pos);
            img_fire.node.setPosition(start_pos);
            img_fire.node.runAction(
                cc.sequence(
                    cc.delayTime(0.3 * this._fActExecute_WayOut),
                    cc.moveTo(0.7 * this._fActExecute_WayOut, end_pos).easing(cc.easeSineInOut()),
                    cc.callFunc((): void => {
                        img_fire.node.removeFromParent(true);
                        pf.UIUtil.destroyNode(img_fire.node);
                    }, this)
                )
            );
        }

        // 路子满了挤动动画
        if (freeIndex > vWayOutImg.length - 1) {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.3 * this._fActExecute_WayOut),
                    cc.callFunc((): void => {
                        this._showWayOutMoveAnim(betOption);
                    }, this)
                )
            );
        } else {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.8 * this._fActExecute_WayOut),
                    cc.callFunc((): void => {
                        vWayOutImg[freeIndex].node.active = true;
                        vWayOutImg[freeIndex].spriteFrame = this._atlas_hb_language.getSpriteFrame(fileName_ball);
                    }, this)
                )
            );
        }
    }

    /**
     * 路单动画(包括图片,文本等)
     * @param betOption
     */
    private _showWayOutAnim(betOption: network.BetZoneOption): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let panelWayOut: cc.Node = this._vAreasInfo[nAreaIdx].panelWayOut;
        let vWayOutImg: cc.Sprite[] = this._vAreasInfo[nAreaIdx].vWayOutImg;
        if (!panelWayOut || vWayOutImg.length <= 0) return;

        let zoneData: domain.BetZone = this._humanboyRoom.betZones.get(betOption);
        if (!zoneData) return;

        // 路子显示风格
        switch (this._vAreasInfo[nAreaIdx].eWayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(betOption, 1);
                this._showWayOutImgAnim(betOption);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(betOption);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    let bShowTxt = false;
                    let vHistoryResults: number[] = zoneData.optionResult.historyResults;
                    if (vHistoryResults.length > 0 && vHistoryResults.length > vWayOutImg.length) {
                        let bDefeat = true;
                        for (let i = 0; i <= vWayOutImg.length; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] > 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(betOption);
                    } else {
                        this._updateWayOutImg(betOption, 1);
                        this._showWayOutImgAnim(betOption);
                    }
                }
                break;

            default:
                break;
        }
    }

    /**
     * 更新路单图片
     * @param betOption
     * @param reduce
     */
    private _updateWayOutImg(betOption: network.BetZoneOption, reduce: number): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let panelWayOut: cc.Node = this._vAreasInfo[nAreaIdx].panelWayOut;
        panelWayOut.active = true;

        let vWayOutImg: cc.Sprite[] = this._vAreasInfo[nAreaIdx].vWayOutImg;
        let vWayOutImgSrcPos: cc.Vec2[] = this._vAreasInfo[nAreaIdx].vWayOutImgSrcPos;

        let zoneData = this._humanboyRoom.betZones.get(betOption);
        if (!zoneData) return;

        // 隐藏路单文本
        let rtxtWayOut: cc.RichText = this._vAreasInfo[nAreaIdx].rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 逆序取历史记录
        let fileName_ball = '';
        let vHistoryResults: number[] = zoneData.optionResult.historyResults;

        let min_count: number = Math.min(vWayOutImg.length, vHistoryResults.length);
        let end_index = 0;
        let end_count = 0;

        // ui显示个数 >= 路子数据个数, 少显示 reduce 个
        if (vWayOutImg.length >= vHistoryResults.length) {
            end_index = min_count - 1;
            end_count = min_count - reduce;
        }
        // ui显示个数 < 路子数据个数, 偏移 reduce 位数据显示
        else {
            end_index = min_count - 1 + reduce;
            end_count = min_count;
        }

        for (let i = 0; i < vWayOutImg.length; ++i) {
            // 复原位置
            vWayOutImg[i].node.setPosition(vWayOutImgSrcPos[i]);

            let index = end_index - i;
            if (i < end_count && index >= 0 && index < vHistoryResults.length) {
                // 与庄家比大小的结果 = 0 相同, > 0 庄家赢, < 0 闲家赢
                let result: number = vHistoryResults[index];
                if (result > 0) {
                    fileName_ball =
                        betOption < network.BetZoneOption.POS_LUCK
                            ? 'humanboy_icon_circle_gray'
                            : 'humanboy_icon_circle_small_gray';
                } else if (result === 0) {
                    fileName_ball = 'humanboy_icon_circle_green';
                } else {
                    fileName_ball =
                        betOption < network.BetZoneOption.POS_LUCK
                            ? 'humanboy_icon_circle_red'
                            : 'humanboy_icon_circle_small_red';
                }

                vWayOutImg[i].node.active = true;
                vWayOutImg[i].spriteFrame = this._atlas_hb_language.getSpriteFrame(fileName_ball);
            } else {
                vWayOutImg[i].node.active = false;
            }
        }
    }

    /**
     * 显示路单文本
     * @param betOption
     */
    private _updateWayOutTxt(betOption: network.BetZoneOption): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let rtxtWayOut: cc.RichText = this._vAreasInfo[nAreaIdx].rtxtWayOut;
        if (!rtxtWayOut) return;

        let vWayOutImg: cc.Sprite[] = this._vAreasInfo[nAreaIdx].vWayOutImg;
        let iWayOutLoseLimitCount: number = this._vAreasInfo[nAreaIdx].iWayOutLoseLimitCount;

        // 隐藏路单球图片面板
        for (const sprite of vWayOutImg) {
            sprite.node.active = false;
        }

        // 显示文本
        let eCurState: network.RoundState = this._humanboyRoom.gameState.roundState;
        let zoneData: domain.BetZone = this._humanboyRoom.betZones.get(betOption);
        if (!zoneData) return;

        // 连续多少手未出现(< 0 房间刚刚开始,不需要统计; > 0 多少手; = 0 上一手出现过)
        let luckLoseHand: number = zoneData.optionResult.luckLoseHand;
        if (luckLoseHand < 0) {
            rtxtWayOut.string = '';
        } else if (luckLoseHand === 0) {
            if (eCurState === network.RoundState.WAIT_NEXT_ROUND) {
                pf.StringUtil.setRichTextString(
                    rtxtWayOut.node,
                    pf.languageManager.getString('Humanboy_game_wayout_hit_txt')
                );
            } else {
                pf.StringUtil.setRichTextString(
                    rtxtWayOut.node,
                    pf.languageManager.getString('Humanboy_game_wayout_hit_last_txt')
                );
            }
        } else {
            let strCountDest = '';
            if (iWayOutLoseLimitCount !== 0 && luckLoseHand > iWayOutLoseLimitCount) {
                strCountDest = pf.StringUtil.formatC('%d+', iWayOutLoseLimitCount);
            } else {
                strCountDest = pf.StringUtil.formatC('%d', luckLoseHand);
            }
            pf.StringUtil.setRichTextString(
                rtxtWayOut.node,
                pf.StringUtil.formatC(pf.languageManager.getString('Humanboy_game_wayout_lose_txt'), strCountDest)
            );
        }
        rtxtWayOut.node.active = true;
    }

    /**
     *  更新路单
     * @param betOption
     * @param reduce
     */
    private _updateWayOut(betOption: network.BetZoneOption, reduce: number): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let vWayOutImg: cc.Sprite[] = this._vAreasInfo[nAreaIdx].vWayOutImg;
        let zoneData: domain.BetZone = this._humanboyRoom.betZones.get(betOption);
        if (!zoneData) return;

        switch (this._vAreasInfo[nAreaIdx].eWayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(betOption, reduce);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(betOption);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    let bShowTxt = false;
                    let vHistoryResults: number[] = zoneData.optionResult.historyResults;
                    if (vHistoryResults.length > 0 && vHistoryResults.length > vWayOutImg.length) {
                        let bDefeat = true;
                        for (let i = 0; i <= vWayOutImg.length; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] > 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(betOption);
                    } else {
                        this._updateWayOutImg(betOption, reduce);
                    }
                }
                break;

            default:
                break;
        }
    }

    /**
     * 更新所有路单
     * @param reduce
     */
    private _updateAllWayOut(reduce: number = 0): void {
        for (const area of this._vAreasInfo) {
            this._updateWayOut(area.eZone, reduce);
        }
    }

    /**
     * 显示指定区域路单面板
     * @param option
     */
    private _showChart(option: network.BetZoneOption): void {
        if (!this._humanboyChart) {
            this._humanboyChart = cc.instantiate(this.prefab_hb_chart).getComponent(HumanboyChartControl);
            this.node.addChild(this._humanboyChart.node, eHumanboyLocalZorder.HL_ZORDER_PANEL_RECORD);
        }
        this._humanboyChart.showView(option);
    }

    /**
     * 显示所有路单动画
     */
    private _showAllWayOutAnim(): void {
        for (const area of this._vAreasInfo) {
            this._showWayOutAnim(area.eZone);
        }

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._fActExecute_WayOut),
                cc.callFunc((): void => {
                    this._showWayOutLightAnim();
                }, this)
            )
        );
    }

    /**
     * 显示路单闪光动画
     */
    private _showWayOutLightAnim(): void {
        let pos: cc.Vec2 = cc.Vec2.ZERO;
        this._btn_record.node.parent.convertToWorldSpaceAR(this._btn_record.node.getPosition(), pos);
        this._nodeWayoutLightAnim.active = true;
        this._nodeWayoutLightAnim.setPosition(this._nodeWayoutLightAnim.parent.convertToNodeSpaceAR(pos));

        let way_out: cc.AnimationClip = this._atlWayoutLightAction.defaultClip;
        way_out.speed = this._getAnimClipSpeedByDuring(way_out, this._fActExecute_WayOutLight);
        way_out.wrapMode = cc.WrapMode.Normal;

        this._atlWayoutLightAction.play();
        this._atlWayoutLightAction.on(
            cc.Animation.EventType.FINISHED,
            (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                this._atlWayoutLightAction.off(cc.Animation.EventType.FINISHED);
                this._nodeWayoutLightAnim.active = false;
            },
            this
        );
    }

    /**
     * 隐藏赢区域金币
     * @param bExceptLuckBlow 是否排除幸运一击区域
     * @param bIgnoreEqual 是否忽略平局区域
     */
    private _hideWinAreaCoinsAnim(bExceptLuckBlow: boolean, bIgnoreEqual: boolean): void {
        for (const area of this._vAreasInfo) {
            if (bExceptLuckBlow && area.eZone >= network.BetZoneOption.POS_LUCK) continue;

            let zoneData: domain.BetZone = this._humanboyRoom.betZones.get(area.eZone);
            if (!zoneData) return;

            let result: number = zoneData.optionResult.result;
            let bFind: boolean = bIgnoreEqual ? result <= 0 : result < 0;
            if (bFind) {
                for (let j = 0; j < area.vCoinQueue.size(); ++j) {
                    let coin: HumanboyBetCoinControl = area.vCoinQueue.at(j);
                    if (!coin.node.active) continue;
                    let sequence: cc.ActionInterval = cc.sequence(
                        cc.fadeOut(this._fActDelayed_FlyWinCoin),
                        cc.callFunc((): void => {
                            coin.node.active = false;
                        })
                    );
                    coin.node.runAction(sequence);
                }
            }
        }
    }

    /**
     * 隐藏输区域金币
     * @param bExceptLuckBlow 是否排除幸运一击区域
     * @param bIgnoreEqual 是否忽略平局区域
     */
    private _hideLoseAreaCoinsAnim(bExceptLuckBlow: boolean, bIgnoreEqual: boolean): void {
        for (const area of this._vAreasInfo) {
            if (bExceptLuckBlow && area.eZone >= network.BetZoneOption.POS_LUCK) continue;

            let zoneData: domain.BetZone = this._humanboyRoom.betZones.get(area.eZone);
            if (!zoneData) return;

            let result: number = zoneData.optionResult.result;
            let bFind: boolean = bIgnoreEqual ? result >= 0 : result > 0;
            if (bFind) {
                for (let j = 0; j < area.vCoinQueue.size(); ++j) {
                    let coin: HumanboyBetCoinControl = area.vCoinQueue.at(j);
                    if (!coin.node.active) continue;
                    let sequence: cc.ActionInterval = cc.sequence(
                        cc.fadeOut(this._fActDelayed_FlyWinCoin),
                        cc.callFunc((): void => {
                            coin.node.active = false;
                        })
                    );
                    coin.node.runAction(sequence);
                }
            }
        }
    }

    /**
     * 隐藏指定下注区金币动画
     */
    private _hideBetAreaCoinsAnim(betOption: network.BetZoneOption, fDuringTime: number): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        for (let i = 0; i < this._vAreasInfo[nAreaIdx].vCoinQueue.size(); ++i) {
            let coin: HumanboyBetCoinControl = this._vAreasInfo[nAreaIdx].vCoinQueue.at(i);
            let sequence: cc.ActionInterval = cc.sequence(
                cc.fadeOut(fDuringTime),
                cc.callFunc((): void => {
                    coin.node.active = false;
                })
            );
            coin.node.runAction(sequence);
        }
    }

    /**
     * 隐藏所有下注区域的金币动画
     * @param anim
     * @param fDuringTime
     */
    private _hideAllBetCoinsAnim(anim: boolean, fDuringTime: number): void {
        for (const area of this._vAreasInfo) {
            if (anim) {
                this._hideBetAreaCoinsAnim(area.eZone, fDuringTime);
            } else {
                for (let j = 0; j < area.vCoinQueue.size(); ++j) {
                    let coin: HumanboyBetCoinControl = area.vCoinQueue.at(j);
                    coin.node.active = false;
                }
            }
        }
    }

    /**
     * 庄家完胜或完败动画
     */
    private _showDealerVictoryOrDefeatAnim(): void {
        // 1 代表庄家通吃  2 代表庄家通赔
        let uDealerWinAll: number = this._humanboyRoom.roundInfo.dealerWinAll;
        switch (uDealerWinAll) {
            case 1:
                {
                    // 暂时去掉通吃通赔音效
                    // this._playSoundEffect(this._sound_dealer_vd);

                    this._nodeDealerVictoryAnim.active = true;

                    let dealer_victory: cc.AnimationClip = this._atlDealerVictoryAction.defaultClip;
                    dealer_victory.speed = this._getAnimClipSpeedByDuring(
                        dealer_victory,
                        this._fActExecute_VictoryOrDefeat
                    );
                    dealer_victory.wrapMode = cc.WrapMode.Normal;

                    this._atlDealerVictoryAction.play();
                    this._atlDealerVictoryAction.on(
                        cc.Animation.EventType.FINISHED,
                        (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                            this._atlDealerVictoryAction.off(cc.Animation.EventType.FINISHED);
                            this._nodeDealerVictoryAnim.active = false;
                        },
                        this
                    );
                }
                break;

            case 2:
                {
                    // 暂时去掉通吃通赔音效
                    // this._playSoundEffect(this._sound_dealer_vd);

                    this._nodeDealerDefeatAnim.active = true;

                    let dealer_defeat: cc.AnimationClip = this._atlDealerDefeatAction.defaultClip;
                    dealer_defeat.speed = this._getAnimClipSpeedByDuring(
                        dealer_defeat,
                        this._fActExecute_VictoryOrDefeat
                    );
                    dealer_defeat.wrapMode = cc.WrapMode.Normal;

                    this._atlDealerDefeatAction.play();
                    this._atlDealerDefeatAction.on(
                        cc.Animation.EventType.FINISHED,
                        (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                            this._atlDealerDefeatAction.off(cc.Animation.EventType.FINISHED);
                            this._nodeDealerDefeatAnim.active = false;
                        },
                        this
                    );
                }
                break;

            default:
                break;
        }
    }

    /**
     * 是否击中幸运一击
     */
    private _isHitLuckBlow(): boolean {
        let vRet = false;
        this._humanboyRoom.betZones.forEach((zoneData: domain.BetZone, option: network.BetZoneOption) => {
            if (option > network.BetZoneOption.POS_LUCK && zoneData.optionResult.result < 0) {
                vRet = true;
                return;
            }
        });
        return vRet;
    }

    /**
     * 是否击中特殊牌型
     */
    private _isHitSpecialCardType(): boolean {
        let vRet = false;
        this._humanboyRoom.betZones.forEach((zoneData: domain.BetZone, option: network.BetZoneOption) => {
            if (option > network.BetZoneOption.POS_LUCK_1 && zoneData.optionResult.result < 0) {
                vRet = true;
                return;
            }
        });
        return vRet;
    }

    /**
     * 获取击中特殊牌型的区域
     */
    private _getHitSpecialCardTypeZone(): network.BetZoneOption {
        let vRet: network.BetZoneOption = network.BetZoneOption.BetZoneOption_DUMMY;

        this._humanboyRoom.betZones.forEach((zoneData: domain.BetZone, option: network.BetZoneOption) => {
            if (vRet > network.BetZoneOption.BetZoneOption_DUMMY) return;
            if (option > network.BetZoneOption.POS_LUCK_1 && zoneData.optionResult.result < 0) {
                vRet = option;
                return;
            }
        });
        return vRet;
    }

    /**
     * 获取击中特殊牌型动画执行时间
     */
    private _getSpecialCardTypeExecuteTime(): number {
        let fRet = 0;

        let eMaxLevel: network.CardResult = this._humanboyRoom.roundInfo.maxLevel;

        switch (eMaxLevel) {
            case network.CardResult.YI_DUI: // 一对
            case network.CardResult.LIAN_DUI: // 两对
            case network.CardResult.SAN_TIAO:
                // 三条
                fRet = this._fActExecute_LuckBlow_1;
                break;

            case network.CardResult.SHUN_ZI: // 顺子
            case network.CardResult.TONG_HUA:
                // 同花
                fRet = this._fActExecute_LuckBlow_2;
                break;

            case network.CardResult.HU_LU: // 葫芦
            case network.CardResult.SI_TIAO: // 金刚
            case network.CardResult.TONG_HUA_SHUN: // 同花顺
            case network.CardResult.HUANG_TONG:
                // 皇同
                fRet = this._fActExecute_LuckBlow_3;
                break;

            default:
                break;
        }

        return fRet;
    }

    /**
     * 显示特殊牌型动画动画
     * @param anim
     * @param delayed
     */
    private _showSpecialCardTypeAnim(anim: boolean = true, delayed: number = 0): void {
        this._clearSpecialCardTypeAnim();
        if (!this._isHitSpecialCardType()) return;

        let fileName = '';
        let specialCardType = '';
        let specialCardOdds = '';
        let soundEffect = '';
        let fActExecute_SpecialCardType = 0;

        let eMaxLevel: network.CardResult = this._humanboyRoom.roundInfo.maxLevel;
        let oddsDetail = this._getLuckBlowOdds(eMaxLevel);

        switch (eMaxLevel) {
            // // 一对
            // case humanboy.CardResult.YI_DUI: {
            // } break;

            // // 两对
            // case humanboy.CardResult.LIAN_DUI: {
            //     fileName = "zh_CN/game/humanboy/animation/special_card_type/special_card_type_1";
            //     specialCardType = cv.config.getLanguagePath("game/humanboy/animation/special_card_type/special_two_pairs");

            //     soundEffect = this._sound_special_card_type_small;
            //     fActExecute_SpecialCardType = this._fActExecute_LuckBlow_1;
            // } break;

            // // 三条
            // case humanboy.CardResult.SAN_TIAO: {
            //     fileName = "zh_CN/game/humanboy/animation/special_card_type/special_card_type_1";
            //     specialCardType = cv.config.getLanguagePath("game/humanboy/animation/special_card_type/special_three_of_a_kind");

            //     soundEffect = this._sound_special_card_type_small;
            //     fActExecute_SpecialCardType = this._fActExecute_LuckBlow_1;
            // } break;

            // 顺子
            case network.CardResult.SHUN_ZI:
                {
                    // fileName = macros.Assets.HUMANBOY_SPECIAL_CARD_TYPE_2;
                    // specialCardType = cv.config.getLanguagePath(
                    //     'game/humanboy/animation/special_card_type/special_straight'
                    // );
                    fileName = macros.Assets.HUMANBOY_SPECIAL_CARD_TYPE_2_PREFAB;
                    specialCardType = macros.Assets.HUMANBOY_SPECIAL_STRAIGHT_SPRITE;

                    let str_odds = pf.StringUtil.numberToString(pf.StringUtil.clientGoldByServer(oddsDetail.odds));
                    specialCardOdds = pf.StringUtil.formatC(
                        '%s%s',
                        str_odds,
                        pf.languageManager.getString('Humanboy_game_fnt_anim_odd')
                    );

                    soundEffect = macros.Audio.Special_Card_Type_Middle;
                    fActExecute_SpecialCardType = this._fActExecute_LuckBlow_2;
                }
                break;

            // 同花
            case network.CardResult.TONG_HUA:
                {
                    fileName = macros.Assets.HUMANBOY_SPECIAL_CARD_TYPE_2_PREFAB;
                    specialCardType = macros.Assets.HUMANBOY_SPECIAL_FLUSH_SPRITE;

                    let str_odds = pf.StringUtil.numberToString(pf.StringUtil.clientGoldByServer(oddsDetail.odds));
                    specialCardOdds = pf.StringUtil.formatC(
                        '%s%s',
                        str_odds,
                        pf.languageManager.getString('Humanboy_game_fnt_anim_odd')
                    );

                    soundEffect = macros.Audio.Special_Card_Type_Middle;
                    fActExecute_SpecialCardType = this._fActExecute_LuckBlow_2;
                }
                break;

            // 葫芦
            case network.CardResult.HU_LU:
                {
                    fileName = macros.Assets.HUMANBOY_SPECIAL_CARD_TYPE_3_PREFAB;
                    specialCardType = macros.Assets.HUMANBOY_SPECIAL_GOURD_SPRITE;

                    let str_odds = pf.StringUtil.numberToString(pf.StringUtil.clientGoldByServer(oddsDetail.odds));
                    specialCardOdds = pf.StringUtil.formatC(
                        '%s%s',
                        str_odds,
                        pf.languageManager.getString('Humanboy_game_fnt_anim_odd')
                    );

                    soundEffect = macros.Audio.Special_Card_Type_Big;
                    fActExecute_SpecialCardType = this._fActExecute_LuckBlow_3;
                }
                break;

            // 金刚(四条)
            case network.CardResult.SI_TIAO:
                {
                    fileName = macros.Assets.HUMANBOY_SPECIAL_CARD_TYPE_3_PREFAB;
                    specialCardType = macros.Assets.HUMANBOY_SPECIAL_FOUR_OF_A_KIND_SPRITE;

                    let str_odds = pf.StringUtil.numberToString(pf.StringUtil.clientGoldByServer(oddsDetail.odds));
                    specialCardOdds = pf.StringUtil.formatC(
                        '%s%s',
                        str_odds,
                        pf.languageManager.getString('Humanboy_game_fnt_anim_odd')
                    );

                    soundEffect = macros.Audio.Special_Card_Type_Big;
                    fActExecute_SpecialCardType = this._fActExecute_LuckBlow_3;
                }
                break;

            // 同花顺
            case network.CardResult.TONG_HUA_SHUN:
                {
                    fileName = macros.Assets.HUMANBOY_SPECIAL_CARD_TYPE_3_PREFAB;

                    specialCardType = macros.Assets.HUMANBOY_SPECIAL_STRAIGHT_FLUSH_SPRITE;

                    let str_odds = pf.StringUtil.numberToString(pf.StringUtil.clientGoldByServer(oddsDetail.odds));
                    specialCardOdds = pf.StringUtil.formatC(
                        '%s%s',
                        str_odds,
                        pf.languageManager.getString('Humanboy_game_fnt_anim_odd')
                    );

                    soundEffect = macros.Audio.Special_Card_Type_Big;
                    fActExecute_SpecialCardType = this._fActExecute_LuckBlow_3;
                }
                break;

            // 皇同
            case network.CardResult.HUANG_TONG:
                {
                    fileName = macros.Assets.HUMANBOY_SPECIAL_CARD_TYPE_3_PREFAB;
                    specialCardType = macros.Assets.HUMANBOY_SPECIAL_ROYAL_FLUSH_SPRITE;

                    let str_odds = pf.StringUtil.numberToString(pf.StringUtil.clientGoldByServer(oddsDetail.odds));
                    specialCardOdds = pf.StringUtil.formatC(
                        '%s%s',
                        str_odds,
                        pf.languageManager.getString('Humanboy_game_fnt_anim_odd')
                    );

                    soundEffect = macros.Audio.Special_Card_Type_Big;
                    fActExecute_SpecialCardType = this._fActExecute_LuckBlow_3;
                }
                break;

            default:
                break;
        }

        // 创建时间轴动画
        if (fileName.length > 0) {
            const prefab: cc.Prefab = pf.addressableAssetManager.getAsset(fileName);
            // cv.resMgr.loadPrefab(fileName, (prefab: cc.Prefab): void => {
            let nodeSpecialCardTypeAnim: cc.Node = cc.instantiate(prefab);
            nodeSpecialCardTypeAnim.name = 'special_card_type_anim';
            nodeSpecialCardTypeAnim.setPosition(cc.Vec2.ZERO);
            this._nodeAnim.addChild(nodeSpecialCardTypeAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_1);
            nodeSpecialCardTypeAnim.active = true;

            // 设置赔率
            if (specialCardOdds.length > 0) {
                let fnt_card_odd: cc.Label = nodeSpecialCardTypeAnim
                    .getChildByName('fnt_card_odd')
                    .getComponent(cc.Label);
                fnt_card_odd.string = specialCardOdds;
            }

            // 设置牌型
            if (specialCardType.length > 0) {
                let img_card_type: cc.Sprite = nodeSpecialCardTypeAnim
                    .getChildByName('img_card_type')
                    .getComponent(cc.Sprite);
                img_card_type.sizeMode = cc.Sprite.SizeMode.RAW;
                img_card_type.spriteFrame = pf.addressableAssetManager.getAsset(specialCardType);
                // cv.resMgr.setSpriteFrame(img_card_type.node, specialCardType);
            }

            let ctlSpecialCardTypeAction: cc.Animation = nodeSpecialCardTypeAnim.getComponent(cc.Animation);
            if (anim) {
                this._playSoundEffect(soundEffect);

                ctlSpecialCardTypeAction.defaultClip.speed = this._getAnimClipSpeedByDuring(
                    ctlSpecialCardTypeAction.defaultClip,
                    fActExecute_SpecialCardType
                );
                ctlSpecialCardTypeAction.defaultClip.wrapMode = cc.WrapMode.Normal;
                ctlSpecialCardTypeAction.play();

                ctlSpecialCardTypeAction.on(
                    cc.Animation.EventType.FINISHED,
                    (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                        ctlSpecialCardTypeAction.off(cc.Animation.EventType.FINISHED);
                        nodeSpecialCardTypeAnim.removeFromParent(true);
                        pf.UIUtil.destroyNode(nodeSpecialCardTypeAnim);
                        this._clearSpecialCardTypeHighLightAnim();
                    },
                    this
                );
            } else {
                ctlSpecialCardTypeAction.play();
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(delayed),
                        cc.callFunc((): void => {
                            nodeSpecialCardTypeAnim.removeFromParent(true);
                            pf.UIUtil.destroyNode(nodeSpecialCardTypeAnim);
                            this._clearSpecialCardTypeHighLightAnim();
                        }, this)
                    )
                );
            }
            // });
        }
    }

    /**
     * 清除特殊牌型动画
     * @param cleanup
     */
    private _clearSpecialCardTypeAnim(cleanup: boolean = true): void {
        let node: cc.Node = cc.find('special_card_type_anim', this._nodeAnim);
        if (node && cc.isValid(node, true)) {
            node.removeFromParent(cleanup);
            if (cleanup) node.destroy();
        }
    }
    /**
     * 显示特殊牌型高亮动画
     */
    private _showSpecialCardTypeHighLightAnim(): void {
        this._clearSpecialCardTypeHighLightAnim();
        if (!this._isHitSpecialCardType()) return;

        //  特殊牌型来自的区域
        let pos: cc.Vec2 = cc.Vec2.ZERO;
        let fileName = '';
        let eMaxLevelOption: network.BetZoneOption = this._humanboyRoom.roundInfo.maxLevelOption;
        switch (eMaxLevelOption) {
            // 庄
            case network.BetZoneOption.HOST:
                {
                    fileName = macros.Assets.HUMANBOY_LIGHT_BOX_1_PREFAB;

                    let panel_dealer_card: cc.Node = this._panel_dealer.getChildByName('panel_dealer_card');
                    panel_dealer_card.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);
                }
                break;

            // 闲
            case network.BetZoneOption.POS1:
            case network.BetZoneOption.POS2:
            case network.BetZoneOption.POS3:
            case network.BetZoneOption.POS4:
                {
                    let nAreaIdx: number = this._getAreaIdxByBetOption(eMaxLevelOption);
                    if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) break;

                    let panelBorder: cc.Node = this._vAreasInfo[nAreaIdx].panelBorder;
                    panelBorder.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);

                    switch (this._eGameboyScreenType) {
                        case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD:
                            fileName = macros.Assets.HUMANBOY_LIGHT_BOX_4_PREFAB;
                            break;

                        case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW:
                            fileName = macros.Assets.HUMANBOY_LIGHT_BOX_3_PREFAB;
                            break;

                        case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL:
                        default:
                            fileName = macros.Assets.HUMANBOY_LIGHT_BOX_2_PREFAB;
                            break;
                    }
                }
                break;

            default:
                break;
        }

        const prefab: cc.Prefab = pf.addressableAssetManager.getAsset(fileName);
        // cv.resMgr.loadPrefab(fileName, (prefab: cc.Prefab): void => {
        let nodeHighLightAnim: cc.Node = cc.instantiate(prefab);
        nodeHighLightAnim.name = 'special_card_type_high_light_anim';
        this._nodeAnim.addChild(nodeHighLightAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_1);
        nodeHighLightAnim.setPosition(nodeHighLightAnim.parent.convertToNodeSpaceAR(pos));
        nodeHighLightAnim.active = true;

        let ctlHighLightAction: cc.Animation = nodeHighLightAnim.getComponent(cc.Animation);
        ctlHighLightAction.defaultClip.speed = this._getAnimClipSpeedByDuring(
            ctlHighLightAction.defaultClip,
            this._fActExecute_LuckBlowHightLight
        );
        ctlHighLightAction.defaultClip.wrapMode = cc.WrapMode.Normal;
        ctlHighLightAction.play();
        // });
    }

    /**
     * 清除特殊牌型高亮动画
     * @param cleanup
     */
    private _clearSpecialCardTypeHighLightAnim(cleanup: boolean = true): void {
        let node: cc.Node = cc.find('special_card_type_high_light_anim', this._nodeAnim);
        if (node && cc.isValid(node, true)) {
            node.removeFromParent(cleanup);
            if (cleanup) node.destroy();
        }
    }

    /**
     * 获取幸运一击赔率映射
     * @param type
     */
    private _getLuckBlowOdds(type: network.CardResult): domain.OddsDetail | undefined {
        // let oddsDetail: domain.OddsDetail = new domain.OddsDetail();
        // let option: humanboy.BetZoneOption = humanboy.BetZoneOption.BetZoneOption_DUMMY;

        // 设置 赔率的牌型 和 结算牌型 映射
        let mapSpecialCardType: Map<network.CardResult, network.BetZoneOption> = new Map();
        mapSpecialCardType.set(network.CardResult.YI_DUI, network.BetZoneOption.POS_LUCK_2);
        mapSpecialCardType.set(network.CardResult.LIAN_DUI, network.BetZoneOption.POS_LUCK_3);
        mapSpecialCardType.set(network.CardResult.SAN_TIAO, network.BetZoneOption.POS_LUCK_4);
        mapSpecialCardType.set(network.CardResult.SHUN_ZI, network.BetZoneOption.POS_LUCK_5);
        mapSpecialCardType.set(network.CardResult.TONG_HUA, network.BetZoneOption.POS_LUCK_5);
        mapSpecialCardType.set(network.CardResult.HU_LU, network.BetZoneOption.POS_LUCK_6);
        mapSpecialCardType.set(network.CardResult.SI_TIAO, network.BetZoneOption.POS_LUCK_6);
        mapSpecialCardType.set(network.CardResult.TONG_HUA_SHUN, network.BetZoneOption.POS_LUCK_6);
        mapSpecialCardType.set(network.CardResult.HUANG_TONG, network.BetZoneOption.POS_LUCK_6);

        // 获取结算牌型所映射的赔率牌型
        // mapSpecialCardType.forEach((v: humanboy.BetZoneOption, k: humanboy.CardResult): string => {
        //     if (k === type) {
        //         option = v;
        //         return 'break';
        //     }
        // });

        const option = mapSpecialCardType.get(type);

        // 判断映射后的赔率牌型是否在下发的赔率牌型列表中
        const vOddsDetail = this._humanboyRoom.roomParams.oddsDetails;
        for (const detail of vOddsDetail) {
            if (option === detail.option) {
                // oddsDetail = humanboy_proto.OddsDetail.create(vOddsDetail[i]);
                // break;
                return detail;
            }
        }

        return undefined;

        // return oddsDetail;
    }

    /**
     * 是否击中 JP
     */
    private _isHitJackpotAward(): boolean {
        return this._humanboyRoom.jackpot.hitJackpotAwards.length > 0;
    }

    /**
     * 获取 JP 动画执行时间
     */
    private _getJackpotAwardExecuteTime(): number {
        let fRet = 0;
        let bHitJPCoin = false;

        let vHitJackpotAward = this._humanboyRoom.jackpot.hitJackpotAwards;
        for (const award of vHitJackpotAward) {
            fRet += this._fActDelayed_JackPot;
            fRet += this._fActExecute_JackPot;

            if (award.hitJackpotAwardData.length > 0) {
                bHitJPCoin = true;
                fRet += this._fActDelayed_FlyWinCoin;
                fRet += this._fActExecute_FlyWinCoin;
            }
        }

        // 若击中JP(且有飞金币动画), 则加上飘分延时时间
        if (bHitJPCoin) {
            fRet += this._fActDelayed_FlyWinCoin;
        }

        return fRet;
    }

    /**
     * 显示jackpot动画
     * @param anim
     * @param delayed
     */
    private _showJackPotAnim(anim: boolean = true, delayed: number = 0): void {
        this._clearJackPotAnim();
        if (!this._isHitJackpotAward()) return;

        let fDelayedTime = 0;
        let vHitJackpotAward = this._humanboyRoom.jackpot.hitJackpotAwards;
        for (const award of vHitJackpotAward) {
            if (anim) {
                let option: network.BetZoneOption = award.option;

                // jackpot
                fDelayedTime += this._fActDelayed_JackPot;
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(fDelayedTime),
                        cc.callFunc((): void => {
                            this._showJackPotAnimByAreaZone(option, anim, delayed);
                        }, this)
                    )
                );
                fDelayedTime += this._fActExecute_JackPot;

                // fly coin
                if (award.hitJackpotAwardData.length > 0) {
                    fDelayedTime += this._fActDelayed_FlyWinCoin;
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(fDelayedTime),
                            cc.callFunc((): void => {
                                this._playSoundEffect(macros.Audio.Get_win_coin);
                                this._showJackPotFlagsAndFlyCoinAnim(option);
                            }, this)
                        )
                    );
                    fDelayedTime += this._fActExecute_FlyWinCoin;
                }
            } else {
                this._showJackPotAnimByAreaZone(award.option, anim, delayed);
            }
        }
    }

    /**
     * 清除 JP 动画
     * @param cleanup
     */
    private _clearJackPotAnim(cleanup: boolean = true): void {
        let node: cc.Node = cc.find('jackpot_anim', this._nodeAnim);
        if (node && cc.isValid(node, true)) {
            node.removeFromParent(cleanup);
            if (cleanup) node.destroy();
        }
    }

    /**
     * 显示指定区域的 JP 动画
     * @param betOption
     * @param anim
     * @param delayed
     */
    private _showJackPotAnimByAreaZone(
        betOption: network.BetZoneOption,
        anim: boolean = true,
        delayed: number = 0
    ): void {
        let nAreaIdx: number = this._getAreaIdxByBetOption(betOption);
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        let panelBorder: cc.Node = this._vAreasInfo[nAreaIdx].panelBorder;
        let pos: cc.Vec2 = cc.Vec2.ZERO;
        panelBorder.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);

        let fileName = '';
        let specialCardType = '';

        switch (this._eGameboyScreenType) {
            case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD:
                fileName = macros.Dynamic_Assets.HUMANBOY_JACKPOT_BROAD_PREFAB;
                break;

            case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW:
                fileName = macros.Dynamic_Assets.HUMANBOY_JACKPOT_NARROW_PREFAB;
                break;

            case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL:
            default:
                fileName = macros.Dynamic_Assets.HUMANBOY_JACKPOT_PREFAB;
                break;
        }

        pf.addressableAssetManager.loadAsset<cc.Prefab>(fileName).then((prefab: cc.Prefab) => {
            let nodeJackpotAnim: cc.Node = cc.instantiate(prefab);
            this._nodeAnim.addChild(nodeJackpotAnim, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_1);
            nodeJackpotAnim.name = 'jackpot_anim';
            nodeJackpotAnim.setPosition(nodeJackpotAnim.parent.convertToNodeSpaceAR(pos));
            nodeJackpotAnim.active = true;

            // 检测牌型
            const vPlayerHoleCard = this._humanboyRoom.roundInfo.playerHoleCard;
            for (const card of vPlayerHoleCard) {
                if (card.option === betOption) {
                    switch (card.level) {
                        case network.CardResult.LIAN_DUI:
                            specialCardType = macros.Assets.HUMANBOY_SPECIAL_TWO_PAIRS_SPRITE;
                            break;

                        case network.CardResult.SAN_TIAO:
                            specialCardType = macros.Assets.HUMANBOY_SPECIAL_TREE_OF_A_KIND_SPRITE;
                            break;

                        case network.CardResult.SHUN_ZI:
                            specialCardType = macros.Assets.HUMANBOY_SPECIAL_STRAIGHT_SPRITE;
                            break;

                        case network.CardResult.TONG_HUA:
                            specialCardType = macros.Assets.HUMANBOY_SPECIAL_FLUSH_SPRITE;
                            break;

                        case network.CardResult.HU_LU:
                            specialCardType = macros.Assets.HUMANBOY_SPECIAL_GOURD_SPRITE;
                            break;

                        case network.CardResult.SI_TIAO:
                            specialCardType = macros.Assets.HUMANBOY_SPECIAL_FOUR_OF_A_KIND_SPRITE;
                            break;

                        case network.CardResult.TONG_HUA_SHUN:
                            specialCardType = macros.Assets.HUMANBOY_SPECIAL_STRAIGHT_FLUSH_SPRITE;
                            break;

                        case network.CardResult.HUANG_TONG:
                            specialCardType = macros.Assets.HUMANBOY_SPECIAL_ROYAL_FLUSH_SPRITE;
                            break;

                        default:
                            break;
                    }

                    // 设置牌型
                    do {
                        let node_jackpot: cc.Node = nodeJackpotAnim.getChildByName('Node_Jackpot');
                        if (specialCardType.length > 0) {
                            let node_card_type: cc.Node = node_jackpot.getChildByName('node_card_type');
                            let img_card_type: cc.Sprite = node_card_type.getChildByName('img').getComponent(cc.Sprite);
                            img_card_type.sizeMode = cc.Sprite.SizeMode.RAW;
                            img_card_type.spriteFrame = pf.addressableAssetManager.getAsset(specialCardType);
                        }

                        let holecards = card.holeCards;
                        let node_card: cc.Node = node_jackpot.getChildByName('node_card');
                        for (let j = 0; j < node_card.childrenCount; ++j) {
                            let img_card: cc.Node = node_card.getChildByName(pf.StringUtil.formatC('card_%d', j));
                            if (!img_card) continue;

                            if (j < 0 || j >= holecards.length) continue;

                            const card = this.createPokerCard();
                            card.node.setAnchorPoint(img_card.getAnchorPoint());
                            card.node.setPosition(cc.Vec2.ZERO);
                            card.SetContent(holecards[j].number, holecards[j].suit);
                            card.SetFace(true);
                            img_card.addChild(card.node);
                        }
                    } while (0);
                }
            }

            // 开始动画
            let ctlJackpotAction: cc.Animation = nodeJackpotAnim.getComponent(cc.Animation);
            if (anim) {
                this._playSoundEffect(macros.Audio.Jackpot);

                ctlJackpotAction.defaultClip.speed = this._getAnimClipSpeedByDuring(
                    ctlJackpotAction.defaultClip,
                    this._fActExecute_JackPot
                );
                ctlJackpotAction.defaultClip.wrapMode = cc.WrapMode.Normal;
                ctlJackpotAction.play();
                ctlJackpotAction.on(
                    cc.Animation.EventType.FINISHED,
                    (type: cc.Animation.EventType, state?: cc.AnimationState): void => {
                        ctlJackpotAction.off(cc.Animation.EventType.FINISHED);
                        nodeJackpotAnim.removeFromParent(true);
                        pf.UIUtil.destroyNode(nodeJackpotAnim);
                    },
                    this
                );
            } else {
                ctlJackpotAction.play();
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(delayed),
                        cc.callFunc((): void => {
                            nodeJackpotAnim.removeFromParent(true);
                            pf.UIUtil.destroyNode(nodeJackpotAnim);
                        }, this)
                    )
                );
            }
        });
    }

    /**
     * 更新指定玩家连胜状态
     * @param uid
     * @param bAnim
     */
    private _updatePlayerWinCount(uid: number, bAnim: boolean = false): void {
        let vPlayerHeadBgNode: cc.Node[] = this._getPlayerHeadNodesByUid(uid);
        for (const headBgNode of vPlayerHeadBgNode) {
            // 富豪No1 和 神算子 不显示连胜
            if (
                this._vOtherPlayerInfo[0].nodeHead === headBgNode ||
                this._vOtherPlayerInfo[1].nodeHead === headBgNode
            ) {
                continue;
            }

            let nodeName: string = 'win_player_win_count_' + headBgNode.uuid;
            let win_node: cc.Node = cc.find(nodeName, this.node);
            if (win_node && cc.isValid(win_node, true)) {
                this.node.removeChild(win_node);
                win_node.destroy();
            }

            const player = this._humanboyRoom.getPlayer(uid);
            let keepWinCount = player ? player.keepWinCount : 0;
            if (keepWinCount >= 3) {
                keepWinCount = keepWinCount > 10 ? 11 : keepWinCount;

                let offsetY: number = headBgNode === this._img_self_head.node ? 40 : 70;
                let tmpPos: cc.Vec2 = cc.Vec2.ZERO;
                headBgNode.convertToWorldSpaceAR(cc.v2(0, offsetY), tmpPos);

                let sprWinCount: cc.Sprite = new cc.Node().addComponent(cc.Sprite);
                this.node.addChild(sprWinCount.node, eHumanboyLocalZorder.HL_ZORDER_IMG_WIN_COUNT);
                sprWinCount.node.active = true;
                sprWinCount.node.name = nodeName;
                sprWinCount.node.setPosition(sprWinCount.node.parent.convertToNodeSpaceAR(tmpPos));

                let fileName: string = pf.StringUtil.formatC('win_count_%d', keepWinCount);
                sprWinCount.spriteFrame = this._atlas_cb_language.getSpriteFrame(fileName);

                // animation
                if (bAnim) {
                    let targetPos: cc.Vec2 = cc.v2(sprWinCount.node.position);
                    let bornPos: cc.Vec2 = cc.v2(targetPos);
                    let headMidWorldPos: cc.Vec2 = cc.Vec2.ZERO;
                    headBgNode.parent.convertToWorldSpaceAR(headBgNode.getPosition(), headMidWorldPos);
                    if (headMidWorldPos.x < cc.winSize.width / 2) {
                        let start_left_x: number =
                            (0 - sprWinCount.node.parent.anchorX) * sprWinCount.node.parent.width;
                        bornPos.x = start_left_x - sprWinCount.node.width;
                    } else {
                        let start_right_x: number =
                            (1 - sprWinCount.node.parent.anchorX) * sprWinCount.node.parent.width;
                        bornPos.x = start_right_x + sprWinCount.node.width;
                    }
                    sprWinCount.node.setPosition(bornPos);

                    let mt: cc.ActionInterval = cc.moveTo(0.8, targetPos);
                    let ebo: cc.ActionInterval = mt.easing(cc.easeBackOut());
                    sprWinCount.node.runAction(ebo);
                }
            }
        }
    }

    /**
     * 更新所有玩家连胜状态
     * @param bAnim
     */
    private _updateAllPlayerWinCount(bAnim: boolean = false): void {
        this._updatePlayerWinCount(this._authService.currentUser.userId, bAnim);

        // 这里按照服务器发的gamePlayers顺序放
        for (let i = 0; i < this._vOtherPlayerInfo.length; ++i) {
            let vOtherPlayer: pf.services.GamePlayer[] = this._humanboyRoom.otherPlayers;
            if (i < vOtherPlayer.length) {
                this._updatePlayerWinCount(vOtherPlayer[i].uid, bAnim);
            }
        }
    }

    /**
     * 显示玩家胜利头像框光环动画
     * @param uid
     * @param fDelayTime
     */
    private _showWinPlayerLightAnim(uid: number, fDelayTime: number): void {
        let vPlayerHeads: cc.Node[] = this._getPlayerHeadNodesByUid(uid);
        if (vPlayerHeads.length <= 0) return;

        let callFunc: Function = (): void => {
            for (const head of vPlayerHeads) {
                // 自己不显示光环
                if (head === this._img_self_head.node) continue;

                let win_node: cc.Node = cc.find('win_player_light', head);
                if (win_node && cc.isValid(win_node, true)) {
                    head.removeChild(win_node);
                    win_node.destroy();
                }
                let winPlayerLightAnim: cc.Node = cc.instantiate(
                    pf.addressableAssetManager.getAsset(macros.Assets.WIN_PLAYER_LIGHT)
                );
                head.addChild(winPlayerLightAnim);

                winPlayerLightAnim.active = true;
                winPlayerLightAnim.name = 'win_player_light';
                winPlayerLightAnim.setPosition(cc.Vec2.ZERO);
                winPlayerLightAnim.zIndex = 10;

                let winPlayerLightAction: cc.Animation = winPlayerLightAnim.getComponent(cc.Animation);
                winPlayerLightAction.defaultClip.wrapMode = cc.WrapMode.Loop;
                winPlayerLightAction.play();
            }
        };

        this._nodeAnim.runAction(cc.sequence(cc.delayTime(fDelayTime), cc.callFunc(callFunc, this)));
    }

    /**
     * 隐藏所有玩家胜利头像框光环动画
     */
    private _hideAllWinPlayerLightAnim(): void {
        for (const info of this._vOtherPlayerInfo) {
            let win_node: cc.Node = cc.find('win_player_light', info.nodeHead);
            if (win_node && cc.isValid(win_node, true)) {
                info.nodeHead.removeChild(win_node);
                win_node.destroy();
            }
        }
    }

    /**
     * 从庄家向普通区域吐金币
     * @param anim
     */
    private _showCoinToNormalAreaFromDealer(anim: boolean = true): void {
        let vWinArea: Map<number, number> = new Map();
        // let vSettles: humanboy_proto.PlayerSettle[] = [];
        // pf.StringUtil.deepCopy(humanboyDataMgr.getHumanboyRoom().vPlayerSettles, vSettles);
        // vSettles.push(humanboyDataMgr.getHumanboyRoom().tOtherPlayerSettle);

        // for (let i = 0; i < vSettles.length; ++i) {
        this._humanboyRoom.roundInfo.getAllPlayerSettles().forEach((it) => {
            // let it: humanboy_proto.PlayerSettle = vSettles[i];
            // let uid: number = it.uid; //
            // let llWinAmount = it.pos4WinAmount; // 4门闲区域赢的多少 (扣除服务费后的)

            for (const zoneSettleDetail of it.settle) {
                let option: network.BetZoneOption = zoneSettleDetail.option;
                let amount: number = zoneSettleDetail.winAmount;
                let betAmount: number = zoneSettleDetail.betAmount;

                // 特殊牌型区除外
                if (option >= network.BetZoneOption.POS_LUCK) continue;

                let nAreaIdx: number = this._getAreaIdxByBetOption(option);
                if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) continue;

                let it_zoneData: domain.BetZone = this._humanboyRoom.betZones.get(option);
                if (it_zoneData) {
                    // 该区域闲家赢(庄家赔付 - 区域上的金币 = 最终吐出的金币, 一起飞向玩家)
                    if (it_zoneData.optionResult.result < 0 && amount > 0) {
                        if (!vWinArea.get(nAreaIdx)) {
                            vWinArea.set(nAreaIdx, 0);
                        }

                        // 获取值
                        let val: number = vWinArea.get(nAreaIdx);

                        // 加上该玩家赢
                        val += amount;

                        // 减去玩家下注
                        val -= betAmount;

                        // 刷新值
                        vWinArea.set(nAreaIdx, val);
                    }
                }
            }
        });

        let vDealerInfoData = this._humanboyRoom.dealers;
        for (let i = 0; i < vDealerInfoData.length; ++i) {
            if (i < 0 || i >= this._vDealerInfo.length) continue;

            let imgGold: cc.Sprite = this._vDealerInfo[i].imgGold;
            let world_pos: cc.Vec2 = cc.Vec2.ZERO;
            imgGold.node.convertToWorldSpaceAR(cc.Vec2.ZERO, world_pos);

            vWinArea.forEach((k: number, v: number): void => {
                let nAreaIdx: number = k;
                let llAmount: number = Math.abs(v);

                let vAmountlevel: number[] = this._getBetDetailAmounts(llAmount);
                for (let i = 0; i < vAmountlevel.length; ++i) {
                    let fDelayedTime: number = 0.2 + i * 0.02;
                    this._showCoinAnimFromPos(world_pos, nAreaIdx, vAmountlevel[i], anim, fDelayedTime);
                }
            });
        }
    }

    /**
     * 从系统向幸运一击区域吐金币
     * @param anim
     */
    private _showCoinToLuckBlowAreaFromSystem(anim: boolean = true): void {
        // let vSettles: humanboy_proto.PlayerSettle[] = [];
        // pf.StringUtil.deepCopy(humanboyDataMgr.getHumanboyRoom().vPlayerSettles, vSettles);
        // vSettles.push(humanboyDataMgr.getHumanboyRoom().tOtherPlayerSettle);

        // for (let i = 0; i < vSettles.length; ++i) {
        //     let tSettle: humanboy_proto.PlayerSettle = vSettles[i];
        //     for (let j = 0; j < tSettle.settle.length; ++j) {
        //         let zoneDetail: humanboy_proto.IZoneSettleDetail = tSettle.settle[j];
        //         let llLuckWinAmount: number = zoneDetail.winAmount;
        //         if (llLuckWinAmount <= 0) continue;

        //         // 减去该区域已下注的金币
        //         llLuckWinAmount -= zoneDetail.betAmount;

        //         let nAreaIdx: number = this._getAreaIdxByBetOption(zoneDetail.option);
        //         if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) continue;

        //         let panelCoin: cc.Node = this._vAreasInfo[nAreaIdx].panelCoin;
        //         let world_pos: cc.Vec2 = cc.Vec2.ZERO;
        //         panelCoin.convertToWorldSpaceAR(cc.Vec2.ZERO, world_pos);
        //         let vAmountlevel: number[] = this._getBetDetailAmounts(llLuckWinAmount);
        //         for (let k = 0; k < vAmountlevel.length; ++k) {
        //             let fDelayedTime: number = 0.2 + k * 0.02;
        //             this._showCoinAnimFromPos(world_pos, nAreaIdx, vAmountlevel[k], anim, fDelayedTime);
        //         }
        //     }
        // }

        this._humanboyRoom.roundInfo.getAllPlayerSettles().forEach((playerSettle) => {
            for (const zoneSettleDetail of playerSettle.settle) {
                let llLuckWinAmount: number = zoneSettleDetail.winAmount;
                if (llLuckWinAmount <= 0) continue;

                // 减去该区域已下注的金币
                llLuckWinAmount -= zoneSettleDetail.betAmount;

                let nAreaIdx: number = this._getAreaIdxByBetOption(zoneSettleDetail.option);
                if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) continue;

                let panelCoin: cc.Node = this._vAreasInfo[nAreaIdx].panelCoin;
                let world_pos: cc.Vec2 = cc.Vec2.ZERO;
                panelCoin.convertToWorldSpaceAR(cc.Vec2.ZERO, world_pos);
                let vAmountlevel: number[] = this._getBetDetailAmounts(llLuckWinAmount);
                for (let k = 0; k < vAmountlevel.length; ++k) {
                    let fDelayedTime: number = 0.2 + k * 0.02;
                    this._showCoinAnimFromPos(world_pos, nAreaIdx, vAmountlevel[k], anim, fDelayedTime);
                }
            }
        });
    }

    /**
     * 从对应位置向玩家飞金币(公用接口)
     * @param uid
     * @param amount
     * @param fromNode
     * @param bRandomPos
     * @param func
     */
    private _showFlyCoinToPlayerAnim(
        uid: number,
        amount: number,
        fromNode: cc.Node,
        bRandomPos: boolean = true,
        func: () => void = null
    ): void {
        if (amount <= 0 || !fromNode) return;

        // 飞往的目标节点数
        let vPlayerCoinNodes: cc.Node[] = this._getPlayerCoinNodesByUid(uid);

        // 桌面没有该玩家
        if (vPlayerCoinNodes.length === 0) {
            console.log(
                'showFlyCoinToPlayerAnim - playerSettles uid: %d not in gameplayers, use player list button',
                uid
            );
            vPlayerCoinNodes.push(this._btn_playerList);
        }

        // 找出该玩家同时存在哪几个头像(一个玩家可以同时是富豪,神算子等)
        for (let i = 0; i < vPlayerCoinNodes.length; ++i) {
            // 自己是富豪/神算子， 只回收一次金币到自己头像
            if (uid === this._authService.currentUser.userId && i > 0) {
                continue;
            }

            let fromHead: cc.Node = vPlayerCoinNodes[i];
            // 飞金币动画
            do {
                let vAmountlevel: number[] = this._getBetDetailAmounts(amount);
                for (let k = 0; k < vAmountlevel.length; ++k) {
                    let flyCoin: HumanboyBetCoinControl = this._createFlyCoin(vAmountlevel[k]);
                    this._nodeAnim.addChild(flyCoin.node, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_0);

                    let offset: cc.Vec2 = cc.Vec2.ZERO;
                    if (bRandomPos) {
                        offset.x =
                            pf.StringUtil.randomRange(fromNode.width * 0.3, fromNode.width * 0.7) -
                            fromNode.width * fromNode.anchorX;
                        offset.y =
                            pf.StringUtil.randomRange(fromNode.height * 0.3, fromNode.height * 0.7) -
                            fromNode.height * fromNode.anchorY;
                    }

                    let coinFlyFromPos: cc.Vec2 = cc.Vec2.ZERO;
                    fromNode.convertToWorldSpaceAR(offset, coinFlyFromPos);

                    let coinFlyDestPos: cc.Vec2 = cc.Vec2.ZERO;
                    fromHead.convertToWorldSpaceAR(cc.Vec2.ZERO, coinFlyDestPos);

                    flyCoin.node.parent.convertToNodeSpaceAR(coinFlyFromPos, coinFlyFromPos);
                    flyCoin.node.parent.convertToNodeSpaceAR(coinFlyDestPos, coinFlyDestPos);
                    flyCoin.node.setPosition(coinFlyFromPos);
                    flyCoin.node.active = false;

                    // 开始飞金币
                    this.scheduleOnce((elapsed: number) => {
                        flyCoin.node.active = true;
                        flyCoin.node.runAction(
                            cc.sequence(
                                cc.delayTime(0.2 + k * 0.02),
                                cc.moveTo(0.5, coinFlyDestPos).easing(cc.easeOut(0.8)),
                                cc.destroySelf()
                            )
                        );
                    }, this._fActDelayed_FlyWinCoin);
                }
            } while (0);

            if (func && amount > 0) {
                // 头像弹性动画
                this._showHeadElasticAnim(fromHead, this._fActExecute_FlyWinCoin);

                // 赢的玩家头像光环
                this._showWinPlayerLightAnim(uid, this._fActExecute_FlyWinCoin);

                // 加金币动画
                this._showAddCoinAnim(fromHead, amount, this._fActExecute_FlyWinCoin, func);
            }
        }
    }

    /**
     * 加金币飘分动画(公用接口)
     * @param toNode
     * @param amount
     * @param fDelayTime
     * @param func
     */
    private _showAddCoinAnim(toNode: cc.Node, amount: number, fDelayTime: number, func: () => void = null): void {
        if (!toNode) return;

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(fDelayTime),
                cc.callFunc((): void => {
                    let strTotalWinAmount: string = pf.StringUtil.transNumberToString(amount, 2, true);
                    let flutterScore: HumanboyFlutterScoreControl = cc
                        .instantiate(
                            pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.HUMANBOY_FLUTTER_SCORE)
                        )
                        .getComponent(HumanboyFlutterScoreControl);
                    flutterScore.node.setScale(1.4);
                    this.node.addChild(flutterScore.node, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_2);
                    flutterScore.init(strTotalWinAmount);

                    let pos: cc.Vec2 = cc.Vec2.ZERO;
                    toNode.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);
                    flutterScore.node.parent.convertToNodeSpaceAR(pos, pos);

                    // 适配飘数字的位置
                    do {
                        let offset = 10;
                        let extra_w: number = cc.winSize.width / 2 - Math.abs(pos.x);

                        let w: number = flutterScore.node.width * flutterScore.node.scaleX;
                        let h: number = flutterScore.node.height * flutterScore.node.scaleY;

                        let bDealerCoinNode = false;
                        for (const info of this._vDealerInfo) {
                            if (toNode === info.imgGold.node) {
                                bDealerCoinNode = true;
                                break;
                            }
                        }

                        // 庄家
                        if (bDealerCoinNode) {
                            let tmp_x = Math.max(0, w / 2 - extra_w);
                            pos.x += tmp_x;
                            pos.y -= toNode.height / 2 + offset;
                            // pos.y -= (toNode.height / 2 + h / 2 + offset);
                        }
                        // 自己
                        else if (toNode === this._img_self_gold.node) {
                            let tmp_x = Math.max(0, w / 2 - extra_w);
                            pos.x += tmp_x;
                            pos.y += toNode.height / 2 + offset;
                            // pos.y += (toNode.height / 2 + h / 2 + offset);
                        }
                        // 其他玩家
                        else if (toNode === this._btn_playerList) {
                            let tmp_x = Math.max(0, w / 2 - extra_w);
                            pos.x -= tmp_x;
                            pos.y += toNode.height / 2 + offset;
                            // pos.y += (toNode.height / 2 + h / 2 + offset);
                        }
                        // 左列表
                        else if (pos.x < 0) {
                            pos.x += toNode.width / 2 + w / 2 + offset;
                        }
                        // 右列表
                        else if (pos.x > 0) {
                            pos.x -= toNode.width / 2 + w / 2 + offset;
                        }
                    } while (0);

                    flutterScore.node.setPosition(pos);
                    flutterScore.show();
                    this._nodeAnim.runAction(
                        cc.sequence(cc.delayTime(this._fActDelayed_FlyWinCoin), cc.callFunc(func, this))
                    );
                }, this)
            )
        );
    }

    /**
     * 头像弹性动画(公用接口)
     * @param toNode
     * @param fDelayTime
     */
    private _showHeadElasticAnim(toNode: cc.Node, fDelayTime: number): void {
        if (!toNode) return;

        let scaleRatio: number = toNode.scale;
        toNode.runAction(
            cc.sequence(
                cc.delayTime(fDelayTime),
                cc.scaleTo(0.5, scaleRatio + 0.2).easing(cc.easeBackOut()),
                cc.scaleTo(0.2, scaleRatio - 0.2),
                cc.scaleTo(0.5, scaleRatio).easing(cc.easeBackOut())
            )
        );
    }

    /**
     * 庄家飘分动画
     * @param fDelayTime
     */
    private _showDealerAddCoinAnim(fDelayTime: number): void {
        let vDealerInfoData = this._humanboyRoom.dealers;
        for (let i = 0; i < vDealerInfoData.length; ++i) {
            if (i < 0 || i >= this._vDealerInfo.length) continue;
            let node_flyDestination: cc.Node = this._vDealerInfo[i].imgGold.node;
            let llAmount: number = vDealerInfoData[i].recentlyWinCoin;
            this._showHeadElasticAnim(node_flyDestination, fDelayTime);
            this._showAddCoinAnim(node_flyDestination, llAmount, fDelayTime);
            // 延时更新庄家信息
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(fDelayTime),
                    cc.callFunc((): void => {
                        this._updateDealerInfo();
                    }, this)
                )
            );
        }
    }

    /**
     * 显示庄家win金币回收动画
     * @param mapDealerWinAreaOption
     */
    private _showDealerWinFlagsAndFlyCoinsAnim(mapDealerWinAreaOption: Map<network.BetZoneOption, number>): void {
        // 隐藏所有lose的区域(幸运一击区域除外)的下注金币
        this._hideLoseAreaCoinsAnim(true, false);
        // 飞金币
        let vDealerInfoData = this._humanboyRoom.dealers;
        for (let i = 0; i < vDealerInfoData.length; ++i) {
            if (i < 0 || i >= this._vDealerInfo.length) continue;
            let node_flyDestination: cc.Node = this._vDealerInfo[i].imgGold.node;
            let coinFlyFromPos: cc.Vec2 = cc.Vec2.ZERO;
            let coinFlyDestPos: cc.Vec2 = cc.Vec2.ZERO;
            mapDealerWinAreaOption.forEach((option: network.BetZoneOption, gold: number): string => {
                let nAreaIdx: number = this._getAreaIdxByBetOption(option);
                if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return 'continue';
                let panelCoin: cc.Node = this._vAreasInfo[nAreaIdx].panelCoin;
                let vAmountlevel: number[] = this._getBetDetailAmounts(gold);
                for (let k = 0; k < vAmountlevel.length; ++k) {
                    coinFlyFromPos.x =
                        pf.StringUtil.randomRange(panelCoin.width * 0.3, panelCoin.width * 0.7) -
                        panelCoin.width * panelCoin.anchorX;
                    coinFlyFromPos.y =
                        pf.StringUtil.randomRange(panelCoin.height * 0.3, panelCoin.height * 0.7) -
                        panelCoin.height * panelCoin.anchorY;
                    panelCoin.convertToWorldSpaceAR(coinFlyFromPos, coinFlyFromPos);
                    node_flyDestination.convertToWorldSpaceAR(cc.Vec2.ZERO, coinFlyDestPos);
                    let flyCoin: HumanboyBetCoinControl = this._createFlyCoin(vAmountlevel[k]);
                    this._nodeAnim.addChild(flyCoin.node, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_0);
                    flyCoin.node.parent.convertToNodeSpaceAR(coinFlyFromPos, coinFlyFromPos);
                    flyCoin.node.parent.convertToNodeSpaceAR(coinFlyDestPos, coinFlyDestPos);
                    flyCoin.node.setPosition(coinFlyFromPos);
                    // 开始飞金币
                    flyCoin.node.active = true;
                    flyCoin.node.runAction(
                        cc.sequence(
                            cc.delayTime(0.2 + k * 0.02),
                            cc.moveTo(0.5, coinFlyDestPos).easing(cc.easeOut(0.8)),
                            cc.destroySelf()
                        )
                    );
                }
            });
        }
    }

    /**
     * 显示Jackpot金币回收动画
     * @param betOption
     */
    private _showJackPotFlagsAndFlyCoinAnim(betOption: network.BetZoneOption): void {
        let index = -1;
        let vHitJackpotAward = this._humanboyRoom.jackpot.hitJackpotAwards;
        for (let i = 0; i < vHitJackpotAward.length; ++i) {
            if (vHitJackpotAward[i].option === betOption) {
                index = i;
                break;
            }
        }
        if (index < 0) return;

        for (const data of vHitJackpotAward[index].hitJackpotAwardData) {
            let uid: number = data.uid;
            let amount: number = data.amount;

            this._showFlyCoinToPlayerAnim(uid, amount, this._panel_jackpot, true, (): void => {
                // 更新击中jackpot后的金币
                let llBscGold: number = this._humanboyRoom.getPlayerBeforeSettlementGold(uid);
                llBscGold += amount;
                this._updatePlayerTempGold(uid, llBscGold);
                this._humanboyRoom.setPlayerBeforeSettlementGold(uid, llBscGold);
            });
        }
    }

    /**
     * 显示所有赢区域金币回收动画
     */
    private _showAllAreaWinFlagsAndFlyCoinAnim(): void {
        // 隐藏所有(win + 平局)的区域的下注金币
        this._hideWinAreaCoinsAnim(false, true);

        this._humanboyRoom.roundInfo.getAllPlayerSettles().forEach((it) => {
            // let it: humanboy_proto.PlayerSettle = vSettles[i];
            let uid: number = it.uid; //
            let llWinAmount: number = it.pos4WinAmount; // 4门闲区域总赢的钱(扣除服务费后的)
            let llLuckWinAmount: number = it.posLuckWinAmount; // 幸运一击区域总赢的钱

            for (const zoneSettleDetail of it.settle) {
                let option: network.BetZoneOption = zoneSettleDetail.option;
                let nAreaIdx: number = this._getAreaIdxByBetOption(option);
                if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) continue;

                let amount = zoneSettleDetail.winAmount;
                if (amount > 0) {
                    let panelCoin: cc.Node = this._vAreasInfo[nAreaIdx].panelCoin;
                    this._showFlyCoinToPlayerAnim(uid, amount, panelCoin, true);
                }
            }

            // 额外飘分等其他动画
            do {
                let llTotalAmount = llWinAmount + llLuckWinAmount;
                if (llTotalAmount > 0) {
                    let vPlayerCoinNodes: cc.Node[] = this._getPlayerCoinNodesByUid(uid);

                    // 桌面没有该玩家
                    if (vPlayerCoinNodes.length === 0) {
                        console.log('playerSettles uid: %d not in gameplayers, use player list button', uid);
                        vPlayerCoinNodes.push(this._btn_playerList);
                    }

                    // 找出该玩家同时存在哪几个头像(一个玩家可以同时是富豪,神算子等)
                    for (let k = 0; k < vPlayerCoinNodes.length; ++k) {
                        // 自己是富豪/神算子, 只回收一次金币到自己头像
                        if (k > 0 && uid === this._authService.currentUser.userId) {
                            continue;
                        }

                        let fromHead: cc.Node = vPlayerCoinNodes[k];

                        // 头像弹性动画
                        this._showHeadElasticAnim(fromHead, this._fActExecute_FlyWinCoin);

                        // 赢的玩家头像光环
                        this._showWinPlayerLightAnim(uid, this._fActExecute_FlyWinCoin);

                        // 加金币动画
                        this._showAddCoinAnim(fromHead, llTotalAmount, this._fActExecute_FlyWinCoin);
                    }
                }
            } while (0);
        });
    }

    /**
     * 充值
     */
    private recharge(): void {
        if (this._platform === 'pkw') {
            if (pf.system.isBrowser) {
                cr.commonResourceAgent.commonDialog.showMsg(
                    pf.languageManager.getString('UIOpenNewWindow'),
                    [pf.languageManager.getString('TipsPanel_sure_button')],
                    () => cr.commonResourceAgent.shop?.open()
                );
            } else {
                const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                context.isSelfRecharge = true;
                // this.exitGame();
                this.tryLeaveRoom();
            }
        } else if (this._platform === 'wpk') {
            cr.commonResourceAgent.commonDialog.showMsg(
                pf.languageManager.getString('PokerMaster_dialog_recharge'),
                [
                    pf.languageManager.getString('TipsPanel_sure_button'),
                    pf.languageManager.getString('TipsPanel_cancel_button')
                ],
                () => {
                    const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                    context.isSelfRecharge = true;
                    context.exitCallback(pf.client.ExitType.Standard);
                },
                () => {
                    // do nothing here
                }
            );
        }
    }

    /**
     * 设置声音改变
     */
    private _onMsgSoundSwitch(): void {
        pf.audioManager.enableMusic = pf.localStorage.getItem(macros.AudioSettingKeys.MUSIC) !== 'false';
        pf.audioManager.enalbeSoundEffect = pf.localStorage.getItem(macros.AudioSettingKeys.SOUND_EFFECT) !== 'false';

        pf.audioManager.playMusic(macros.Audio.BGM);
    }

    /**
     * 游戏内错误提示
     */
    private _onMsgGameError(param: any): void {
        let code: number = pf.Util.Number(param);
        let strValue: string = pf.StringUtil.formatC('Humanboy_ServerErrorCode%d', code);

        // 下注额小于最小下注额
        if (code === network.ErrorCode.BET_TOO_SMALL) {
            let formatCoin: number = pf.StringUtil.clientGoldByServer(this._humanboyRoom.roomParams.smallBet);
            this._showGameToast(
                pf.StringUtil.formatC(pf.languageManager.getString(strValue), pf.StringUtil.numberToString(formatCoin))
            );
        } else if (
            code === network.ErrorCode.CAN_NOT_LEAVE_IN_BETTING ||
            code === network.ErrorCode.CAN_NOT_LEAVE_IN_DEALER
        ) {
            cr.commonResourceAgent.toastMessage.showMsg(pf.languageManager.getString(strValue));

            // possible scenario: push recharge button while there are still unsettled bets
            // isSelfRecharge needs to be reset or exiting game will lead to recharge flow
            const context = pf.app.getGameContext<pf.services.MiniGameContext>();
            context.isSelfRecharge = false;
        } else {
            this._showGameToast(pf.StringUtil.formatC('%s', pf.languageManager.getString(strValue)));
        }
    }

    /**
     * 服务器踢人
     */

    private _onMsgKick(param: network.IKickNotify): void {
        if (param.idle_roomid > 0) {
            if (!this._bSwitchTable) {
                this._humanboyRoom.roundInfo.idleRoomId = param.idle_roomid;
                this.showSwitchTable();
            }
            return;
        }

        let eKickType: number = pf.Util.Number(param.kickType);
        switch (eKickType) {
            case network.Kick.Kick_DUMMY:
                break;

            // 太长时间没下注
            case network.Kick.IDLE_LONG_TIME:
                {
                    let tips: string = pf.languageManager.getString('Humanboy_server_kick_long_time_text');
                    this._backToMainScene(tips);
                }
                break;

            // 停服踢人
            case network.Kick.Stop_World:
                {
                    let tips: string = pf.languageManager.getString('Humanboy_server_kick_stop_world_text');
                    this._backToMainScene(tips);
                }
                break;

            default:
                break;
        }
    }

    /**
     * 进入房间游戏数据同步
     */
    private _onMsgGameDataSyn(): void {
        this._bSwitchTable = false;
        this._resetAllUI();

        /* 客户端检测流程:
            => 停止下注, 翻牌
            0.庄家完胜完败检测
            1.显示幸运一击区域win动画
            2.路单动画
            3.特殊牌型检测
            4.飞庄家金币 + 系统收取幸运一击未击中的金币
            5.jackpot检测
            6.所有流程完毕, 统一更新至服务器最新数据
            => 清屏(等待下一局)
        */

        /* 节奏时间
            1.基础动画(结束动画 1.5s + 翻牌动画4s + 路单动画延时0.5s + 路单动画1s + 停顿1s) 总共 8s
            2.完胜完败动画 			2.5s
            3.幸运一击				0s			- 小牌 +0s		中牌 +4s			大牌+ 8s
            4.飞庄金币动画			0s			- 四门庄家赢||幸运一击系统赢			+ 2s
            5.飞闲金币动画			0s			- 有赢								+ 2s + 2s
            6.jackpot检测 			n * 3s		- 有赢								+ n * 2s
        */

        // 根据不同的游戏状态恢复游戏场景
        switch (this._humanboyRoom.gameState.roundState) {
            // 无

            case network.RoundState.RoundState_DUMMY:
                break;

            // 房间新建的，准备开局( do nothing )
            case network.RoundState.GAME_PENDING:
                break;

            // 新的一局
            case network.RoundState.NEW_ROUND:
                if (this._getLeftTime() >= this._fActExecute_RoundStart + this._fActExecute_SendCard) {
                    this._showRoundStartAnim();
                } else {
                    this._resetAllCards(true);
                }

                this._updateAllWayOut();
                break;

            // 下注
            case network.RoundState.BET:
                this._resetAllCards(true);
                this._recoverAreasCoin(true);

                if (this._getLeftTime() >= this._fActDelayed_FightBegin + this._fActExecute_FightBegin) {
                    this._showFightBeginAnim();
                }

                this._onMsgGameStartBet();
                this._updateAllWayOut();

                if (this._humanboyRoom.betSettings.canAdvanceAutoBet) {
                    this._checkAdvanceAutoReq();
                }
                break;

            // 处于结束期间并即将开启新的一局
            case network.RoundState.WAIT_NEXT_ROUND:
                this._resetAllCards(true);
                this._recoverAreasCoin(false);

                this._showAllCardsAnim(false);

                this._showAllWinFlagAnim();

                // 路子动画
                if (this._getLeftTime() >= this._fActExecute_WayOutLight) {
                    this._updateAllWayOut(1);
                    this._showAllWayOutAnim();
                } else {
                    this._updateAllWayOut();
                }

                // 直接显示倒计时等待(跳过 humanboy.RoundState.WAIT_NEXT_ROUND2的倒计时)
                this._showWaittingTime(true);
                break;

            // 清屏等待下一局
            // 处于结束期间并即将开启新的一局的第二阶段，此阶段会清理庄家
            case network.RoundState.WAIT_NEXT_ROUND2:
                this._showNextRoundPrepare();
                break;

            default:
                break;
        }
    }

    /**
     * 下注级别变更
     */
    private _onMsgBetAmountLevelChange(param: any): void {
        this._updateBetAmountLevel();
        this._updateBetButtonState();
    }

    /**
     * 设置高级续投次数成功
     */
    private _onMsgAdvanceAutobetSet(count: number): void {
        this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING);

        // 如果本局没有下注,且已勾选续投局数,则本局就生效一次
        if (!this._humanboyRoom.roundInfo.hasBetInCurRound && this._humanboyRoom.betSettings.canAutoBet) {
            this._checkAdvanceAutoReq();
        }
    }

    /**
     * 高级续投
     */
    private _onMsgAdvanceAutobet(code: number): void {
        switch (code) {
            case network.ErrorCode.OK:
                break;

            // 高级续投超出限红
            case network.ErrorCode.AUTO_BET_EXCEED_LIMIT:
                if (this._humanboyAdvancedAuto) {
                    this._humanboyAdvancedAuto.adaptAdvanceAutoTipsPos(this._btn_betAuto.node);
                    this._humanboyAdvancedAuto.showAdvanceAutoTips(
                        pf.languageManager.getString(pf.StringUtil.formatC('Humanboy_ServerErrorCode%d', code))
                    );
                }
                break;

            // 高级续投金额不足
            case network.ErrorCode.AUTO_BET_NO_MONEY:
                {
                    let strNodeName = 'humanboy_dialog_recharge';
                    let dialogNode: cc.Node = this.node.getChildByName(strNodeName);
                    if (!dialogNode) {
                        dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_DIALOG)
                        );
                        dialogNode.name = strNodeName;
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, eHumanboyLocalZorder.HL_ZORDER_PANEL_SERVER_TOAST);

                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const stringContent = pf.languageManager.getString(
                            pf.StringUtil.formatC('Humanboy_ServerErrorCode%d', code)
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_auto_cancel');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_auto_recharge');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            this._humanboyRoom.cancelAdavnceAutoBet();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            this.recharge();
                        };
                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: false,
                            legacyDialog: legacyDialog,
                            isShowBtnCenter: false,
                            themeType: ThemeSystemType.TwoButton_NoMoney_Style
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);

                        // 如果打开了 JP界面, 则关闭
                        if (this._humanboyJackpot) {
                            // this._humanboyJackpot.setShieldLayerEnabled(false);
                            this._humanboyJackpot.hide(false);
                        }
                    }
                }
                break;
        }

        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.adaptAdvanceAutoCountPos(this._btn_betAuto.node);
            this._humanboyAdvancedAuto.showAdvanceAutoCount();
        }
    }

    /**
     * 取消高级续投成功
     */
    private _onMsgAdvanceAutobetCancel(param: any): void {
        this._updateBetAmountLevel();
        this._updateBetButtonState();
    }

    /**
     * 提示续投已达上限
     */
    private _onMsgAdvanceAutobetLimitReached(param?: any): void {
        const handAdded = param;
        if (handAdded) {
            // cv.TT.showMsg(
            //     pf.StringUtil.formatC(pf.languageManager.getString('MiniGame_btn_desc_auto_bet_reached'), handAdded),
            //     cv.Enum.ToastType.ToastTypeError
            // );
        }
    }

    /**
     * 房间变更通知(目前只针对赔率)
     */
    private _onMsgRoomParamChange(): void {
        this._updateBetOddsDetail();
    }

    /**
     * 新开一局
     */
    private _onMsgGameDeal(): void {
        this._resetGameView();

        // 更新下注按钮和触摸状态
        this._updateBetButtonState();
        this._updateBetAreaTouchEnabled();

        // 更新庄家信息
        this._updateDealerInfo();

        // 更新自己信息
        this._updateSelfInfo();

        // 更新其他人信息
        this._updateOtherPlayersInfo();

        // 更新所有玩家连胜状态
        this._updateAllPlayerWinCount();

        // 开局动画 . 发牌动画 . 开始下注动画
        this._showRoundStartAnim();
    }

    /**
     * 开始下注
     */
    private _onMsgGameStartBet() {
        this._resetAllCards(true);
        this._resetLeftTime();

        // 更新下注倒计时
        this._updateTimeBetClock();

        // 更新区域触摸状态
        this._updateBetButtonState();
        this._updateBetAreaTouchEnabled();

        // 检测是否正在使用高级续投
        this._checkAdvanceAutoReq();
    }

    /**
     *  一局结束
     */
    private _onMsgGameRoundEnd() {
        this._resetLeftTime();
        this._restAllTimelineAnims();

        this._updateBetButtonState(false);
        this._updateBetAreaTouchEnabled();

        this._showRoundEndAnim();

        // 隐藏高级续投选择面板
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.hideSelectPanel(false);
        }
    }

    /**
     * 游戏即将开始
     */
    private _onMsgWillStartNotify() {
        this._resetLeftTime();
        this._showNextRoundPrepare();

        // 请求走势图
        if (this._humanboyChart && this._humanboyChart.node.active) {
            this._humanboyChart.updateReusult();
        }
    }

    /**
     * 显示上庄面板
     * @param anim
     * @param eType
     */
    private _showDealerListView(anim: boolean, eType: eHumanboyDealerListViewType): void {
        if (!this._dealerListView) {
            this._dealerListView = cc.instantiate(this.prefab_hb_dealerList).getComponent(HumanboyDealerListControl);
            this.node.addChild(this._dealerListView.node, eHumanboyLocalZorder.HL_ZORDER_PANEL_DEALERLIST);
        }
        this._dealerListView.show(anim, eType);

        this.getDealerList();
    }

    /**
     * 点击上庄按钮
     */
    private _onClickDealerBtn(event: cc.Event): void {
        this._playSoundEffect(macros.Audio.Button);

        switch (this._btnStatus) {
            case eHumanboyDealerBtnStatus.HDB_STATUS_NONE:
                break;

            case eHumanboyDealerBtnStatus.HDB_STATUS_DEALER_UP:
                this._showDealerListView(true, eHumanboyDealerListViewType.HDLV_TYPE_WATTING);
                break;

            case eHumanboyDealerBtnStatus.HDB_STATUS_DEALER_DOWN:
                this._humanboyRoom.downDealer();
                break;

            default:
                break;
        }
    }

    /**
     * 点击金币区域下注
     * @param nAreaIdx
     */
    private _onClickAreaCoinPanel(nAreaIdx: number): void {
        if (nAreaIdx < 0 || nAreaIdx >= this._vAreasInfo.length) return;

        if (this._humanboyRoom.gameState.roundState === network.RoundState.BET && this._getLeftTime() > 0) {
            // 可以下注
            if (this._nCurBetBtnIndex < 0) {
                this._showGameToast(pf.languageManager.getString('Humanboy_not_select_betbtn'));
                return;
            } else {
                // if (!cv.C2CNotify.isAllowBet()) return;
                this._humanboyRoom.bet(this._vAreasInfo[nAreaIdx].eZone, this._getCurBetLevel());
            }
        } else {
            console.log(
                'HumanboyMainView.betAreaClicked, cannot bet, curState: %d, left bet time: %d',
                this._humanboyRoom.gameState.roundState,
                this._getLeftTime()
            );
            this._showGameToast(pf.languageManager.getString('Humanboy_ServerErrorCode41009'));
        }
    }

    /**
     * 切换指定场景前回调(切出该场景)
     * @param scene
     */
    // private _onMsgSwitchSceneBegan(sceneName: string): void {
    //     console.log(pf.StringUtil.formatC('SwitchScene - From[%s] To [%s]', cv.Enum.SCENE.HUMANBOY_SCENE, sceneName));
    //     this._clearData();
    // }

    /**
     * 下注
     */
    private _onMsgBet(bet: PlayerOneBet): void {
        this._updateAutoBetBtnStatus();

        let nAreaIdx: number = this._getAreaIdxByBetOption(bet.betOption);

        /* 直接当前帧处理
        // 更新区域限红
        this._updateBettAreaLimitAmount(bet.betOption, -bet.betAmount);

        // 更新玩家金币
        this._updatePlayerGold(bet.uid);

        // 金币动画
        this._showCoinAnim(nAreaIdx, bet.betAmount, bet.uid, true, true, true);

        // 自己筹码变化后判断一下下注筹码状态
        if (this._humanboyRoom.selfPlayer.uid === bet.uid) {
            this._updateBetButtonState();
        }
        */

        // 添加到金币队列, 按帧添加
        let tCoinOptimization: HumanboyCoinOptimization = new HumanboyCoinOptimization();
        tCoinOptimization.nAreaIdx = nAreaIdx;
        tCoinOptimization.nGold = bet.betAmount;
        tCoinOptimization.nUid = bet.uid;
        tCoinOptimization.bAnim = true;
        tCoinOptimization.bHeadAnim = true;
        tCoinOptimization.bPlaySound = true;
        this._vCoinOptimizationDeque.push_back(tCoinOptimization);
    }

    /**
     * 请求续投成功
     */
    private _onMsgAutoBet(): void {
        this._updateBetButtonState();
    }

    /**
     * 合并续投动作
     */
    private _onMsgMergeAutoBetAct(bets: PlayerOneBet[]): void {
        // let betSize: number = pf.Util.Number(param);
        // let headAnim: boolean = false;
        // if (++this._nMergeAutoBetNum >= betSize) {
        //     this._nMergeAutoBetNum = 0;
        //     headAnim = true;
        // }
        const betSize = bets.length;

        bets.forEach((tOneBet) => {
            // let tOneBet: PlayerOneBet = humanboyDataMgr.getHumanboyRoom().tCurPlayerOneBet;
            let nAreaIdx: number = this._getAreaIdxByBetOption(tOneBet.betOption);

            // 动态增加金币池(主要用于优化续投大量金币体验)
            do {
                let nFreeCoinCount: number = this._getFreeCoinCountFromPool(nAreaIdx);
                // if(betSize > this._nAreaCoinLimitCountMin)
                if (betSize > nFreeCoinCount) {
                    // let nDiffCount: number = betSize - this._nAreaCoinLimitCountMin;
                    let nDiffCount: number = betSize - nFreeCoinCount;
                    let nFinalCount: number = Math.min(
                        this._nAreaCoinLimitCountMin + nDiffCount,
                        this._nAreaCoinLimitCountMax
                    );
                    this._nAreaCoinLimitCountMin = nFinalCount;
                }
            } while (0);

            /* 直接当前帧处理
            // 更新区域限红
            this._updateBettAreaLimitAmount(tOneBet.betOption, -tOneBet.betAmount);

            // 更新玩家金币
            this._updatePlayerGold(tOneBet.uid);

            // 金币动画
            this._showCoinAnim(nAreaIdx, tOneBet.betAmount, tOneBet.uid, true, headAnim, headAnim);
            */

            // 添加到金币队列, 按帧添加
            let tCoinOptimization: HumanboyCoinOptimization = new HumanboyCoinOptimization();
            tCoinOptimization.nAreaIdx = nAreaIdx;
            tCoinOptimization.nGold = tOneBet.betAmount;
            tCoinOptimization.nUid = tOneBet.uid;
            tCoinOptimization.bAnim = true;
            tCoinOptimization.bHeadAnim = true;
            tCoinOptimization.bPlaySound = true;
            this._vCoinOptimizationDeque.push_back(tCoinOptimization);
        });

        let sound: string = betSize <= 1 ? macros.Audio.Betin : macros.Audio.Betin_Many;
        this._playSoundEffect(sound);
        this._updateBetButtonState();
    }

    /**
     * 合并续投动作结束
     */
    // private _onMsgMergeAutoBetEnd(param: any): void {
    //     let betSize: number = pf.Util.Number(param);
    //     let sound: string = betSize <= 1 ? macros.Audio.Betin : macros.Audio.Betin_Many;
    //     this._playSoundEffect(sound);
    //     this._updateBetButtonState();
    // }

    /**
     * 上庄列表
     */
    private _onMsgDealerList(param: any): void {
        // 刷新上庄视图
        if (!this._dealerListView) return;
        this._dealerListView.updateView();
    }

    private getDealerList(): void {
        this._humanboyRoom.getBuyStockNum();
        this._humanboyRoom.getDealerList();
    }

    /**
     * 上庄申请
     */
    private _onMsgDealerUp(uBuyStockNum: number): void {
        this._showGameToast(pf.languageManager.getString('Humanboy_game_dealer_up'));

        this.getDealerList();
    }

    /**
     * 上庄通知
     */
    private _onMsgDealerUpNotify(uDealerUid: number): void {
        if (uDealerUid === this._authService.currentUser.userId) {
            // 更新自身金币数量
            let llGold: number = this._humanboyRoom.selfPlayer.curCoin;
            this._txt_self_gold.string = this._humanboyRoom.transGoldShortString(llGold);

            this.getDealerList();

            // 更新庄家信息
            this._updateDealerInfo();
        }
    }

    /**
     * 上庄失败被踢
     */
    private _onMsgKickDealerApplyNotify(reson: number): void {
        switch (reson) {
            case network.KickApplyDealerReason.K_NULL:
                break;

            // 提示:余额不足，申请上庄失败
            case network.KickApplyDealerReason.K_NoMoney:
                this._showGameToast(pf.languageManager.getString('Humanboy_kick_applydealer_nomoney'));
                break;

            // 提示:余额不足，补充股份失败
            case network.KickApplyDealerReason.K_SUPPLY:
                this._showGameToast(pf.languageManager.getString('Humanboy_kick_applydealer_supply'));
                break;

            // 断线被踢出上庄申请列表（备用）
            case network.KickApplyDealerReason.K_OFFLINE:
                break;

            // 离开被踢出上庄申请列表（备用）
            case network.KickApplyDealerReason.K_LEAVE:
                break;

            default:
                break;
        }

        this.getDealerList();
    }

    /**
     * 取消等待队列
     */
    private _onMsgDealerCancelWait(param: any): void {
        this._showGameToast(pf.languageManager.getString('Humanboy_game_dealer_cancel_wait'));

        this.getDealerList();
    }

    /**
     * 下庄
     */
    private _onMsgDealerDown(uDoNow: number): void {
        switch (uDoNow) {
            // 当局会下庄
            case 1:
                this._showGameToast(pf.languageManager.getString('Humanboy_game_dealer_down_1'));
                break;

            // 需要下局才下庄
            case 2:
                // 点击我要下庄后, 禁用上庄列表面板中的"我要上庄按钮"
                if (this._dealerListView) {
                    this._dealerListView.setBtnDealerEnable(false);
                }

                this._showGameToast(pf.languageManager.getString('Humanboy_game_dealer_down_2'));
                break;

            default:
                break;
        }
    }

    /**
     * 下庄通知
     */
    private _onMsgDealerDownNotify(noti: network.IDownDealerNotify): void {
        if (noti.uid === this._authService.currentUser.userId) {
            let str_reason = '';
            switch (noti.reason) {
                // 身上钱低于下庄要求
                case network.DownDealerReason.NoMoney:
                    this._showGameToast(pf.languageManager.getString('Humanboy_game_dealer_down_notify_nomoney'));
                    break;

                // 连续座庄次数到达限制
                case network.DownDealerReason.LongTime:
                    {
                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.MINI_GAME_DIALOG)
                        );
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, eHumanboyLocalZorder.HL_ZORDER_PANEL_SERVER_TOAST);

                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const stringContent = pf.languageManager.getString('Humanboy_game_dealer_down_notify_longtime');
                        const stringLeftBtn = pf.languageManager.getString('TipsPanel_cancel_button');
                        const stringRightBtn = pf.languageManager.getString('TipsPanel_sure_button');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {};
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            this._humanboyRoom.upDealer(noti.holdStockNum);
                        };
                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: false,
                            legacyDialog: legacyDialog,
                            isShowBtnCenter: false
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);

                        // 如果打开了 JP界面, 则关闭
                        if (this._humanboyJackpot) {
                            // this._humanboyJackpot.setShieldLayerEnabled(false);
                            this._humanboyJackpot.hide(false);
                        }
                    }
                    break;

                // 主动下庄
                case network.DownDealerReason.Leave:
                    this._showGameToast(pf.languageManager.getString('Humanboy_game_dealer_down_1'));
                    break;

                case network.DownDealerReason.Offline:
                    break; // 离线下庄

                default:
                    break;
            }

            // 更新自身金币数量
            let llGold: number = this._humanboyRoom.selfPlayer.curCoin;
            this._txt_self_gold.string = this._humanboyRoom.transGoldShortString(llGold);

            // 更新庄家信息
            // this._updateDealerInfo();
        }
    }

    /**
     * 更新玩家列表
     */
    private _onMsgPlayerList(gamePlayers: pf.services.GamePlayer[], playerNum: number): void {
        if (!this._humanboyPlayerList) {
            // this._humanboyPlayerList = cc
            //     .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_PLAYER_LIST))
            //     .getComponent(MiniGamePlayerListControl);
            pf.addressableAssetManager
                .loadAsset(macros.Dynamic_Assets.MINI_GAME_PLAYER_LIST)
                .then((asset: cc.Prefab) => {
                    this._humanboyPlayerList = cc.instantiate(asset).getComponent(MiniGamePlayerListControl);
                    this.node.addChild(this._humanboyPlayerList.node, eHumanboyLocalZorder.HL_ZORDER_PANEL_RECORD);
                    this._humanboyPlayerList.setHumanboyData(gamePlayers, playerNum);
                    this._humanboyPlayerList.displayCell(0);
                });
        } else {
            this._humanboyPlayerList.node.active = true;
            this._humanboyPlayerList.setHumanboyData(gamePlayers, playerNum);
            this._humanboyPlayerList.displayCell(-1);
        }
    }

    /**
     * 中奖,荣耀榜等提示
     */
    private _onMsgRewardTips(param: any): void {
        if (!this._humanboyRewardTips) {
            let pos_x: number = (1 - this.node.anchorX) * this.node.width * this.node.scaleX;
            let pos_y: number = (1 - this.node.anchorY) * this.node.height * this.node.scaleY - 122;

            this._humanboyRewardTips = cc
                .instantiate(pf.addressableAssetManager.getAsset<cc.Prefab>(macros.Assets.HUMANBOY_REWARD_TIPS))
                .getComponent(HumanboyRewardTipsControl);
            this.node.addChild(this._humanboyRewardTips.node, eHumanboyLocalZorder.HL_ZORDER_PANEL_REWRAD_TIP);
            this._humanboyRewardTips.node.setPosition(pos_x, pos_y);
        }

        let value: string = pf.Util.String(param);
        this._humanboyRewardTips.show(value, 4);
    }

    /**
     * world服金币有变动通知
     */
    // TODO: world服金币有变动通知
    private _onMsgUpdateWorldServerGold(wallet: Wallet): void {
        // world服接收接口已过滤只发自己, 因此这里无需再次判断(同时没有别的需求, 所以也不用缓存下发的结构)
        let llCurGold: number = wallet.totalAmount;

        // 结算阶段跳过(否则会提前知道输赢结果)
        if (this._humanboyRoom.canUpdateWorldServerGold) {
            // 更新自己金币信息
            this._humanboyRoom.selfPlayer.curCoin = llCurGold;
            this._updatePlayerGold(this._authService.currentUser.userId);

            // 更新其他人信息(因为自己有可能会在8人列表中)
            let bOnMainPlayerList = false;
            let vOtherPlayerInfo: pf.services.GamePlayer[] = this._humanboyRoom.otherPlayers;
            for (const info of vOtherPlayerInfo) {
                if (this._authService.currentUser.userId === info.uid) {
                    bOnMainPlayerList = true;
                    info.curCoin = llCurGold;
                }
            }
            if (bOnMainPlayerList) {
                this._updateOtherPlayersInfo();
            }
        }
    }

    /**
     * 红包节开关通知
     */
    private _onMsgShowLuckButton(mode: number) {
        if (mode === pf.client.RedPacketLotteryMode.Diamond) {
            cc.log('[3in1] no diamond red packet in humanboy');
            return;
        }

        if (!this._luckButton) {
            // this._luckButton = cc.instantiate(this.prefab_luckButton).getComponent(LuckTurntablesButton);
            const luckButtonPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>(
                macros.Assets.LUCK_TURNTABLE_BUTTON
            );
            this._luckButton = cc.instantiate(luckButtonPrefab).getComponent(LuckTurntableButtonControl);
            this._luckButton.setLotteryMode(pf.client.RedPacketLotteryMode.Classical);
            this._btn_redpacket_festival.addChild(this._luckButton.node);
            this._luckButton.node.setPosition(0, 0);
            let pos: cc.Vec2 = cc.Vec2.ZERO;
            this._img_self_gold.node.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);
            this._luckButton.setViewData(pos);
        }

        if (this._luckTurntableService.isShowLuckTurntable(mode)) {
            this._btn_redpacket_festival.active = true;
            this._luckButton.updateView(true, this._btn_redpacket_festival_layer);
        } else {
            this._btn_redpacket_festival.active = false;
        }
        // "红包节"提示层是否显隐
        this._btn_redpacket_festival_layer.active = this._btn_redpacket_festival.active;
        // "红包节"状态有变化, 适配底栏按钮位置
        this._adaptiveBetBtnPanel();
    }

    /**
     * 红包转盘中奖结果通知
     * @param id
     */
    private _onMsgTurntableResultNotice(userId: number, mode: number) {
        if (mode === pf.client.RedPacketLotteryMode.Diamond) {
            cc.log('[3in1] no diamond red packet in humanboy');
            return;
        }

        let nodeList: cc.Node[] = this._getPlayerCoinNodesByUid(userId);
        if (nodeList.length <= 0) {
            nodeList.push(this._btn_playerList);
        }

        for (const node of nodeList) {
            // let node: cc.Node = list[i];
            // let pos: cc.Vec2 = cc.Vec2.ZERO;
            // node.getParent().convertToWorldSpaceAR(node.getPosition(), pos);
            // this._luckButton.showGoldMoveAction(pos, param.currency_type);
            this._luckButton.runGoldMoveAction(this._btn_redpacket_festival, node);
        }
    }

    /**
     * 适配刘海屏相关控件
     */
    private _adaptiveScreen(): void {
        // 左右玩家列表
        // let panel_left_playerlist: cc.Node = this.node.getChildByName('panel_left_playerlist');
        // let panel_right_playerlist: cc.Node = this.node.getChildByName('panel_right_playerlist');

        // 适配相关控件位置
        switch (this._eGameboyScreenType) {
            case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD:
                this._btn_menu.node.getComponent(cc.Widget).top = 68;
                pf.UIUtil.adaptWidget(this._btn_menu.node);
                this._btn_record.node.getComponent(cc.Widget).top = 68;
                pf.UIUtil.adaptWidget(this._btn_record.node);
                // let tmp_y: number = 50;
                // this._btn_menu.node.setPosition(this._btn_menu.node.x, this._btn_menu.node.y - tmp_y);
                // this._btn_record.node.setPosition(this._btn_record.node.x, this._btn_record.node.y - tmp_y);

                // panel_left_playerlist.setPosition(panel_left_playerlist.x, panel_left_playerlist.y - tmp_y / 2);
                // panel_right_playerlist.setPosition(panel_right_playerlist.x, panel_right_playerlist.y - tmp_y / 2);
                break;

            case MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW:
                // let tmp_x: number = HumanboyControl.g_fullScreenOffset.x - 25;
                // panel_left_playerlist.setPosition(panel_left_playerlist.x + tmp_x, panel_left_playerlist.y);
                // panel_right_playerlist.setPosition(panel_right_playerlist.x - tmp_x, panel_right_playerlist.y);
                break;
        }

        // 菜单按钮
        this._btn_menu.node.setPosition(cc.v2(this._panel_left_playerlist.x, this._btn_menu.node.y));

        // 表格记录按钮
        this._btn_record.node.setPosition(cc.v2(this._panel_right_playerlist.x, this._btn_record.node.y));

        // 玩家自己面板
        if (this._panel_self) {
            let img_head: cc.Node = this._panel_self.getChildByName('img_head');
            let pos: cc.Vec2 = cc.Vec2.ZERO;
            this._btn_menu.node.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);
            img_head.parent.convertToNodeSpaceAR(pos, pos);
            this._panel_self.setPosition(this._panel_self.x + pos.x - img_head.x, this._panel_self.y);
        }

        // 玩家列表按钮
        if (this._btn_playerList) {
            let pos: cc.Vec2 = cc.Vec2.ZERO;
            this._btn_record.node.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);
            this._btn_playerList.parent.convertToNodeSpaceAR(pos, pos);
            this._btn_playerList.setPosition(pos.x, this._btn_playerList.y);
        }
        let rebateFloatingButton: cc.Node = this.node.getChildByName('rebate_float_button');
        if (rebateFloatingButton) {
            const space = this._btn_menu.node.width / 2 + rebateFloatingButton.width / 2;
            rebateFloatingButton.setPosition(cc.v2(this._btn_menu.node.x + space, rebateFloatingButton.y));
        }
    }

    /**
     * 适配下注按钮面板布局(横向)
     */
    private _adaptiveBetBtnPanel(): void {
        // 若为空, 则填充按钮数组
        if (this._vBottomBetBtns.length <= 0) {
            // 下注按钮
            for (const button of this._vBetButtons) {
                this._vBottomBetBtns.push(new MiniGameCommonDef.GameNodeScale(button.node, this._fBetBtnSrcScaleRate));
            }

            // 续投按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btn_betAuto.node, this._btn_betAuto.node.scale)
            );

            // 清屏按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btn_betClean.node, this._btn_betClean.node.scale)
            );

            // 红包节按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btn_redpacket_festival, this._btn_redpacket_festival.scale)
            );
        }

        let w: number = this._btn_playerList.x - this._btn_playerList.width / 2;
        w -= this._panel_self.x + this._panel_self.width / 2;
        this._panel_betbtn.setContentSize(cc.size(w, this._panel_betbtn.height));
        this._panel_betbtn.setPosition(this._panel_self.x + this._panel_self.width / 2 + w / 2, this._panel_betbtn.y);

        let iTotal_w = 0; // 所有可见子节点宽度和
        let iSpacing_x = 0; // 子节点之间的间距
        let iChildrenCount = 0; // 可见的子节点个数

        for (const button of this._vBottomBetBtns) {
            let node: cc.Node = button.node;
            let fScale: number = button.scale;
            if (node.active) {
                ++iChildrenCount;
                iTotal_w += node.width * fScale;
            }
        }

        iSpacing_x = (this._panel_betbtn.width - iTotal_w) / (iChildrenCount + 1);

        let iLast_w: number = -this._panel_betbtn.width * 0.5;
        for (const button of this._vBottomBetBtns) {
            let node: cc.Node = button.node;
            let fScale: number = button.scale;
            if (node.active) {
                let x = iLast_w + iSpacing_x + (node.width * fScale) / 2;
                let pos: cc.Vec2 = cc.Vec2.ZERO;
                this._panel_betbtn.convertToWorldSpaceAR(cc.v2(x, 0), pos);
                node.parent.convertToNodeSpaceAR(pos, pos);

                node.setPosition(pos.x, node.y);
                iLast_w = pos.x + (node.width * fScale) / 2;
            }
        }

        // 适配红包节入口节点提示层
        if (this._btn_redpacket_festival_layer) {
            let wpos: cc.Vec2 = cc.Vec2.ZERO;
            this._btn_redpacket_festival.convertToWorldSpaceAR(cc.Vec2.ZERO, wpos);
            this._btn_redpacket_festival_layer.setPosition(
                this._btn_redpacket_festival_layer.parent.convertToNodeSpaceAR(wpos)
            );
        }

        // 适配高级续投提示语位置
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.adaptAdvanceAutoTipsPos(this._btn_betAuto.node);
            this._humanboyAdvancedAuto.adaptAdvanceAutoCountPos(this._btn_betAuto.node);
        }
    }

    playPointAni() {
        let points_num = this._humanboyRoom.roundInfo.changePoints;
        if (points_num < 0) return;

        if (!this.points_node) {
            pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.HEAD_POINTS_ANI).then((asset: cc.Prefab) => {
                this.points_node = cc.instantiate(asset);
                this.node.addChild(this.points_node, eHumanboyLocalZorder.HL_ZORDER_ANIM_NODE_3);
                this.points_node.setPosition(
                    this.node.convertToNodeSpaceAR(
                        this._img_self_head.node.parent.convertToWorldSpaceAR(this._img_self_head.node.position)
                    )
                );
                this.points_node.getComponent(cc.Animation).on(
                    'finished',
                    (event: cc.Event): void => {
                        this.resetPointAni();
                    },
                    this
                );
                this.points_node.getComponent(HeadPointsAniControl).playPointAni(points_num);
            });
        } else this.points_node.getComponent(HeadPointsAniControl).playPointAni(points_num);
    }

    resetPointAni() {
        // this._humanboyRoom.roundInfo.changePoints = 0;
        if (this.points_node) {
            this.points_node.getComponent(HeadPointsAniControl).resetPointAni();
        }
    }

    setLeftAndRightList() {
        // let panel_left_playerlist: cc.Node = this.node.getChildByName('panel_left_playerlist');
        // let panel_right_playerlist: cc.Node = this.node.getChildByName('panel_right_playerlist');
        let headBgWidth = this._panel_left_playerlist.getChildByName('img_bg_0').width;
        let bgPosY = [288, 92, -104, -300, -300];
        let headPosY = 15;
        let coinPosY = -67;
        let left_nb_flag = cc.v2(-4, 330);
        let right_nb_flag = cc.v2(-16, 333);
        let w4 = 22; // 下注面板边缘存在3个间隙
        if (this._eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
            bgPosY = [419, 205, -9, -223, -437];
            left_nb_flag = cc.v2(-4, 461);
            right_nb_flag = cc.v2(-16, 464);
        } else if (this._eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            let baseWidth = cc.winSize.width - 2338;
            w4 = 96 + baseWidth * 0.5;
        } else {
            let baseWidth = cc.winSize.width - 1920;
            w4 = baseWidth > 0 ? 22 + baseWidth * 0.5 : 22;
        }
        this._panel_left_playerlist.getComponent(cc.Widget).left =
            w4 + headBgWidth * 0.5 - this._panel_left_playerlist.width * 0.5;
        // this._panel_left_playerlist.getComponent(cc.Widget).left = 0;
        this._panel_right_playerlist.getComponent(cc.Widget).right =
            w4 + headBgWidth * 0.5 - this._panel_right_playerlist.width * 0.5;
        pf.UIUtil.adaptWidget(this._panel_left_playerlist);
        pf.UIUtil.adaptWidget(this._panel_right_playerlist);

        for (let i = 0; i < 5; ++i) {
            let left_img = this._panel_left_playerlist.getChildByName(pf.StringUtil.formatC('img_bg_%d', i));
            let left_nodeHead = this._panel_left_playerlist.getChildByName(pf.StringUtil.formatC('node_head_%d', i));
            let left_txtCoin = this._panel_left_playerlist.getChildByName(pf.StringUtil.formatC('text_coin_%d', i));

            let right_img = this._panel_right_playerlist.getChildByName(pf.StringUtil.formatC('img_bg_%d', i));
            let right_nodeHead = this._panel_right_playerlist.getChildByName(pf.StringUtil.formatC('node_head_%d', i));
            let right_txtCoin = this._panel_right_playerlist.getChildByName(pf.StringUtil.formatC('text_coin_%d', i));

            left_img.setPosition(cc.v2(0, bgPosY[i]));
            left_nodeHead.setPosition(cc.v2(0, bgPosY[i] + headPosY));
            left_txtCoin.setPosition(cc.v2(0, bgPosY[i] + coinPosY));

            right_img.setPosition(cc.v2(0, bgPosY[i]));
            right_nodeHead.setPosition(cc.v2(0, bgPosY[i] + headPosY));
            right_txtCoin.setPosition(cc.v2(0, bgPosY[i] + coinPosY));

            if (i === 0) {
                let left_imgFlag = this._panel_left_playerlist.getChildByName('nb_flag');
                let right_imgFlag = this._panel_right_playerlist.getChildByName('nb_flag');

                left_imgFlag.setPosition(left_nb_flag);
                right_imgFlag.setPosition(right_nb_flag);
            }
        }
    }

    // How to test switch table
    // How it works: server will create a new room (room 2) if there are already 4 players in current room (room 1)
    // Step 1. 4 players enter room 1
    // Step 2. the player who wants to test switch table chooses to enter room 2
    //         (room selection menu will show after mini game icon clicked)
    // Step 3. 4 players in room 1 leave room 1
    // Step 4. server will kick out the player in room 2 with idle room id pointing to room 1
    showSwitchTable() {
        if (this._bSwitchTable) return;
        this._bSwitchTable = true;

        cr.commonResourceAgent.commonDialog.showMsg(
            pf.languageManager.getString('MiniGame_Switch_Content'),
            [pf.languageManager.getString('MiniGame_Switch_Table'), pf.languageManager.getString('MiniGame_Exit')],
            async () => {
                pf.app.emit('hideWebview');
                const roomId = this._humanboyRoom.roundInfo.idleRoomId;
                const humanboyService = pf.serviceManager.get(domain.HumanboyService);
                await humanboyService.login();
                await this._humanboyRoom.joinRoom(roomId);
                if (this._eAutoBtnStyle === MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING) {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE);
                }
            },
            () => {
                pf.app.emit('hideWebview');
                this._backToRoomListScene();
            }
        );
    }

    // 冷静倒计时
    // onCalmDownShowTip(params: CalmDownParams) {
    //     if (params.calmDownLeftSeconds > 0) {
    //         cr.commonResourceAgent.calmDownDialog.autoShow(params);
    //     }
    // }

    onMsgAdvanceAutobetAdd(usedBetCount: number, autoBetCount: number) {
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoCountPos(this._btn_betAuto.node);
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).showAdvanceAutoCount();
        }
    }

    private _onPushNotification(notification: PushNotification) {
        const curLanguageContent = pf.StringUtil.getServerStrByLanguage(notification.msg);
        let content = '';
        if (notification.sourceType.length === 0) {
            content = curLanguageContent;
        } else {
            for (const src of notification.sourceType) {
                if (src === pf.client.GameId.HumanBoy) {
                    content = curLanguageContent;
                    break;
                }
            }
        }

        if (content.length > 0) {
            this._onMsgRewardTips(content);
        }
    }

    tryLeaveRoom(type: pf.client.ExitType = pf.client.ExitType.Standard) {
        if (type === pf.client.ExitType.NoLeaveRoom) {
            console.log('[3in1] humanboy tryLeaveRoom with NoLeaveRoom');
            this.exitGame();
        } else {
            try {
                this._humanboyRoom.leaveRoom();
            } catch (err) {
                cc.warn(err);
            }
        }
    }

    exitGame() {
        cc.log('[Humanboy] exit game');
        this._clearData();
        pf.bundleManager.exitBundle(macros.BUNDLE_NAME);
    }

    private _onMsgConsumingNotify(msg: network.ILeftGameCoinNotify) {
        if (!this.consumingNotify) {
            const notifyObj = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.CONSUMING_PROMPT));
            this.consumingNotifyHolder.addChild(notifyObj);
            this.consumingNotifyHolder.parent.x = this._panel_self.x - this._panel_self.width / 2;
            this.consumingNotify = notifyObj.getComponent(ConsumingPromptControl);
        }
        this.consumingNotify.show(msg.lost_game_coin, msg.cur_game_coin, 2);
    }

    private _fitSafeArea() {
        const safeArea = pf.system.view.getSafeArea();
        cc.log(
            `[3in1] humanboy safe area x:${safeArea.x}, y:${safeArea.y}, w: ${safeArea.width}, h: ${safeArea.height}`
        );
        if (safeArea.width <= 0 || safeArea.height <= 0) {
            if (cr.CommonUtil.isFitSafeAreaNeeded()) {
                cc.log('[3in1] fit safe area fallback');
                this._panel_left_playerlist.setScale(
                    macros.SAFE_AREA_PLAYER_LIST_SCALE,
                    macros.SAFE_AREA_PLAYER_LIST_SCALE
                );
                this._panel_left_playerlist.setPosition(
                    this._panel_left_playerlist.x + macros.SAFE_AREA_PLAYER_LIST_OFFSET,
                    this._panel_left_playerlist.y
                );
                this._panel_right_playerlist.setScale(
                    macros.SAFE_AREA_PLAYER_LIST_SCALE,
                    macros.SAFE_AREA_PLAYER_LIST_SCALE
                );
                this._panel_right_playerlist.setPosition(
                    this._panel_right_playerlist.x - macros.SAFE_AREA_PLAYER_LIST_OFFSET,
                    this._panel_right_playerlist.y
                );

                this._panel_game.setScale(macros.SAFE_AREA_BOARD_SCALE, macros.SAFE_AREA_BOARD_SCALE);
            }
        } else {
            const safeAreaLeftBorder = safeArea.x - macros.safeAreaSurrenderDistance;
            const leftPlayerlistLeftBorder = this._panel_left_playerlist.getComponent(cc.Widget).left;
            // console.log(
            //     `[3in1] safe area left border:${safeAreaLeftBorder}, left playerlist left border:${leftPlayerlistLeftBorder}`
            // );
            if (safeAreaLeftBorder <= leftPlayerlistLeftBorder + macros.noFitBuffer) {
                cc.log('[3in1] no need to fit safe area');
            } else {
                cc.log('[3in1] need to fit safe area');
                const newLeftBorder = safeAreaLeftBorder - macros.noFitBuffer;
                const canvasNode = this.getComponent(cc.Canvas).node;
                const posWorld = canvasNode.convertToWorldSpaceAR(this._panel_game.position);
                // console.log('[3in1] game panel world pos:', posWorld);
                // console.log('[3in1] winSize:', cc.winSize);
                const newGamePanelLeftBorder = posWorld.x - macros.narrowGamePanelWidth * 0.5;
                const leftPlayerPanelRightBorder =
                    newGamePanelLeftBorder - macros.gapBetweenGamePanelAndPlayerlistPanel;
                const newPlayerlistWidth = leftPlayerPanelRightBorder - newLeftBorder;
                const curPlayerlistWidth = this._panel_left_playerlist.width;
                // console.log(`[3in1] game left:${newGamePanelLeftBorder}, left right:${leftPlayerPanelRightBorder}`);
                // console.log(`[3in1] left panel width cur:${curPlayerlistWidth}, new:${newPlayerlistWidth}`);

                // console.log('[3in1] panel_game w:' + this._panel_game.width);
                const gameBoardRatio = macros.narrowGamePanelWidth / this._panel_game.width;
                // console.log('[3in1] game board ratio:' + gameBoardRatio);
                this._panel_game.setScale(gameBoardRatio, gameBoardRatio);

                const newLeftPlayerlistX = newLeftBorder + newPlayerlistWidth * 0.5;
                const playerlistRatio = newPlayerlistWidth / curPlayerlistWidth;
                if (playerlistRatio < 1) {
                    this._panel_left_playerlist.setScale(playerlistRatio, playerlistRatio);
                }
                let posNew = canvasNode.convertToNodeSpaceAR(cc.v2(newLeftPlayerlistX, 0));
                this._panel_left_playerlist.setPosition(posNew.x, this._panel_left_playerlist.position.y);

                const newGamePanelRightBorder = posWorld.x + macros.narrowGamePanelWidth * 0.5;
                if (playerlistRatio < 1) {
                    this._panel_right_playerlist.setScale(playerlistRatio, playerlistRatio);
                }
                const rightPlayerPanelLeftBorder =
                    newGamePanelRightBorder + macros.gapBetweenGamePanelAndPlayerlistPanel;
                const newRightPlayerlistX = rightPlayerPanelLeftBorder + newPlayerlistWidth * 0.5;
                posNew = this.node.getComponent(cc.Canvas).node.convertToNodeSpaceAR(cc.v2(newRightPlayerlistX, 0));
                this._panel_right_playerlist.setPosition(posNew.x, this._panel_left_playerlist.position.y);
            }
        }
    }

    private _adaptBtnMenu() {
        setTimeout(() => {
            if (this._btn_menu.getComponent(cc.Widget)) {
                this._btn_menu.getComponent(cc.Widget).enabled = false;
            }
            this._btn_menu.node.setPosition(cc.v2(this._panel_left_playerlist.x, this._btn_menu.node.y));
            let rebateFloatingButton: cc.Node = this.node.getChildByName('rebate_float_button');
            if (rebateFloatingButton) {
                const space = this._btn_menu.node.width / 2 + rebateFloatingButton.width / 2;
                rebateFloatingButton.setPosition(cc.v2(this._btn_menu.node.x + space, rebateFloatingButton.y));
            }
        }, 100);
    }

    private hideRebateActivity() {
        this._rebateActivity?.hide();
        this.GetDialogHub().processClose();
    }

    protected GetDialogHub(): DialogHubControl {
        return cr.commonResourceAgent.dialogHub;
    }

    private _onRebateStatusNotify(){
        this.unschedule(this._boundGetRebateEventStatus);
        this.scheduleOnce(this._boundGetRebateEventStatus, 0.5);
    }

    private _getRebateEventStatus() {
        this._rebateService.getEventStatus().then((response) => {
          
            this._rebateEventStatus = cr.RebateUtil.getActiveEventByGame(pf.client.GameId.HumanBoy, response.events);
            this.onMsgRebateActivityActive();
            this._lastTimeGetRebateEventStatus = Date.now();
            this._lastSystemTime = this._rebateEventStatus?.system_time || 0;
        });

        this._rebateService.getLeaderBoard().then((response) => {
            this._rebateService.emit("eventLeaderboardResult", response);
        });
    }

    onMsgRebateActivityActive() {
        if (!cc.isValid(this.node)){
            return;
        }
        const holder = this.node.getChildByName('rebate_float_button');

        if (!this._rebateEventStatus) {
            holder.active = false;
            this.GetDialogHub().processClose();
            return;
        }

        // regenerate new floating button for new event
        if (!this._rebateActivity || this._rebateActivity.eventId !== this._rebateEventStatus.id) {
            holder.destroyAllChildren();
            this._rebateActivity = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.REBATE_FLOATING_BUTTON_NEW)
            ).getComponent(RebateFloatingButtonControl);

            this._rebateCoinsFly = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.REBATE_COINS_FLY)
            );
            holder.addChild(this._rebateActivity.node);
            this.node.addChild(this._rebateCoinsFly);         
            this._rebateActivity.init(this._rebateEventStatus.id, true);
            this._rebateActivity.setup(this.onRebateClicked.bind(this));

            this._rebateActivity.node.on('barMaxAnimationEnd', this._boundBarMaxAnimationEnd);
        }

        holder.active = true;
        this._rebateActivity.play(this._rebateEventStatus);
        this._rebateService.emit("eventStatusResult", this._rebateEventStatus);
    }

    private onRebateClicked() {
        if (!this._rebateEventStatus) {
            return;
        }
        
        const currentTime = Date.now();
        if (currentTime - this._lastTimeClick > 2000) // Throttle the event status request
        {
            this._lastTimeClick = currentTime;
            this._getRebateEventStatus();
        }
   
        const eventType: number = 1;

        this.GetDialogHub()
            .onInit((node) => {
                this._rebateEventStatus.system_time = this._lastSystemTime + Math.floor((Date.now() - this._lastTimeGetRebateEventStatus)/1000);
                this._updateActivityData(node, eventType, this._rebateEventStatus);
            })
            .showPopup({
                popupId: eventType,
                title: cr.RebateUtil.getEventTitle(this._rebateEventStatus),
                content: this.getActivityContent(eventType),
                onAction2: () => {
                    this.GetDialogHub().processClose();
                },
                horizontalAlign: cc.Label.HorizontalAlign.LEFT
            });

    }

    private async onBarMaxAnimationEnd() {
        if (this._rebateCoinsFly && this._selfAvatar && this._rebateActivity) {
            if (this._rebateService && this._rebateService.rebateEvents.length > 0) {
                const event = this._rebateService.rebateEvents[0];
                const idx = event.id;
                const betTime = event.setting.bet_time[0];
                
                let rewardProgressIndex = 0;
                let rewardAmount = 0;
                
                if (betTime && betTime.reward_progress) {
                    for (let i = 0; i < betTime.reward_progress.length; i++) {
                        if (betTime.reward_progress[i].can_get && !betTime.reward_progress[i].got) {
                            rewardProgressIndex = i;
                            rewardAmount = betTime.reward_progress[i].reward;
                            break;
                        }
                    }
                }
                
                await this._rebateCoinsFly.getComponent(RebateCoinsFlyControl).playCoinsFlyAnimation(
                    this._selfAvatar.node, 
                    this._rebateActivity.node, 
                    rewardAmount
                );

                this._rebateClaimToast?.();
                
            }
        }
    }

    _onResponseRebateReceiveReward(data: pf.client.Rebate.IRebateNoticeMessage): void {
        // cv.MessageCenter.send("onClaimedReward");

        const list = Object.entries(data.reward_amount);
        if (list.length === 0) {
            return;
        }

        const msgContent = pf.StringUtil.formatC(
            pf.languageManager.getString('Rebate_claim_reward_success'),
            cr.RebateUtil.getRewardString(data.reward_amount,'#FFDE58')
        );
        
        this._rebateClaimToast = () => {
            this.GetDialogHub().showPopup(
                {
                    popupId: MiniGameDialogPopupId.ToastMsg,
                    content: msgContent
                },
                true
            );
        }
        
    }

    private showRebateRewardPopup(reward_amount: { [k: string]: number }) {
        const msgContent = pf.StringUtil.formatC(
            pf.languageManager.getString('minigame_rebate_reward_popup'),
            cr.RebateUtil.getRewardString(reward_amount, '#FFFF00')
        );

        this.GetDialogHub()
            .onInit((node) => {
                const controller = node.getComponent(RebateRewardPopupControl);
                if (controller) {
                    controller.init();
                    controller._playPopup();
                }
            })
            .showPopup({
                popupId: MiniGameDialogPopupId.RebateRewardPopup,
                content: msgContent,
                sureCallback: () => {
                    // cv.dialogMager.processClose();
                    this.GetDialogHub().processClose();
                }
            });
    }

    OnGameDataSynNotify() {
        this._getRebateEventStatus();
    }

    private getActivityTitle(activityId: number, isDaily: boolean): string {
        switch (activityId) {
            case 1:
                return pf.languageManager.getString('minigame_cowboy_rebate_title');
            case 2:
                return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_2');
            case 3:
                return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_3');
            case 4:
                if (isDaily) {
                    return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_4_daily');
                }
                return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_4');
            default: // need update for more cases later
                return pf.languageManager.getString('minigame_cowboy_rebate_title');
        }
    }

    private getActivityContent(activityId: number): string {
        switch (activityId) {
            case 1:
                return pf.languageManager.getString('minigame_cowboy_rebate_content');
            case 2:
                return pf.languageManager.getString('minigame_cowboy_rebate_content_activity_2');
            case 3:
                return pf.languageManager.getString('minigame_cowboy_rebate_content_activity_3');
            case 4:
                return pf.languageManager.getString('minigame_cowboy_rebate_content_activity_4');
            default: // need update for more cases later
                return pf.languageManager.getString('minigame_cowboy_rebate_content');
        }
    }

    private _updateActivityData(
        activityNode: cc.Node,
        activityId: number,
        data: pf.client.IEventStatusClient
    ): void {
        let tag = activityNode.getComponent(TagControl);
        if (tag === null) {
            tag = activityNode.addComponent(TagControl);
        }
        tag.nIdx = activityId;
        activityNode.getComponent(BettingRebateEventControl).showRebateEvent(data);
        
    }
}

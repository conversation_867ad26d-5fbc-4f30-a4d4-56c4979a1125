{"groups": {"common-resource": {"bundle": "common-resource", "assets": [{"name": "common-dialog", "type": "cc.Prefab", "path": "prefabs/CommonDialog"}, {"name": "avatar", "type": "cc.Prefab", "path": "prefabs/Avatar"}, {"name": "hollow", "type": "cc.Prefab", "path": "prefabs/hollow"}, {"name": "dot", "type": "cc.Prefab", "path": "prefabs/dot"}, {"name": "solid", "type": "cc.Prefab", "path": "prefabs/solid"}, {"name": "shop", "type": "cc.Prefab", "path": "prefabs/Shop"}, {"name": "luck-turntables-button", "type": "cc.Prefab", "path": "prefabs/red-envelope/LuckTurntablesButton"}, {"name": "toast-message", "type": "cc.Prefab", "path": "prefabs/errorMsg"}, {"name": "humanboy-flutter-score", "type": "cc.Prefab", "path": "prefabs/HumanboyFlutterScore"}, {"name": "humanboy-toast", "type": "cc.Prefab", "path": "prefabs/HumanboyToast"}, {"name": "humanboy-reward-tips", "type": "cc.Prefab", "path": "prefabs/HumanboyRewardTips"}, {"name": "win-flag", "type": "cc.Prefab", "path": "prefabs/win_flag"}, {"name": "start-bets", "type": "cc.Prefab", "path": "textures/i18n/zh_CN/first-load-auto-atlas/animation/game_round/start_bets"}, {"name": "end-bets", "type": "cc.Prefab", "path": "textures/i18n/zh_CN/first-load-auto-atlas/animation/game_round/end_bets"}, {"name": "way-out", "type": "cc.Prefab", "path": "animation/way_out/way_out"}, {"name": "poker-card", "type": "cc.Prefab", "path": "prefabs/PokerCard"}, {"name": "special-card-type-atlas", "type": "cc.SpriteAtlas", "path": "textures/i18n/en_US/atlas/cowboy/en_animation"}, {"name": "cowboy-trend-anim-atlas", "type": "cc.SpriteAtlas", "path": "textures/atlas/cowboy_trend_anim"}, {"name": "cowboy-language-atlas", "type": "cc.SpriteAtlas", "path": "textures/i18n/en_US/no-tiny/cowboy/language"}, {"name": "humanboy-game_round_001-sprite", "type": "cc.SpriteFrame", "path": "textures/i18n/zh_CN/first-load-auto-atlas/animation/game_round/001"}, {"name": "humanboy-game_round_002-sprite", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/first-load-auto-atlas/animation/game_round/002"}, {"name": "humanboy-game_round_003-sprite", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/first-load-auto-atlas/animation/game_round/003"}, {"name": "humanboy-game_round_004-sprite", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/first-load-auto-atlas/animation/game_round/004"}, {"name": "humanboy-game_round_005-sprite", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/first-load-auto-atlas/animation/game_round/005"}, {"name": "humanboy-game_round_006-sprite", "type": "cc.SpriteFrame", "path": "textures/i18n/zh_CN/first-load-auto-atlas/animation/game_round/006"}, {"name": "humanboy-language-atlas", "type": "cc.SpriteAtlas", "path": "textures/i18n/en_US/no-tiny/humanboy/language"}, {"name": "humanboy-exchange-atlas", "type": "cc.SpriteAtlas", "path": "textures/i18n/en_US/atlas/humanboy/exchangetexture"}, {"name": "humanboy-atlas", "type": "cc.SpriteAtlas", "path": "textures/i18n/zh_CN/atlas/humanboy/humanboy"}, {"name": "mini-game-menu", "type": "cc.Prefab", "path": "prefabs/MiniGameMenu"}, {"name": "humanboy-advanced-auto", "type": "cc.Prefab", "path": "prefabs/advanced-auto-bet/HumanboyAdvancedAuto"}, {"name": "mini-game-advanced-auto", "type": "cc.Prefab", "path": "prefabs/advanced-auto-bet/MiniGameAdvancedAuto"}, {"name": "mini-game-dialog", "type": "cc.Prefab", "path": "prefabs/advanced-auto-bet/MiniGameDialog"}, {"name": "bet-coin", "type": "cc.Prefab", "path": "prefabs/HumanboyBetCoin"}, {"name": "chart-atlas", "type": "cc.SpriteAtlas", "path": "textures/atlas/chart"}, {"name": "dznz-atlas", "type": "cc.SpriteAtlas", "path": "textures/atlas/game_dznz"}, {"name": "mini-game-guide", "type": "cc.Prefab", "path": "prefabs/MiniGameGuide"}, {"name": "dialog-hub", "type": "cc.Prefab", "path": "prefabs/DialogHub"}, {"name": "consuming-prompt", "type": "cc.Prefab", "path": "prefabs/ConsumingPrompt"}, {"name": "card-back", "type": "cc.SpriteFrame", "path": "textures/card_type_0/Pb_01"}, {"name": "win-player-light", "type": "cc.Prefab", "path": "prefabs/win_player_light"}, {"name": "rebate-floating-button", "type": "cc.Prefab", "path": "prefabs/rebate-promotion/rebate_floating_button"}, {"name": "rebate-floating-button-new", "type": "cc.Prefab", "path": "prefabs/rebate-promotion/rebate_floating_button_new"}, {"name": "rebate-coins-fly", "type": "cc.Prefab", "path": "prefabs/rebate-promotion/rebate_coins_fly"}]}, "common-resource-audio": {"bundle": "common-resource", "assets": [{"name": "button-click", "type": "cc.AudioClip", "path": "audio/buttonSound/button_click"}, {"name": "common-close", "type": "cc.AudioClip", "path": "audio/buttonSound/close"}, {"name": "tab", "type": "cc.AudioClip", "path": "audio/buttonSound/tab"}, {"name": "back-button", "type": "cc.AudioClip", "path": "audio/buttonSound/back_button"}, {"name": "luck-start", "type": "cc.AudioClip", "path": "audio/luck_start"}, {"name": "luck-result", "type": "cc.AudioClip", "path": "audio/luck_result"}, {"name": "chip-fly", "type": "cc.AudioClip", "path": "audio/laba_chipfly"}, {"name": "deal-card", "type": "cc.AudioClip", "path": "audio/dealcard"}, {"name": "bgm", "type": "cc.AudioClip", "path": "audio/back"}, {"name": "begin-bet", "type": "cc.AudioClip", "path": "audio/begin_bet"}, {"name": "end-bet", "type": "cc.AudioClip", "path": "audio/end_bet"}, {"name": "ka<PERSON>ai", "type": "cc.AudioClip", "path": "audio/kaipai"}, {"name": "fapai", "type": "cc.AudioClip", "path": "audio/fapai"}, {"name": "bet", "type": "cc.AudioClip", "path": "audio/chip"}, {"name": "bet-many", "type": "cc.AudioClip", "path": "audio/hechip"}, {"name": "win-lose", "type": "cc.AudioClip", "path": "audio/bx_getCoin"}, {"name": "get-win-coin", "type": "cc.AudioClip", "path": "audio/bigying"}, {"name": "press", "type": "cc.AudioClip", "path": "audio/press"}, {"name": "time-tick", "type": "cc.AudioClip", "path": "audio/time"}, {"name": "special-card-type-big", "type": "cc.AudioClip", "path": "audio/special_card_type_big"}]}, "common-resource-dynamic": {"bundle": "common-resource", "assets": [{"name": "red-envelope-closed", "type": "cc.SpriteFrame", "path": "textures/first-load-auto-atlas/red-envelope/red_btn"}, {"name": "red-envelope-opened", "type": "cc.SpriteFrame", "path": "textures/first-load-auto-atlas/red-envelope/red_btn_open"}, {"name": "iphone-icon", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/luck-turntable/icon_iphone02"}, {"name": "ticket-icon", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/luck-turntable/icon_ticket02"}, {"name": "currency-type0-icon-small", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/icon_gold"}, {"name": "currency-type0-icon-large", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/result-panel/result_coin_0"}, {"name": "currency-type1-icon-small", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/YellowChip"}, {"name": "currency-type1-icon-large", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/result-panel/result_coin_1"}, {"name": "currency-type2-icon-small", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/icon_ustd"}, {"name": "currency-type2-icon-large", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/result-panel/result_coin_2"}, {"name": "currency-type5-icon-small", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/icon_trial"}, {"name": "currency-type5-icon-large", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/result-panel/result_coin_5"}, {"name": "rank-1", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/hb_rank_1"}, {"name": "rank-2", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/hb_rank_2"}, {"name": "rank-3", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/hb_rank_3"}, {"name": "currency-type0-icon-red-envelope", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/icon_0"}, {"name": "currency-type1-icon-red-envelope", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/icon_1"}, {"name": "currency-type2-icon-red-envelope", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/icon_2"}, {"name": "currency-type3-icon-red-envelope", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/icon_3"}, {"name": "currency-type5-icon-red-envelope", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/icon_5"}, {"name": "spin-button-normal", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/second-load-auto-atlas/luck-turntable/spinbutton_normal"}, {"name": "spin-button-disable", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/second-load-auto-atlas/luck-turntable/spinbutton_disable"}, {"name": "red-envelope-text-reward", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/second-load-auto-atlas/luck-turntable/text_reward"}, {"name": "win-result", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/second-load-auto-atlas/luck-turntable/result_tex"}, {"name": "red-envelope-bg", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/second-load-auto-atlas/luck-turntable/hb_bg"}, {"name": "toast-bg-portrait", "type": "cc.SpriteFrame", "path": "textures/first-load-auto-atlas/toast-message/common_tips_bg"}, {"name": "toast-bg-landscape", "type": "cc.SpriteFrame", "path": "textures/first-load-auto-atlas/toast-message/common_tips_bg_1"}, {"name": "rebate-toast", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/rebate-promotion/Toast"}, {"name": "rebate-toast-new", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/rebate-promotion/NewToast"}, {"name": "tips-bg", "type": "cc.SpriteFrame", "path": "textures/second-load-auto-atlas/common/tips_bg"}, {"name": "des-img", "type": "cc.SpriteFrame", "path": "textures/i18n/en_US/first-load-auto-atlas/des_img"}, {"name": "red-envelope", "type": "cc.Prefab", "path": "prefabs/red-envelope/RedEnvelope"}, {"name": "red-envelope-banner", "type": "cc.Prefab", "path": "prefabs/red-envelope/RedEnvelopeBanner"}, {"name": "luck-turntable", "type": "cc.Prefab", "path": "prefabs/red-envelope/LuckTurntables"}, {"name": "luck-turntable-h", "type": "cc.Prefab", "path": "prefabs/red-envelope/LuckTurntablesH"}, {"name": "luck-turntable-squid", "type": "cc.Prefab", "path": "prefabs/red-envelope/LuckTurntables_Squid"}, {"name": "luck-turntable-h-squid", "type": "cc.Prefab", "path": "prefabs/red-envelope/LuckTurntablesH_Squid"}, {"name": "mini-game-exchange", "type": "cc.Prefab", "path": "prefabs/MiniGameExchange"}, {"name": "mini-game-rule", "type": "cc.Prefab", "path": "prefabs/MiniGameRule"}, {"name": "mini-game-audio-setting", "type": "cc.Prefab", "path": "prefabs/MiniGameAudioSetting"}, {"name": "mini-game-advanced-setting", "type": "cc.Prefab", "path": "prefabs/MiniGameAdvancedSetting"}, {"name": "mini-game-exit", "type": "cc.Prefab", "path": "prefabs/MiniGameExit"}, {"name": "head-points-ani", "type": "cc.Prefab", "path": "prefabs/head_points_ani"}, {"name": "mini-game-player-list", "type": "cc.Prefab", "path": "prefabs/mini-game-player-list/MiniGamePlayerList"}]}}}
import * as pf from '../../../../poker-framework/scripts/pf';
import { macros } from '../common/common-resource-macros';
import { GameDialogBaseControl } from './rebate-promotion/GameDialogBaseControl';

const { ccclass, property } = cc._decorator;

export enum MiniGameDialogPopupId {
    RebateActivity1 = 1,
    RebateActivity2 = 2,
    RebateActivity3 = 3,
    RebateActivity4 = 4,
    ToastMsg = 5,
    RebateRewardPopup = 6,
    Dummy = 999
}

export interface IMiniGameDialogPopupParams {
    popupId: number;
    title?: string;
    content?: string;
    cancelCallback?: Function;
    sureCallback?: Function;
    onAction?: Function;
    onAction2?: Function;
    horizontalAlign?: number;
}

@ccclass('MiniGameDialogPopupType')
class MiniGameDialogPopupType {
    @property({ type: cc.Enum(MiniGameDialogPopupId) }) popupType: MiniGameDialogPopupId =
        MiniGameDialogPopupId.RebateActivity1;
    @property(cc.Prefab) prefab: cc.Prefab = null;
}

@ccclass
export class DialogHubControl extends cc.Component {
    // private static instance: DialogManager;

    // public static getInstance(): DialogManager {
    //     if (!this.instance || !this.instance.popUpPrefab) {
    //         DialogManager.instance = new DialogManager();
    //         DialogManager.instance.init();
    //     }
    //     return DialogManager.instance;
    // };

    @property(cc.Node)
    private contentParent: cc.Node | null = null;
    @property(MiniGameDialogPopupType)
    private prefabPopupList: MiniGameDialogPopupType[] = [];

    // protected popUpPrefab: cc.Prefab = null;
    // protected popUpNode: cc.Node = null;
    // protected popUp: DialogManager = null;
    private subPopup: cc.Node = null;
    private _sureCallback: Function = null;
    private _onHide: Function;
    private _onAction: Function;
    private _onClickedOutside: Function;
    private _onInit: (baseNode: cc.Node) => void = null;

    // static preFix: string = 'DialogManager_';

    protected onLoad(): void {
        this.node.zIndex = macros.ZORDER_TT;

        this.node.active = false;
    }

    protected onEnable(): void {
        // this.registerEvent("onPopupClose", this.hide.bind(this), this.node);
    }

    protected onDisable(): void {
        // this.unRegisterEvent("onPopupClose", this.node);
        this._onHide?.();
    }

    // private registerEvent(msg: string, callback: Function, object: cc.Object): void {
    //     cv.MessageCenter.register(DialogManager.preFix + msg, callback, object);
    // }

    // private unRegisterEvent(msg: string, object: cc.Object): void {
    //     cv.MessageCenter.unregister(DialogManager.preFix + msg, object);
    // }

    init(): void {
        // cv.resMgr.load("zh_CN/commonPrefab/DialogManager", cc.Prefab, (prefab: cc.Prefab): void => {
        //     this.popUpPrefab = prefab;
        //     this.createPopUp();
        // }, cv.resMgr.CleanResLevel.LEVEL_BASE);
    }

    showPopup(params: IMiniGameDialogPopupParams, isToast?: boolean): void {
        // const self = this.popUp;
        const { popupId, cancelCallback, sureCallback, onAction, onAction2 } = params;

        let subPopupPrefab = this.getPrefabByPopupId(popupId);
        if (!subPopupPrefab) {
            console.error(`The popup prefab for id : ${popupId} was not defined`);
            return;
        }

        if (isToast) {
            this.showToast(cc.instantiate(subPopupPrefab), params.content);
            return;
        }

        // TODO: not dealt with
        // cv.MessageCenter.send('HideWebview_ShowWindows');
        // cv.MessageCenter.send('hide_bombInfoTips');
        // cv.native.SYwebCloseChildWebview();

        // cv.tools.doFadeIn(self.node, 0.5);
        //pf.Util.doFadeIn(this.node, 0.5);
        this.node.active = true;

        this._onHide = cancelCallback;
        this._sureCallback = sureCallback;
        this._onAction = onAction;
        this._onClickedOutside = onAction2;
        if (this.contentParent.childrenCount > 0) {
            this.contentParent.removeAllChildren();
        }

        if (this.subPopup) {
            this.subPopup.destroy();
        }

        this.subPopup = cc.instantiate(subPopupPrefab);
        this.contentParent.addChild(this.subPopup);
        const dialogControl = this.subPopup.getComponent(GameDialogBaseControl);
        dialogControl?.setCloseCallback(this.processClose.bind(this));
        dialogControl?.showMsg(
            params.content,
            params.title,
            sureCallback,
            cancelCallback,
            onAction,
            params.horizontalAlign
        );
        this._onInit?.(this.subPopup);
    }

    // private createPopUp(): void {
    //     if (this.popUpPrefab && cc.isValid(this.popUpPrefab, true)) {
    //         this.popUpNode = cc.instantiate(this.popUpPrefab);
    //         cc.game.addPersistRootNode(this.popUpNode);
    //         this.popUpNode.zIndex = cv.Enum.ZORDER_TYPE.ZORDER_TT;
    //         this.popUp = this.popUpNode.getComponent(DialogManager);
    //         this.popUpNode.active = false;
    //     }
    //     else {
    //         this.init();
    //     }
    // }

    private showToast(toastNode: cc.Node, msg: string) {
        //this.contentParent.addChild(toastNode);
        cc.director.getScene().addChild(toastNode, cc.macro.MAX_ZINDEX);
        const screenSize = cc.view.getVisibleSize();
       
        toastNode.setPosition(cc.v2(screenSize.width / 2,  screenSize.height / 2));
       
        const content = toastNode.getComponentInChildren(cc.RichText);
        if (content) {
            content.string = msg;
        }
        const duration = 1;
        toastNode.runAction(
            cc.sequence(
                cc.fadeIn(0.5),
                cc.delayTime(duration || 1),
                cc.fadeOut(0.5),
                cc.callFunc(() => {
                    toastNode.destroy();
                })
            )
        );
    }

    onInit(onInit: (baseNode: cc.Node) => void) {
        this._onInit = onInit;
        return this;
    }

    private hide() {
        this.node.active = false;
        if (this.subPopup) {
            this.subPopup.destroy();
            this.subPopup = null;
        }
    }

    processClose() {
        // if (this.popUp) {
        //     this.popUp.hide();
        // }
        this.hide();
    }

    private getPrefabByPopupId(popupId: number): cc.Prefab {
        let item = this.prefabPopupList.find((o) => o.popupType === popupId);
        if (item) {
            return item.prefab;
        } else {
            return null;
        }
    }

    private onClickOutSide(){
        this._onClickedOutside?.();
    }
}

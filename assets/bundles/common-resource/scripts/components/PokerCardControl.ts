/* eslint-disable camelcase */
import * as pf from '../../../../poker-framework/scripts/pf';
import { macros } from '../common/common-resource-macros';

const { ccclass, property } = cc._decorator;

@ccclass
export class PokerCardControl extends cc.Component {
    @property(cc.SpriteAtlas)
    cardAtlas: cc.SpriteAtlas = null;

    @property(cc.SpriteFrame)
    cardBackSpriteFrame: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    graySpriteFrame: cc.SpriteFrame = null;

    @property(cc.Node)
    cardRootNode: cc.Node = null;

    @property(cc.Node)
    cardFaceNode: cc.Node = null;

    @property(cc.Sprite)
    numberSprite: cc.Sprite = null;

    @property(cc.Sprite)
    cardBackSprite: cc.Sprite = null;

    @property(cc.Sprite)
    graySprite: cc.Sprite = null;

    m_eNumber: number = macros.CardNum.CARD_INVALID;
    m_eSuit: number = macros.CardSuit.CardSuit_MAX;

    m_bIsFace: boolean = true;
    m_bIsGray: boolean = true;
    m_bActionDone: boolean = true;
    m_bIsBonny: boolean = false;

    m_kDealPos: cc.Vec2 = new cc.Vec2(0, 0);
    m_f32DealTime: number = 0;
    m_DealRotate: number = 0;

    static readonly CARD_SPECIAL: number = 255;

    init(): void {
        this.cardBackSprite.spriteFrame = this.cardBackSpriteFrame;
        this.graySprite.spriteFrame = this.graySpriteFrame;

        this.Gray(false);
        this.SetFace(false);
        this.UpdateContent();

        this.node.setAnchorPoint(cc.v2(0.5, 0.5));
        this.node.setContentSize(this.numberSprite.node.getContentSize());
    }

    setCardBackSpriteFrame(spriteFrame: cc.SpriteFrame) {
        this.cardBackSpriteFrame = spriteFrame;
        this.cardBackSprite.spriteFrame = spriteFrame;
    }

    ResetFromNode(node: cc.Node): void {
        node.active = false;
        if (!this.node.parent) {
            node.getParent().addChild(this.node);
        }

        this.node.setAnchorPoint(node.getAnchorPoint());
        this.node.setPosition(node.getPosition());
        this.node.angle = node.angle;
        this.node.setScale(node.scale);

        // 关闭裁剪模式(保持精灵原样)
        this.numberSprite.trim = false;
        this.cardBackSprite.trim = false;
        this.graySprite.trim = false;
    }

    SetContent(eNum: number, eSuit: number): void {
        let finalNum = eNum;
        let finalSuit = eSuit;
        if (eNum === PokerCardControl.CARD_SPECIAL) {
            finalNum = macros.CardNum.CARD_2;
        }
        if (eSuit === PokerCardControl.CARD_SPECIAL) {
            finalSuit = macros.CardSuit.CARD_DIAMOND;
        }
        
        this.m_eNumber = finalNum;
        this.m_eSuit = finalSuit;
        this.UpdateContent();
    }

    GetNumber(): number {
        return this.m_eNumber;
    }

    GetSuit(): number {
        return this.m_eSuit;
    }

    UpdateContent() {
        let suitName = '';

        switch (this.m_eSuit) {
            case macros.CardSuit.CARD_SPADE:
                suitName = 'Bhm_';
                break;
            case macros.CardSuit.CARD_HEART:
                suitName = 'Rhm_';
                break;
            case macros.CardSuit.CARD_CLUB:
                suitName = 'Bcm_';
                break;
            case macros.CardSuit.CARD_DIAMOND:
                suitName = 'Rbm_';
                break;
            default:
                break;
        }
        if (CC_PREVIEW) {
            if (suitName.length <= 0) return;
            // 在编辑器模式下使用 cc.assetManager.loadBundle 讀取 bundle
            cc.assetManager.loadBundle('bundles/common-resource', (err, bundle) => {
                if (err) {
                    console.error('加载 bundle 失败:', err);
                    return;
                }

                // 成功加载 bundle 后再加载目標資源
                bundle.load(
                    'textures/card_type_0/' + suitName + String(this.m_eNumber + 1),
                    cc.SpriteFrame,
                    (err, spriteFrame) => {
                        if (err) {
                            console.error('加载资源失败:', err);
                            return;
                        }
                        // 檢查 spriteFrame 是否有值且是 cc.SpriteFrame 類型
                        if (!spriteFrame || !(spriteFrame instanceof cc.SpriteFrame)) {
                            console.error('资源不是 cc.SpriteFrame 类型');
                            return;
                        }

                        // 成功加载资源后进行操作
                        this.numberSprite.spriteFrame = spriteFrame as cc.SpriteFrame;
                    }
                );
            });
        } else {
            if (suitName.length > 0 && this.m_eNumber < macros.CardNum.CARD_INVALID) {
                this.numberSprite.spriteFrame = this.cardAtlas.getSpriteFrame(suitName + String(this.m_eNumber + 1));
            } else {
                cc.warn('[3in1] no suit name or number');
                this.numberSprite.spriteFrame = null;
            }
        }
    }

    SetFace(IsFace: boolean): void {
        this.m_bIsFace = IsFace;
        this.cardFaceNode.active = IsFace;
        this.cardBackSprite.node.active = !IsFace;
        this.cardFaceNode.stopAllActions();
        this.cardBackSprite.node.stopAllActions();
        this.unscheduleAllCallbacks();
        this.cardBackSprite.node.setScale(1, 1);
        this.cardFaceNode.setScale(1, 1);
        // cocos2d::OrbitCamera* orbitFront = cocos2d::OrbitCamera::create(0.0f,1,0,0,0,0,0);
        // this.m_pkCardFace.runAction(orbitFront);
        // cocos2d::OrbitCamera* orbitFront2 = cocos2d::OrbitCamera::create(0.0f,1,0,0,0,0,0);
        // this.m_pkCardBack.runAction(orbitFront2);
        this.m_bActionDone = true;
    }

    Turn(IsFace: boolean, f32Delay?: number): void {
        const delay = f32Delay !== undefined ? f32Delay : 0.0;
        this.unscheduleAllCallbacks();
        this.scheduleOnce(() => {
            this.RealTurn(IsFace);
        }, delay);
    }

    RealTurn(IsFace: boolean) {
        if (!cc.isValid(this, true) || !cc.isValid(this.cardFaceNode, true)) return;
        if (IsFace === this.m_bIsFace) return;

        this.SetFace(!IsFace);

        const orbitFront = cc.scaleTo(0.3 * 0.5, 1, 1);
        const orbitBack = cc.scaleTo(0.3 * 0.5, 0, 1);
        if (IsFace) {
            this.m_bActionDone = false;

            this.m_bIsFace = false;
            this.cardBackSprite.node.active = true;
            this.cardFaceNode.active = false;
            this.cardBackSprite.node.setScale(1, 1);
            this.cardBackSprite.node.runAction(
                cc.sequence(
                    orbitBack,
                    cc.callFunc(() => {
                        this.cardBackSprite.node.active = false;
                        this.cardBackSprite.node.setScale(1, 1);
                        this.cardFaceNode.active = true;
                        this.cardFaceNode.setScale(0, 1);
                        this.cardFaceNode.runAction(
                            cc.sequence(
                                orbitFront,
                                cc.callFunc(() => {
                                    this.m_bActionDone = true;
                                    this.m_bIsFace = true;
                                })
                            )
                        );
                    })
                )
            );
        } else {
            this.m_bActionDone = false;
            this.m_bIsFace = false;
            this.cardBackSprite.node.active = false;
            this.cardFaceNode.active = true;
            this.cardFaceNode.setScale(1, 1);
            this.cardFaceNode.runAction(
                cc.sequence(
                    orbitBack,
                    cc.callFunc(() => {
                        this.cardFaceNode.active = false;
                        this.cardFaceNode.setScale(1, 1);
                        this.cardBackSprite.node.active = true;
                        this.cardBackSprite.node.setScale(0, 1);
                        this.cardBackSprite.node.runAction(
                            cc.sequence(
                                orbitFront,
                                cc.callFunc(() => {
                                    this.m_bActionDone = true;
                                    this.m_bIsFace = true;
                                })
                            )
                        );
                    })
                )
            );
        }
    }

    Gray(IsGray: boolean): void {
        if (this.m_bIsGray !== IsGray) {
            this.m_bIsGray = IsGray;
            this.graySprite.node.active = this.m_bIsGray;
        }
    }

    SetDealPos(kPos: cc.Vec2) {
        // 	m_kDealPos = convertToNodeSpaceAR(kPos);
        //
        //     AffineTransform kTransform = getNodeToWorldAffineTransform();
        //     m_kDealPos.x = kPos.x - kTransform.tx;
        //     m_kDealPos.y = kPos.y - kTransform.ty;

        this.m_kDealPos = this.node.convertToNodeSpaceAR(kPos);
    }

    ResetPos(): void {
        this.cardRootNode.setPosition(0, 0);
        this.cardRootNode.angle = 0;
    }

    SetDealRotate(rotate: number): void {
        this.m_DealRotate = rotate;
        this.m_bActionDone = true;
    }

    Deal(f32Delay: number): void {
        this.node.active = false;
        this.scheduleOnce(this.OnDeal, f32Delay);
    }

    OnDeal(f32Delta: number): void {
        this.m_f32DealTime = 0.0;
        this.cardRootNode.setPosition(this.m_kDealPos);
        this.cardBackSprite.node.opacity = 0;
        this.cardRootNode.angle = 0;
        this.node.active = true;
        this.schedule(this.UpdateDeal, 0.0);
        pf.audioManager.playSoundEffect(macros.Audio.DEAL_CARD);
        // if (cv.tools.isSoundEffectOpen()) {
        //     cv.AudioMgr.playEffect('zh_CN/game/cowboy/audio/dealcard');
        // }
    }

    UpdateDeal(f32Delta: number): void {
        this.m_f32DealTime += f32Delta * 4.0;
        if (this.m_f32DealTime >= 1.0) {
            this.m_f32DealTime = 1.0;
            this.unschedule(this.UpdateDeal);
            this.cardRootNode.setPosition(0, 0);
            if (!this.m_bIsBonny) {
                this.cardBackSprite.node.opacity = 255;
            } else {
                this.cardBackSprite.node.opacity = 100;
            }
            this.cardRootNode.angle = -this.m_DealRotate;
        } else {
            const f32Alpha: number = Math.pow(Math.min(this.m_f32DealTime * 4.0, 1.0), 2.0);
            if (!this.m_bIsBonny) {
                this.cardBackSprite.node.opacity = f32Alpha * 255;
            } else {
                this.cardBackSprite.node.opacity = f32Alpha * 100;
            }
            const f32RatePos: number = Math.cos(this.m_f32DealTime * Math.PI * 0.5);
            this.cardRootNode.setPosition(this.m_kDealPos.x * f32RatePos, this.m_kDealPos.y * f32RatePos);
            this.cardRootNode.angle = f32RatePos * 360;
        }
    }

    IsBonny(): boolean {
        return this.m_bIsBonny;
    }

    IsFace(): boolean {
        return this.m_bIsFace;
    }

    // updateCardBack(path: string): void {
    //     cv.resMgr.setSpriteFrame(this.m_pkCardBack.node, path);
    // }

    getRoot(): cc.Node {
        return this.cardRootNode;
    }

    getCardBack(): cc.Sprite {
        return this.cardBackSprite;
    }

    showCard(isView: boolean): void {
        this.cardBackSprite.node.active = !isView;
        this.cardFaceNode.active = isView;
    }
}

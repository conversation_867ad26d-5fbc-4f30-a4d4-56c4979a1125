// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

@ccclass

export default class Particle3DControl extends cc.Component {

    @property(cc.ParticleSystem3D)
    nodeList: cc.ParticleSystem3D[] = [null];
    
    @property(cc.Float)
    DelaySec = 0;

    playParticles3D() {
        this.unscheduleAllCallbacks();
        this.scheduleOnce(function () {
            for (let i = 0; i < this.nodeList.length; i++) {
                this.nodeList[i].stop();
                this.nodeList[i].play();
            };
        }, this.DelaySec);
    }

    stopParticles3D() {
        this.unscheduleAllCallbacks();
        this.scheduleOnce(function () {
            for (let i = 0; i < this.nodeList.length; i++) {
                //this.nodeList[i].clear();
                this.nodeList[i].stop();
            };
        }, this.DelaySec);
    }
}

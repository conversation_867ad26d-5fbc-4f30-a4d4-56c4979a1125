// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html
/* eslint-disable */
const { ccclass, property} = cc._decorator;

@ccclass
export default class PlayAnimControl extends cc.Component {
    
    @property({ displayName: "Show Animtion List", tooltip: "Show the Animator's Node too.\r----------\r(Becareful, the Animator MAY NOT be the parent UI Node.)"})
    ShowNodeList = true;

    @property({ displayName: "Hide Animtion List onFinished", tooltip: "Hide the Animator's Node too when Finished animations.\r----------\r(Becareful, the Animator MAY NOT be the parent UI Node.)" })
    HideNodeListOnFinished = true;
    
    @property({ tooltip: "Play animation clip while another is currently playing.\r----------\rUseful for Loop animations" })
    PlayAdditive = false;
    
    @property({ type: cc.Animation, displayName: "Animtion List", tooltip: "Animations to animate."})
    nodeList: cc.Animation[] = [null];

    @property({ tooltip: "Animation Clip Name to Play.\r----------\rIf blank, it will play Clip 0."})
    PlayClipName = ""; //Empty means play clips 0 for lazy people

    @property({ type: cc.Float, min: 0, tooltip: "Set the time to JUMP TO and play." })
    PlayheadStartTime = 0;

    @property({type: cc.Float, min: 0, tooltip: "Delay before starting Animation."})
    delay = 0
    
    @property({ type: cc.Button, tooltip: "Run a Button after animation finished.\r----------\r(When ANY animation finished first)"})
    EmitButtonAfterFinished: cc.Button = null;

    /*
    @property(cc.Boolean)
    ShowNodeList = true;

    @property(cc.Animation)
    nodeList: cc.Animation[] = [null];

    @property(cc.String)
    PlayClipName = ""; //Empty means play clips 0

    @property(cc.Float)
    delay = 0;

    @property(cc.Boolean)
    PlayAdditive = false;

    @property(cc.Button)
    EmitButtonAfterFinished: cc.Button = null;

    @property(cc.Boolean)
    PlayReverse = false;
    */

    _onFinished() {
        console.log("_onFinished triggered");

        if (this.HideNodeListOnFinished == true) {
            for (let i = 0; i < this.nodeList.length; i++) {
                this.nodeList[i].node.active = false;
                console.log('HideNodeListOnFinished: ' + this.nodeList[i].node.name);
            } 
        }

        if (this.EmitButtonAfterFinished != null) {
            for (let i = 0; i < this.EmitButtonAfterFinished.clickEvents.length; i++) {
                this.EmitButtonAfterFinished.clickEvents[i].emit([]);
            };
            console.log('EmitButtonAfterFinished: ' + this.EmitButtonAfterFinished.name);
        }

        //unregister all callbacks
        console.log('UN---REGISTERRED _onFinished!!!!!!!!!!!!!');
        for (let i = 0; i < this.nodeList.length; i++) {
            this.nodeList[i].off(cc.Animation.EventType.FINISHED, this._onFinished, this);
        }
    }

    playAnimation() {

        this.scheduleOnce(function () {
            //register _onFinish animations for HideNodeListOnFinished and EmitButtonAfterFinished
            if (this.HideNodeListOnFinished == true || this.EmitButtonAfterFinished != null) {
                for (let i = 0; i < this.nodeList.length; i++) {
                    //if (this.nodeList[i] != null) this.nodeList[i].on(cc.Animation.EventType.FINISHED, this._onFinished, this)
                    if (this.nodeList[i] != null) this.nodeList[i].once(cc.Animation.EventType.FINISHED, this._onFinished, this)
                    console.log('REGISTERRED _onFinished: ' + this.nodeList[i].name);
                };
            };

            for (let i = 0; i < this.nodeList.length; i++) {
                if (!this.nodeList[i]) return;

                //Show nodeList
                if (this.ShowNodeList == true) {
                    this.nodeList[i].node.active = true;
                };

                if (this.PlayClipName == "") { //No name specified, play clip 0 for lazy people
                    const clips = this.nodeList[i].getClips();
                    //const clipsLength = clips.length;

                    this.nodeList[i].play(clips[0].name, this.PlayheadStartTime);
                    console.log("No ClipNames specified, playing Clip 0: " + clips[0].name);

                } else {
                    if (this.PlayAdditive == true) {
                        this.nodeList[i].playAdditive(this.PlayClipName, this.PlayheadStartTime);
                        console.log('Play Additive Anim Name: ' + this.PlayClipName + ".anim from " + this.nodeList[i].node.name);
                    } else {
                        this.nodeList[i].play(this.PlayClipName, this.PlayheadStartTime);
                        console.log('Play Anim Name: ' + this.PlayClipName + ".anim from " + this.nodeList[i].node.name);
                    }

                }

            };
        }, this.delay);

    }
}

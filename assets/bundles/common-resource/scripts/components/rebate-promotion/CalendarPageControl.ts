import { RewardRebateItem } from './RewardRebateItem';

const { ccclass, property } = cc._decorator;

@ccclass
export class CalendarPageControl extends cc.Component {

    @property(cc.Prefab) itemPrefab: cc.Prefab = null;
    @property maxCount: number = 9;
    @property(cc.Node) progressLinesContainer: cc.Node = null;
    @property(cc.Node) rewardContainer: cc.Node = null;

    public items: RewardRebateItem[] = [];

    private currentIndex: number = 0;
    private layout: cc.Layout = null;
    private itemWidth: number = 0;
    private spacing: number = 0;

    public init(itemCount: number): void {
        this.itemWidth = this.itemPrefab.data.width;

        // First create all reward items
        for (let i = 0; i < itemCount; i++) {
            const item = cc.instantiate(this.itemPrefab);
            item.parent = this.rewardContainer;
            this.items.push(item.getComponent(RewardRebateItem));
        }

        this.layout = this.rewardContainer.getComponent(cc.Layout);

        // Wait for next frame to ensure Layout has updated
        this.scheduleOnce(() => {
            this.updateProgressLines();
        }, 0);
    }

    private updateProgressLines(): void {        
        this.items.forEach((rewardItem, index) => {
            if (rewardItem.progressLine) {
                const progressNode = rewardItem.progressLine.node;
                const worldPos = progressNode.convertToWorldSpaceAR(cc.Vec3.ZERO);
                progressNode.parent = this.progressLinesContainer;
                const localPos = this.progressLinesContainer.convertToNodeSpaceAR(worldPos);
                progressNode.position = localPos;
                progressNode.anchorX = 0;
                
                progressNode.setSiblingIndex(this.progressLinesContainer.childrenCount - 1);
                const nextItem = this.items[index + 1];
                if (nextItem) {
                    const nextWorldPos = nextItem.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
                    const nextLocalPos = this.progressLinesContainer.convertToNodeSpaceAR(nextWorldPos);
                    progressNode.width = nextLocalPos.x - localPos.x;
                }
            }

            if (rewardItem.lineGrey) {
                const greyNode = rewardItem.lineGrey.node.parent;
                const worldPos = greyNode.convertToWorldSpaceAR(cc.Vec3.ZERO);
                greyNode.parent = this.progressLinesContainer;
                const localPos = this.progressLinesContainer.convertToNodeSpaceAR(worldPos);
                greyNode.position = localPos;
                greyNode.setSiblingIndex(0);
            }
        });
    }

    updateLayout(activeItemCount: number, containerWidth: number): void {
        if (!this.layout || activeItemCount <= 1) return;
        
        // Calculate spacing based on container width and number of items
        const totalSpace = containerWidth - (this.itemWidth * activeItemCount);
        this.spacing = Math.floor(totalSpace / (activeItemCount - 1));
        
        this.layout.spacingX = this.spacing;
        this.layout.updateLayout();

        // Update progress lines after layout changes
        this.scheduleOnce(() => {
            this.updateProgressLines();
        }, 0);
    }
    
    dequeue(): RewardRebateItem {
        let item = this.items[this.currentIndex];
        this.currentIndex++;
        return item;
    }

    isEmpty(): boolean {
        return this.currentIndex >= this.items.length;
    }

    reset(): void {
        this.currentIndex = 0;
        this.items.forEach((item) => {
            item.node.active = false;
        });
    }
}

/* eslint-disable */
import * as pf from '../../../../../poker-framework/scripts/pf';
import { GameDialogBaseControl } from "./GameDialogBaseControl";
import { BettingBonusControl } from "./BettingBonusControl";
import { CountdownControl } from "./CountdownControl";
import { BettingLeaderboardControlV2 } from './BettingLeaderboardControlV2';
import RebateInfoControl from './RebateInfoControl';
import { GameModeTabsControl } from './GameModeTabsControl';

const {ccclass, property} = cc._decorator;

const TITLE_EN_FONT_SIZE = 50;
@ccclass
export default class BettingRebateEventControl extends GameDialogBaseControl {
    @property(cc.Prefab) protected countDownPrefab: cc.Prefab = null;
    @property(cc.Node) protected countDowntHolder: cc.Node = null;
    @property(BettingBonusControl) private bettingBonus: BettingBonusControl = null;
    @property(BettingLeaderboardControlV2) private bettingLeaderboard: BettingLeaderboardControlV2 = null;
    @property(cc.ToggleContainer) private tabContainer: cc.ToggleContainer = null;
    @property(RebateInfoControl) private rebateInfoControl: RebateInfoControl = null;
    @property(GameModeTabsControl) private gameModeTabs: GameModeTabsControl = null;
    @property(cc.Node) private titleContainer: cc.Node = null;
    
    private _currentEventData: pf.client.IEventStatusClient = null;
    private _betTimeData: pf.client.Rebate.IBetTime = null;
    private _currentLeaderboardRsp: pf.client.Rebate.IGetLeaderboardResponse = null;
    protected _countdown: CountdownControl = null;
    private _hasStarted: boolean = false;
    private _hasEnded: boolean = false;
    private _authService: pf.services.AuthService = null;
   
    private _originalBetBonusTabPos: cc.Vec3 = null;
    private _originalLeaderboardTabPos: cc.Vec3 = null;
    private _betBonusTab: cc.Node = null;
    private _leaderboardTab: cc.Node = null;
    private _selectedTab: number = 0;

    private _boundEventStatusResult = this._onGetEventData.bind(this);
    private _boundLeaderboardResult = this._onGetLeaderboardData.bind(this);
    private _boundEventEnded = this._onEventEnded.bind(this);
    private _boundEventStarted = this._onEventStarted.bind(this);

    private _lastTimeRequest: number = 0;
    private _md5Leaderboard: string = null;

    onLoad(): void {
        super.onLoad();
        this.tabContainer.toggleItems.forEach((toggle, index) => {
            toggle.node.on('toggle', () => {
                this.changeTabBaseOnTabType(index);
            }, this);
        });
        this._authService = pf.serviceManager.get(pf.services.AuthService);

        this._betBonusTab = this.tabContainer.node.getChildByName('Tab_BetBonus');
        this._leaderboardTab = this.tabContainer.node.getChildByName('Tab_Leaderboard');

        if (this._betBonusTab) {
            this._originalBetBonusTabPos = this._betBonusTab.position.clone();
        }
        if (this._leaderboardTab) {
            this._originalLeaderboardTabPos = this._leaderboardTab.position.clone();
        }
        if (this.titleLabel && pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.en_US) {
            this.titleLabel.fontSize = TITLE_EN_FONT_SIZE;
        }
        this._initCountdown();
        this._lastTimeRequest = Date.now();
    }

    private changeTabBaseOnTabType(index: number) {
        this._selectedTab = index;
        if (index ===0){
            this.bettingBonus.node.active = true;
            this.bettingLeaderboard.node.active = false;
            this._showBettingBonus(this._currentEventData);
        }
        else{
            this.bettingBonus.node.active = false;
            this.bettingLeaderboard.node.active = true;
            this._fetchLeaderboardData();
        }
        this._showCooldownTitle();
    }

    protected onEnable(): void {
        this.rebateService.addListener("eventStatusResult", this._boundEventStatusResult);
        this.rebateService.addListener("eventLeaderboardResult", this._boundLeaderboardResult);
    }

    protected onDisable(): void {
        this.rebateService.removeListener("eventStatusResult", this._boundEventStatusResult);
        this.rebateService.removeListener("eventLeaderboardResult", this._boundLeaderboardResult);
    }

    private _initCountdown(){
        const countObj = cc.instantiate(this.countDownPrefab);
        this._countdown = countObj.getComponent(CountdownControl);
        countObj.setParent(this.countDowntHolder);
        countObj.setPosition(0, 0);
        this.countDowntHolder.getComponent(cc.Label).string = "";
        this._countdown.setTitle('');
        this._countdown.stopCountdown();
        this._countdown.node.active = true;
    }

    private _onGetEventData(data: pf.client.IEventStatusClient) {
        this._currentEventData = data;
        this._betTimeData = data.setting.bet_time[0];
        
        this.updateCountDown(data);
       
        this._showCooldownTitle();
        this._showBettingBonus(this._currentEventData);
    }

    private _onGetLeaderboardData(data: pf.client.Rebate.IGetLeaderboardResponse) {
        this._currentLeaderboardRsp = data;
        if (this._selectedTab === 1){
            this._showBettingLeaderboard(data);
        }
    }

    public showRebateEvent(data: pf.client.IEventStatusClient): void {
        
        this._currentEventData = data;
        this._betTimeData = data.setting.bet_time[0];

        if (this.gameModeTabs && data.game_ids) {
            this.gameModeTabs.updateGameModesByIds(data.game_ids);
        }
        
        // Check bet bonus availability
        if (this._betBonusTab) {
            this._betBonusTab.active = true;
            const toggle = this._betBonusTab.getComponent(cc.Toggle);
            if (toggle) {
                toggle.interactable = true;
            }
        }

        // Then check leaderboard
        const hasLeaderboard = !!this.rebateService.rebateLeaderboards?.[data.id];

        // Enable/disable leaderboard tab based on data availability
        if (this._leaderboardTab) {
            this._leaderboardTab.active = hasLeaderboard;
            const toggle = this._leaderboardTab.getComponent(cc.Toggle);
            if (toggle) {
                toggle.interactable = hasLeaderboard;
                if (!hasLeaderboard) {
                    toggle.isChecked = false;
                }
            }
        }

        this._selectedTab = 0;
        
        this.tabContainer.node.active = hasLeaderboard;
        if (this._betBonusTab && this._leaderboardTab) {
            this._betBonusTab.position = this._originalBetBonusTabPos;
            this._leaderboardTab.position = this._originalLeaderboardTabPos;
        }
      
        this.updateCountDown(data);

        this.changeTabBaseOnTabType(this._selectedTab);

    }

    private _showBettingBonus(data: pf.client.IEventStatusClient): void {
        this.bettingBonus.updateData(data);
    }

    private _showBettingLeaderboard(data: pf.client.Rebate.IGetLeaderboardResponse): void {
        if (!this._currentEventData) {  
            return;
        }
        const myData = {
            myId: this._authService.currentUser.userId,
            betAmount: this._currentEventData.setting.bet_time[0].betting_amount
        }
        if (data){
            const myLeaderboard = data.leaderboards[this._currentEventData.id];
            const newSerializeData = pf.Crypto.md5(JSON.stringify(myLeaderboard));
            if (this._md5Leaderboard !== newSerializeData){
                this._md5Leaderboard = newSerializeData;
                this.bettingLeaderboard.showleaderboard(myData, myLeaderboard.infos);
            }
        }
        
    }
    
    private _showCooldownTitle() {
        let title = '';
        if (!this._hasStarted){
             title = pf.StringUtil.formatC(
                pf.languageManager.getString('Rebate_start_in'), 
                pf.StringUtil.formatTime(this._betTimeData.event_start_time, pf.eTimeType.Year_Month_Day_Hour_Min, false, false)
            );
        }
        else if (this._hasEnded){
            if (this._selectedTab === 0){
                title = pf.languageManager.getString('Rebate_has_end_bet_bonus');
            }
            else{
                title = pf.StringUtil.formatC(
                    pf.languageManager.getString('Rebate_has_end_leaderboard'), 
                    pf.StringUtil.formatTime(this._betTimeData.event_end_time, pf.eTimeType.Year_Month_Day_Hour_Min, false, false)
                );
            }
        }
        else{
            if (this._selectedTab === 0){
                title = pf.languageManager.getString('Rebate_bet_bnous_countdown');
            }
            else{
                title = pf.languageManager.getString('Rebate_leaderboard_countdown');
            }
        }
        this._countdown?.setTitle(title);
    }

    protected updateCountDown(data: pf.client.IEventStatusClient) {
        const betTime = data.setting.bet_time[0];
        this._hasStarted = betTime.event_start_time <= data.system_time;
        this._hasEnded = betTime.event_end_time < data.system_time;

        if (!this._hasStarted){
            this._countdown.startCountdown(betTime.event_start_time - data.system_time, this._boundEventStarted, false);
        }
        else if (this._hasEnded){
            this._countdown.stopCountdown();
        }
        else { //this._hasStarted && !this._hasEnded
            this._countdown.startCountdown(betTime.event_end_time - data.system_time, this._boundEventEnded);
        }
    }

    private _fetchLeaderboardData(): void {
        const currentTime = Date.now();
        if (currentTime - this._lastTimeRequest < 2000) {
            this._showBettingLeaderboard(this._currentLeaderboardRsp);
            return;
        }
        this._lastTimeRequest = currentTime;

        this.rebateService.getLeaderBoard().then((response) => {
            this._onGetLeaderboardData(response);
        });
    }

    _onEventEnded() {
        this._hasEnded = true;
        this._showCooldownTitle();
        this._countdown.stopCountdown();
    }

    _onEventStarted() {
        this._hasEnded = false;
        this._hasStarted = true;
        this._countdown.startCountdown(this._betTimeData.event_end_time - this._betTimeData.event_start_time, this._boundEventEnded);

        this._showCooldownTitle();
    }

    private createMockDataForTest(): void {
        const leaderboardInfos: pf.client.Rebate.ILeaderboardInfo[] = [
            {player_id: 1, name: 'Player1', bet_amount: 10000, reward: 3000, head: "/1", plat: 0, rank:1, currency_type:2},
            {player_id: this._authService.currentUser.userId, name: 'Player2', bet_amount: 12000, reward: 2000, head: "/2", plat: 0, rank:2, currency_type:2},
            {player_id: 3, name: 'Player3', bet_amount: 3000, reward: 1300, head: "/3", plat: 0, rank:3, currency_type:2},
            {player_id: 4, name: 'Player4', bet_amount: 1000, reward: 400, head: "/4", plat: 0, rank:4, currency_type:2},
            {player_id: 5, name: 'Player5', bet_amount: 1000, reward: 400, head: "/5", plat: 0, rank:5, currency_type:2},
        ];
        this.bettingLeaderboard.showleaderboard({myId: this._authService.currentUser.userId, betAmount: 0}, leaderboardInfos);
    }

    protected onClickShowGuideInfo() {
        this.rebateInfoControl.setGuideInfo(this._currentEventData, this.titleContainer.width);
        this.rebateInfoControl.showGuideInfo();
        
    }
}

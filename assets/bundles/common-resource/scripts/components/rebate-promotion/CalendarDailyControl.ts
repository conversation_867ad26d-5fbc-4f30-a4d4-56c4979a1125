import * as pf from '../../../../../poker-framework/scripts/pf';
import { CalendarPageControl } from './CalendarPageControl';
import type { ICalendarItemParam } from '../../common/rebate-defines';
import { RebateUtil } from '../../common-resource';
import { RewardRebateItem } from './RewardRebateItem';
import { Rebate } from '../../../../../poker-framework/scripts/poker-client/poker-socket';

const { ccclass, property } = cc._decorator;

@ccclass
export class CalendarDailyControl extends cc.Component {
    @property(cc.PageView) scrollView: cc.PageView = null;
    @property(cc.Button) btLeft: cc.Button = null;
    @property(cc.Button) btRight: cc.Button = null;
    @property(cc.Node) content: cc.Node = null;
    @property(cc.Prefab) pagePrefab: cc.Prefab = null;

    private numItemPerPage: number = 4;
    _onSelected: (idx: number) => void;
    private pages: CalendarPageControl[] = [];

    private _items: RewardRebateItem[] = [];

    protected onLoad(): void {
        this._initUI();
    }

    private _initUI(): void {
        // cv.resMgr.adaptWidget(this.node);
        pf.UIUtil.adaptWidget(this.node);
    }

    protected start(): void {
        this.btLeft.node.on('click', this.onLeft, this);
        this.btRight.node.on('click', this.onRight, this);
        this.btLeft.interactable = false;
        this.scrollView.node.on('page-turning', this.onPageTurning, this);
    }

    onLeft = () => {
        this.scrollView.scrollToPage(this.scrollView.getCurrentPageIndex() - 1, 0.5);
    };

    onRight = () => {
        this.scrollView.scrollToPage(this.scrollView.getCurrentPageIndex() + 1, 0.5);
    };

    onPageTurning(event) {
        const pageIndex = this.scrollView.getCurrentPageIndex();
        if (pageIndex === this.scrollView.getPages().length - 1) {
            this.btRight.interactable = false;
            this.btLeft.interactable = true;
        } else if (pageIndex === 0) {
            this.btLeft.interactable = false;
            this.btRight.interactable = true;
        } else {
            this.btLeft.interactable = true;
            this.btRight.interactable = true;
        }
    }

    initPages(numberOfItems: number): void {
        if (this._items.length !== numberOfItems){
            this._clearAll();
        }
        else{
            this.pages.forEach((item) => {
                item.reset();
            });
            return;
        }

        if (this.pages.length === 0) {
            const firstPage = cc.instantiate(this.pagePrefab).getComponent(CalendarPageControl);
            this.pages.push(firstPage);
            firstPage.init(numberOfItems);
            this.scrollView.addPage(firstPage.node);
            this.numItemPerPage = this.pages[0].items.length;
            const numberOfPage = Math.ceil(numberOfItems / this.numItemPerPage);
            for (let i = 0; i < numberOfPage - 1; i++) {
                const page = cc.instantiate(this.pagePrefab).getComponent(CalendarPageControl);
                page.init(numberOfItems);
                this.pages.push(page);
                this.scrollView.addPage(page.node);
            }
        }
        this.pages.forEach((page) => {
            page.reset();
            page.node.active = false;
        });

        this._items = [];
    }

    updateData(eventData: Rebate.IEventData, activityType: number = 1, sysTime: number): void {
        const betTime = eventData.bet_time[0];

        this.initPages(betTime.reward_progress.length);
   
        let lastPage: CalendarPageControl = this.pages[0];
        lastPage.node.active = true;
        const idxOfToday = this.getActiveDate(eventData, sysTime);
        this.numItemPerPage = lastPage.items.length;
        let pageIdx = 0;

        for (let i = 0; i < betTime.reward_progress.length; ++i) {
            if (lastPage.isEmpty()) {
                pageIdx++;
                if (pageIdx < this.pages.length) {
                    lastPage = this.pages[pageIdx];
                } else {
                    lastPage = cc.instantiate(this.pagePrefab).getComponent(CalendarPageControl);
                    this.pages.push(lastPage);
                    this.scrollView.addPage(lastPage.node);
                }
                lastPage.node.active = true;
            }

            let idx = i;
            //let data = this.getDateItemData(eventData, idx, idxOfToday, activityType, sysTime);

            const item = lastPage.dequeue();
            item.setData(betTime.reward_progress[i], (idx) => {
                this._onSelected?.(idx);
                this.performSelected(idx);
            }, i, i === betTime.reward_progress.length - 1);
            this._items.push(item);
        }

        lastPage.updateLayout(betTime.reward_progress.length, this.scrollView.node.width);
        this.adaptViewForPage(lastPage);
        const activeDate = idxOfToday > 0 ? idxOfToday : 0;
        this.selectPage(activeDate);
        // Go to active page
        const selectedPage = Math.floor(activeDate / this.numItemPerPage);
        if (selectedPage > 0) {
            this.scheduleOnce(() => {
                this.scrollView.scrollToPage(selectedPage, 0.5);
            }, 0.5);
        } else {
            this.scrollView.scrollToLeft();
        }
    }

    private adaptViewForPage(page: CalendarPageControl) {
        const layout = page.rewardContainer.getComponent(cc.Layout);
        layout.paddingLeft = 0;
        layout.paddingRight = 20;
        layout.updateLayout();
    }

    onSelected(onSelected: (idx: number) => void) {
        this._onSelected = onSelected;
    }

    selectPage(idx: number) {
        this._onSelected?.(idx);
    }

    protected getActiveDate(data: Rebate.IEventData, sysTime: number): number {
        for (let i = 0; i < data.bet_time.length; i++) {
            const eventData = data.bet_time[i];
            if (eventData.start_time <= sysTime && sysTime <= eventData.end_time) {
                return i;
            }
        }
        return -1;
    }

    private getDateItemData(
        eventData: Rebate.IEventData,
        index: number,
        idxToday: number,
        activityType: number,
        sysTime: number
    ): ICalendarItemParam {
        const data = eventData.bet_time[index];
        let params: ICalendarItemParam = {
            index: index,
            isClaimedAll: !data.reward_progress.some((o) => !o.got),
            isExpired: false,
            canClaim: data.reward_progress?.some((o) => o.can_get && !o.got),
            showOutline: index === idxToday || (index === 0 && idxToday === -1),
            currencyType: 0
        };

        const uniqueCurrencyTypeList = RebateUtil.getUniqueCurrencyTypeList(eventData);
        if (uniqueCurrencyTypeList.length === 1) {
            params.currencyType = uniqueCurrencyTypeList[0];
        }

        params.amountReward = data.reward_progress
            .filter((o) => o.can_get)
            .reduce((accumulator, currentValue) => {
                return Number(accumulator) + Number(currentValue.reward || 0);
            }, 0);

        params.isClaimedAll =
            params.isClaimedAll || (sysTime > data.end_time && !data.reward_progress.some((o) => o.can_get && !o.got));
        params.isExpired = Number(sysTime) > Number(data.end_time) && Number(data.betting_amount) === 0;
        const isNotToday = idxToday !== index || idxToday === -1;
        if (isNotToday) {
            const date = pf.Util.convertTimeToUTC8(new Date(data.start_time * 1000));
            if (activityType === 2) {
                params.dateString = `${date.getMonth() + 1}${pf.languageManager.getString(
                    'minigame_rebate_text_month'
                )}${date.getDate()}`;
            } else if (activityType === 3) {
                params.dateString = pf.languageManager.getString('minigame_cowboy_rebate_day').split(' ')[
                    date.getDay()
                ];
            }
        } else {
            params.dateString = pf.languageManager.getString('minigame_cowboy_rebate_today');
        }
        return params;
    }

    performSelected(index: number) {
        this._items.forEach((item) => {
            item.setOutline(index);
        });
    }

    private _clearAll(): void {
        for (const page of this.pages) {
            if (page && page.node) {
                page.node.destroy();
            }
        }
        this.pages = [];
        this.scrollView.removeAllPages();
        
        this._items = [];
    }

}

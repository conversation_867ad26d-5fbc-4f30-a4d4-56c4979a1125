import * as pf from '../../../../../poker-framework/scripts/pf';

const {ccclass, property} = cc._decorator;
const CH_FONT_SIZE = 28;
@ccclass
export class GameModeTabsControl extends cc.Component {
    @property([cc.Node])
    private gameNodes: cc.Node[] = [];

    @property(cc.Layout)
    private layout: cc.Layout = null;

    @property 
    private padding: number = 50; // Padding for text (25px on each side)

    private readonly NODE_INDEX_TO_GAME_ID = [
        pf.client.GameId.CowBoy,
        pf.client.GameId.PokerMaster,
        pf.client.GameId.HumanBoy
    ];

    updateGameModesByIds(gameIds: number[]): void {
        this.gameNodes.forEach((node, index) => {
            const gameId = this.NODE_INDEX_TO_GAME_ID[index];
            node.active = gameId !== undefined && gameIds.includes(gameId);
        });

        this.updateLayout();
    }

    private updateLayout(): void {
        this.gameNodes.forEach(node => {
            if (!node.active) return;
            
            const nameNode = node.getChildByName('name');
            if (!nameNode) return;

            const label = nameNode.getComponent(cc.Label);
            if (!label) return;
            if (pf.languageManager.currentLanguage ===  pf.LANGUAGE_GROUPS.zh_CN ) {
                label.fontSize = CH_FONT_SIZE;
            }
            // Wait 1 frame to update label size after i18n changes text
            this.scheduleOnce(() => {
                const textWidth = label.node.width;
                const newWidth = textWidth + this.padding;
                
                node.width = newWidth;

                if (this.layout) {
                    this.layout.updateLayout();
                }
            }, 0);
        });
    }

    protected onLoad(): void {
        this.updateLayout();
    }

}

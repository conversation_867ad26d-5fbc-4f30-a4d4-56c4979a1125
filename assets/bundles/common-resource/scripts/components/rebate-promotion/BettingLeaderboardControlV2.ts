import * as pf from '../../../../../poker-framework/scripts/pf';
import { BettingRankingItemControlV2 } from './BettingRankingItemControlV2';
import { ScrollViewItemControl } from '../ScrollViewItemControl';

const { ccclass, property } = cc._decorator;
@ccclass
export class BettingLeaderboardControlV2 extends cc.Component {
    @property(cc.Node) noRanking: cc.Node = null;
    @property(cc.Node) myRankHolder: cc.Node = null;
    @property(cc.Prefab) rankItemPrefab: cc.Prefab = null;
    @property(ScrollViewItemControl) scrollViewControl: ScrollViewItemControl = null;

    private _myRank: BettingRankingItemControlV2 = null;
    private _once: boolean = true;

    protected onLoad(): void {
        this._initUI();
    }

    private _initUI(): void {
        pf.UIUtil.adaptWidget(this.node);
    }

    showleaderboard(myData: any, rankInfos: pf.client.Rebate.ILeaderboardInfo[]): void {
        const leaderboardInfos = rankInfos?.filter((item) => item.player_id > 0 && item.reward > 0) || [];
        if (leaderboardInfos.length === 0) {
            this.myRankHolder.active = false;
            this.noRanking.active = true;
            return;
        }
        this.noRanking.active = false;
        if (this.scrollViewControl && this._once) {
            this._once = false;
            this.scrollViewControl.bindPrefab(this.rankItemPrefab, 'BettingRankingItemControlV2', []);
            this.scrollViewControl.generateItemPool();
            this.scrollViewControl.bindScrollEventTarget(this);
        }
        const updatedLeaderboardInfos = leaderboardInfos.map(info => ({
            ...info,
            highLight: info.player_id === myData.myId 
        }));
        this.scrollViewControl.reloadView(updatedLeaderboardInfos);

        const myRecord = rankInfos.find((item) => item.player_id === myData.myId);
        const data = this._createMyData(myData);
        if (myRecord) {
            data.bet_amount = myRecord.bet_amount;
            data.rank = myRecord.rank;
            data.reward = myRecord.reward || 0;
        }

        this.myRankHolder.active = true;
        if (!this._myRank) {
            this._myRank = cc.instantiate(this.rankItemPrefab).getComponent(BettingRankingItemControlV2);
            this._myRank.node.setParent(this.myRankHolder);
        }
        this._myRank.updateUI(data);
    
    }

    private _createMyData(myData: any): pf.client.Rebate.ILeaderboardInfo & { highLight: boolean } {
        const userInfo = pf.app.getGameContext<pf.services.MiniGameContext>().room.selfPlayer;
        
        const myRecord: pf.client.Rebate.ILeaderboardInfo = {
            player_id: myData.myId,
            name: userInfo.name,
            bet_amount: myData.betAmount,
            reward: 0,
            plat: userInfo.plat,
            rank: -1,
            currency_type: 0,
            head: userInfo.head
        };
        return {...myRecord, highLight: true};
    }
}

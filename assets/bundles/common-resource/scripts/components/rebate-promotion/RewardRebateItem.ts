import * as util from '../../utils/common-util-index';
import { Rebate } from '../../../../../poker-framework/scripts/poker-client/poker-socket';

const { ccclass, property } = cc._decorator;

@ccclass
export class RewardRebateItem extends cc.Component {
    @property(cc.Label) amountBet: cc.Label = null;
    @property(cc.Label) amountReward: cc.Label = null;
    @property(cc.Sprite) coinIcon: cc.Sprite = null;
    @property(cc.Sprite) bgBetAmount: cc.Sprite = null;
    @property(cc.Color) colors: cc.Color[] = [];
    @property(cc.SpriteFrame) bgSprite: cc.SpriteFrame[] = [];
    @property(cc.SpriteFrame) coinFrame: cc.SpriteFrame[] = [];
    @property(cc.Sprite) progressLine: cc.Sprite = null;
    @property(cc.SpriteFrame) progressFrame: cc.SpriteFrame[] = [];
    @property(cc.Sprite) lineGrey: cc.Sprite = null;
    @property(cc.Color) lineColors: cc.Color[] = [];
    _onSelected: (idx: number) => void;
    index: number = 0;

    protected start(): void {

    }

    setData(data: Rebate.IRewardProgress, onSelected: (idx: number) => void, itemIndex: number = 0, isLastItem: boolean = false): void {
        this.node.active = true;
        this._onSelected = onSelected;
        this.amountBet.string = util.CommonUtil.getShortOwnCoinString (data.amount_gte);
        this.amountReward.string = util.CommonUtil.getShortOwnCoinString(data.reward);

        const index = data.got ? 0 : 1;
            
        this.coinIcon.spriteFrame = this.coinFrame[index];
        this.amountBet.node.color = this.colors[index];
        this.bgBetAmount.spriteFrame = this.bgSprite[index];

        if (this.progressLine) {
            this.progressLine.node.active = !isLastItem;
            if (this.progressLine.node.active) {
                this.progressLine.spriteFrame = this.progressFrame[index];
            }
        }

        if (this.lineGrey) {
            this.lineGrey.node.active = (itemIndex === 0 || isLastItem);
            if (this.lineGrey.node.active) {
                this.lineGrey.node.color = this.lineColors[index];
            }
        }
    }

    setOutline(selectedIdx: number): void {

    }
}

/* eslint-disable camelcase */
import * as pf from '../../../../poker-framework/scripts/pf';
import { CommonUtil } from './common-util';

export class RebateUtil {
    static getRankString(rank: number, topNCanGet: number): string {
        if (rank > 0 && rank <= 10) {
            return pf.StringUtil.formatC(pf.languageManager.getString('minigame_rebate_top_rank'), rank);
        }

        if (rank > 0 && rank <= topNCanGet) {
            return pf.StringUtil.formatC(pf.languageManager.getString('minigame_rebate_toast_top_tips'), topNCanGet);
        }

        return pf.languageManager.getString('minigame_rebate_no_rank');
    }

    static getRewardString(reward_amount: { [k: string]: number }, tagColor?: string): string {
        const list = Object.entries(reward_amount);
        if (list.length === 0) {
            return '';
        }
        let [key, value] = list[0];
        const space = pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN ? '' : ' ';
        let str = `${RebateUtil.getStringMoneyWithColor(value, tagColor, key)}`;
        for (let i = 1; i < list.length - 1; i++) {
            [key, value] = list[i];
            str += `${pf.languageManager.getString('string_comma')}${RebateUtil.getStringMoneyWithColor(
                value,
                tagColor,
                key
            )}`;
        }
        if (list.length > 1) {
            [key, value] = list[list.length - 1];
            str += `${space}${pf.languageManager.getString('string_and')}${space}${RebateUtil.getStringMoneyWithColor(
                value,
                tagColor,
                key
            )}`;
        }
        return str;
    }

    static getStringMoneyWithColor(amount: number, color?: string, currencyType?: string): string {
        let str = CommonUtil.getShortOwnCoinString(amount);
        const space = pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN ? '' : ' ';
        if (!!color && color !== '') {
            str = `<color=${color}>${CommonUtil.getShortOwnCoinString(amount)}</color>`;
        }
        if (currencyType) {
            str += `${space}${pf.languageManager.getString('minigame_currency_type_' + currencyType)}`;
        }
        return str;
    }

    static getUniqueCurrencyTypeList(data: pf.client.Rebate.IEventData) {
        const uniqueCurrencyTypeList = data.bet_time
            .reduce((acc, curr) => acc.concat(curr.reward_progress), [])
            .map((item) => item.currency_type)
            .filter((value, index, self) => self.indexOf(value) === index);

        return uniqueCurrencyTypeList;
    }

    static getActiveEventByGame (gameId: number, events: pf.client.IEventStatusClient[]) {
        const gameEvents = events.filter(event => 
            event.game_ids.includes(gameId)
        );

        let activeEvent = gameEvents?.find(event => {
            const betTime = event.setting.bet_time[0];
            return betTime.start_time <= event.system_time && event.system_time <= betTime.end_time;
        });

        if (!activeEvent) {
            activeEvent = gameEvents?.find(event => {
                const betTime = event.setting.bet_time[0];
                return betTime.start_time > event.system_time;
            });
        }
        if (!activeEvent) {
            activeEvent = gameEvents?.find(event => {
                const betTime = event.setting.bet_time[0];
                return betTime.end_time < event.system_time;
            });
        }
        return activeEvent;
    }

     static getEventTitle(evt: pf.client.IEventStatusClient){
        const content = evt.content;
        if (!content) {
            return '';
        }
        return content[pf.languageManager.currentLanguage]?.title || '';
    }
}

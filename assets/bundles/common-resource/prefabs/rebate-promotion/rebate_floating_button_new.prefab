[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "rebate_floating_button_new", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 8}, {"__id__": 12}, {"__id__": 15}, {"__id__": 54}, {"__id__": 57}, {"__id__": 67}, {"__id__": 72}], "_active": true, "_components": [{"__id__": 70}, {"__id__": 76}, {"__id__": 77}], "_prefab": {"__id__": 79}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Base", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 119}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "926c66da-cb2c-4121-bfcd-1b5b9c8f3abf"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "84SoLWfDxGbbrB/fmtP/1G", "sync": false}, {"__type__": "cc.Node", "_name": "Box", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -16, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4d1394ff-3a64-4b97-8e81-e2a3a6cd5014"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9akwDny5JB2b3vK2o7zeVo", "sync": false}, {"__type__": "cc.Node", "_name": "Node_Bar", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}, {"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 19, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "08090600-919c-4085-8a76-37845390c7ac"}, "_type": 3, "_sizeMode": 1, "_fillType": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_fillStart": 0, "_fillRange": -0.5, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "57269u++1pHjqEKB4ltO7NT", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e3a1zVLKtItI0FAocYjDnh", "sync": false}, {"__type__": "cc.Node", "_name": "BarGlow", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 207, "g": 164, "b": 70, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 122, "height": 86}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 18.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_spriteFrame": {"__uuid__": "26396392-710e-4bcf-a922-166d4aef0c81"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5cfAi6qpFG8qld4nFtgo4S", "sync": false}, {"__type__": "cc.Node", "_name": "Node_ParticlePivot", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 16}], "_active": true, "_components": [], "_prefab": {"__id__": 53}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 14.136, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "New 3D Particle_Coins", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 17}], "_prefab": {"__id__": 52}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -9.741, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem3D", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "_materials": [{"__uuid__": "e265aba0-996c-4043-af18-f0c920ff9476"}, null], "duration": 0.25, "_capacity": 30, "loop": false, "playOnAwake": false, "_prewarm": false, "_simulationSpace": 1, "simulationSpeed": 0.3, "startDelay": {"__id__": 18}, "startLifetime": {"__id__": 19}, "startColor": {"__id__": 20}, "scaleSpace": 0, "startSize": {"__id__": 21}, "startSpeed": {"__id__": 22}, "startRotation": {"__id__": 23}, "gravityModifier": {"__id__": 24}, "rateOverTime": {"__id__": 25}, "rateOverDistance": {"__id__": 26}, "bursts": [], "_shapeModule": {"__id__": 27}, "_colorOverLifetimeModule": {"__id__": 29}, "_sizeOvertimeModule": {"__id__": 39}, "_rotationOvertimeModule": {"__id__": 48}, "_renderMode": 0, "_velocityScale": 0, "_lengthScale": 0, "_mesh": null, "_id": ""}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 3, "constantMin": 0.08, "constantMax": 0.4, "multiplier": 1}, {"__type__": "cc.GradientRange", "_mode": 0, "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"__type__": "cc.CurveRange", "mode": 3, "constantMin": 25, "constantMax": 45, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 400, "multiplier": 60}, {"__type__": "cc.CurveRange", "mode": 3, "constantMin": 0, "constantMax": 360, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 600, "multiplier": 1000}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 35, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 10}, {"__type__": "cc.ShapeModule", "enable": true, "_shapeType": 1, "emitFrom": 2, "radius": 30, "radiusThickness": 0.5, "_angle": 0.7853981633974483, "_arc": 3.141592653589793, "arcMode": 0, "arcSpread": 1, "arcSpeed": {"__id__": 28}, "length": 15, "boxThickness": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_position": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_scale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "alignToDirection": false, "randomDirectionAmount": 0, "sphericalDirectionAmount": 15, "randomPositionAmount": 2}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.ColorOvertimeModule", "enable": true, "color": {"__id__": 30}}, {"__type__": "cc.GradientRange", "_mode": 1, "gradient": {"__id__": 31}}, {"__type__": "cc.Gradient", "colorKeys": [{"__id__": 32}, {"__id__": 33}], "alphaKeys": [{"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 38}], "mode": 0}, {"__type__": "cc.<PERSON><PERSON>", "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "time": 0}, {"__type__": "cc.<PERSON><PERSON>", "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "time": 1}, {"__type__": "cc.<PERSON><PERSON><PERSON>", "alpha": 0, "time": 0}, {"__type__": "cc.<PERSON><PERSON><PERSON>", "alpha": 255, "time": 0.12954545454545455}, {"__type__": "cc.<PERSON><PERSON><PERSON>", "alpha": 255, "time": 0.45}, {"__type__": "cc.<PERSON><PERSON><PERSON>", "alpha": 0, "time": 0.6}, {"__type__": "cc.<PERSON><PERSON><PERSON>", "alpha": 0, "time": 1}, {"__type__": "cc.SizeOvertimeModule", "enable": true, "separateAxes": false, "size": {"__id__": 40}, "x": {"__id__": 44}, "y": {"__id__": 46}, "z": {"__id__": 47}}, {"__type__": "cc.CurveRange", "mode": 1, "curve": {"__id__": 41}, "multiplier": 1}, {"__type__": "cc.AnimationCurve", "keyFrames": [{"__id__": 42}, {"__id__": 43}], "preWrapMode": 2, "postWrapMode": 2}, {"__type__": "cc.Keyframe", "time": 0, "value": 1, "inTangent": 0, "outTangent": 0}, {"__type__": "cc.Keyframe", "time": 1, "value": 0.56, "inTangent": -0.89, "outTangent": 0}, {"__type__": "cc.CurveRange", "mode": 1, "curve": {"__id__": 45}, "multiplier": 1}, {"__type__": "cc.AnimationCurve", "keyFrames": [{"__id__": 42}, {"__id__": 43}], "preWrapMode": 2, "postWrapMode": 2}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 5, "multiplier": 1}, {"__type__": "cc.RotationOvertimeModule", "enable": true, "_separateAxes": true, "x": {"__id__": 49}, "y": {"__id__": 50}, "z": {"__id__": 51}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 5, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": -5, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 5, "multiplier": 1}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3dmgnDSxRMG5iPm3N99ugI", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9FiGB7rNCOphlqiMKU6Cv", "sync": false}, {"__type__": "cc.Node", "_name": "Base2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 55}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 138, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -41, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "80feb480-783a-4457-9417-9a6dc5beb626"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfNmqI/qZBHYLrfkZz/FdA", "sync": false}, {"__type__": "cc.Node", "_name": "Base2_Mask", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 58}, {"__id__": 61}], "_active": true, "_components": [{"__id__": 65}], "_prefab": {"__id__": 66}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.5, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON>", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [], "_active": true, "_components": [{"__id__": 59}], "_prefab": {"__id__": 60}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 31, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-78.121, -1.377, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 1, "_spriteFrame": {"__uuid__": "98c2e122-5d1e-46ce-b3f1-0b3e3a2c9fb7"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9wkqgiYRN7Irj8JH2bHDc", "sync": false}, {"__type__": "cc.Node", "_name": "label_Text", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 63}], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 87, "g": 55, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 108, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "1投注返奖", "_N$string": "1投注返奖", "_fontSize": 22, "_lineHeight": 28, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "57269u++1pHjqEKB4ltO7NT", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cuWtroZtOIYqSynkOSsxa", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": {"__uuid__": "5d20fb41-871c-4226-b87c-63ef92da660d"}, "_type": 2, "_segments": 64, "_N$alphaThreshold": 0.6, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aecSp7BlZOQYf0E3x2gLiG", "sync": false}, {"__type__": "cc.Node", "_name": "anim-show", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 69}], "_prefab": {"__id__": 71}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "b7e9f6kfkpNfLYDCeJtVUIf", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "nodeList": [{"__id__": 17}], "DelaySec": 0, "_id": ""}, {"__type__": "49d8fKTKhpD36Mu3ZVVY21u", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "ShowNodeList": true, "HideNodeListOnFinished": false, "PlayAdditive": false, "nodeList": [{"__id__": 70}], "PlayClipName": "BettingRebate", "PlayheadStartTime": 0, "delay": 0, "EmitButtonAfterFinished": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_defaultClip": {"__uuid__": "d6c5a0ca-7d0c-48dc-a27d-e1d60c038bc8"}, "_clips": [{"__uuid__": "d6c5a0ca-7d0c-48dc-a27d-e1d60c038bc8"}, {"__uuid__": "4ccca047-f828-49d4-a2d2-3f24ecd377a2"}], "playOnLoad": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8fZ6fWfdMFKep1IoYvKa6", "sync": false}, {"__type__": "cc.Node", "_name": "anim-barMax", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 73}, {"__id__": 74}], "_prefab": {"__id__": 75}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "b7e9f6kfkpNfLYDCeJtVUIf", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "nodeList": [{"__id__": 17}], "DelaySec": 0, "_id": ""}, {"__type__": "49d8fKTKhpD36Mu3ZVVY21u", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "ShowNodeList": false, "HideNodeListOnFinished": false, "PlayAdditive": false, "nodeList": [{"__id__": 70}], "PlayClipName": "BetingRebate_BarMax", "PlayheadStartTime": 0, "delay": 0, "EmitButtonAfterFinished": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "532YnN7tRKZq/04bJEysmG", "sync": false}, {"__type__": "0ae9fhre3JP2KrL6Szr8yVe", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "spB1Radial": {"__id__": 9}, "animShow": {"__id__": 69}, "p3dShow": {"__id__": 68}, "animBarMax": {"__id__": 74}, "p3dBarMax": {"__id__": 73}, "lbStatus": {"__id__": 62}, "ndParticlePivot": {"__id__": 15}, "ndParticle": {"__id__": 16}, "ndBarGlow": {"__id__": 12}, "ndBox": {"__id__": 5}, "ndBar": {"__id__": 8}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 78}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "0ae9fhre3JP2KrL6Szr8yVe", "handler": "clickFloatButton", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]
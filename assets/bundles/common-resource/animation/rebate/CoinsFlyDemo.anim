{"__type__": "cc.AnimationClip", "_name": "CoinsFlyDemo", "_objFlags": 0, "_native": "", "_duration": 2.6, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"CoinFLYDEMO_1": {"props": {"position": [{"frame": 0, "value": [0, 0, 0], "curve": "cubicInOut"}, {"frame": 1.1666666666666667, "value": [-123.364, -919.202, 0]}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.08333333333333333, "value": 255}, {"frame": 1.1666666666666667, "value": 255}, {"frame": 1.25, "value": 0}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec3", "x": 0.25, "y": 0.25, "z": 1}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"frame": 1.25, "value": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}}]}}, "CoinFLYDEMO_2": {"props": {"position": [{"frame": 0.016666666666666666, "value": [0, 0, 0], "curve": "cubicInOut"}, {"frame": 1.1833333333333333, "value": [-123.364, -919.202, 0]}], "opacity": [{"frame": 0.016666666666666666, "value": 0}, {"frame": 0.1, "value": 200}, {"frame": 1.1833333333333333, "value": 200}, {"frame": 1.2666666666666666, "value": 0}], "scale": [{"frame": 0.016666666666666666, "value": {"__type__": "cc.Vec3", "x": 0.25, "y": 0.25, "z": 1}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.18333333333333332, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"frame": 1.2666666666666666, "value": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}}]}}, "CoinFLYDEMO_3": {"props": {"position": [{"frame": 0.03333333333333333, "value": [0, 0, 0], "curve": "cubicInOut"}, {"frame": 1.2, "value": [-123.364, -919.202, 0]}], "opacity": [{"frame": 0.03333333333333333, "value": 0}, {"frame": 0.11666666666666667, "value": 160}, {"frame": 1.2, "value": 160}, {"frame": 1.2833333333333334, "value": 0}], "scale": [{"frame": 0.03333333333333333, "value": {"__type__": "cc.Vec3", "x": 0.25, "y": 0.25, "z": 1}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.2, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"frame": 1.2833333333333334, "value": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}}]}}, "CoinFLYDEMO_4": {"props": {"position": [{"frame": 0.05, "value": [0, 0, 0], "curve": "cubicInOut"}, {"frame": 1.2166666666666666, "value": [-123.364, -919.202, 0]}], "opacity": [{"frame": 0.05, "value": 0}, {"frame": 0.13333333333333333, "value": 100}, {"frame": 1.2166666666666666, "value": 100}, {"frame": 1.3, "value": 0}], "scale": [{"frame": 0.05, "value": {"__type__": "cc.Vec3", "x": 0.25, "y": 0.25, "z": 1}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.21666666666666667, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"frame": 1.3, "value": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}}]}}, "Node_ToastDemo": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 1.1, "value": 0}, {"frame": 1.2666666666666666, "value": 255}, {"frame": 2.2666666666666666, "value": 255}, {"frame": 2.6, "value": 0}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec3", "x": 0.95, "y": 0.95, "z": 1}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 1.1, "value": {"__type__": "cc.Vec3", "x": 0.95, "y": 0.95, "z": 1}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 1.4333333333333333, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}]}}}}, "events": []}
{"__type__": "cc.AnimationClip", "_name": "BetingRebate_BarMax", "_objFlags": 0, "_native": "", "_duration": 1.25, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"Node_ParticlePivot": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1}, "curve": "sineOut"}, {"frame": 0.05, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}]}}, "Node_ParticlePivot/New 3D Particle_Coins": {"props": {"y": [{"frame": 0, "value": -5, "curve": "sineOut"}, {"frame": 0.2, "value": 15}, {"frame": 0.36666666666666664, "value": 0}]}}, "BarGlow": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.05, "value": 255}, {"frame": 0.11666666666666667, "value": 255, "curve": "linear"}, {"frame": 0.6666666666666666, "value": 0}]}}, "Box": {"props": {"scale": [{"frame": 0, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "curve": "sineOut"}, {"frame": 0.08333333333333333, "value": {"__type__": "cc.Vec3", "x": 1.25, "y": 1.25, "z": 1}, "curve": "sineOut"}, {"frame": 0.6333333333333333, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "curve": "sineOut"}]}}, "Node_Bar": {"comps": {"cc.Sprite": {"fillRange": [{"frame": 0.3333333333333333, "value": -1, "curve": "cubicOut"}, {"frame": 1.25, "value": -0.5}]}}}}}, "events": []}
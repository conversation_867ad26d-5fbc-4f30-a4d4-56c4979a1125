{"__type__": "cc.AnimationClip", "_name": "BettingRebate", "_objFlags": 0, "_native": "", "_duration": 1.6, "sample": 60, "speed": 1, "wrapMode": 1, "curveData": {"paths": {"Base": {"props": {"scale": [{"frame": 0.016666666666666666, "value": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.23333333333333334, "value": {"__type__": "cc.Vec3", "x": 1.1, "y": 1.1, "z": 1}, "curve": "sineOut"}, {"frame": 0.7666666666666667, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}]}}, "Box": {"props": {"position": [{"frame": 0.016666666666666666, "value": [0, -16, 0]}, {"frame": 0.23333333333333334, "value": [0, 0.557, 0]}, {"frame": 0.43333333333333335, "value": [0, -16, 0]}, {"frame": 0.5666666666666667, "value": [0.403, -11.388, 0]}, {"frame": 0.7666666666666667, "value": [0, -16, 0]}, {"frame": 0.9833333333333333, "value": [0, 0.557, 0]}, {"frame": 1.1833333333333333, "value": [0, -16, 0]}, {"frame": 1.3166666666666667, "value": [0.403, -11.388, 0]}, {"frame": 1.6, "value": [0, -16, 0]}], "angle": [{"frame": 0.016666666666666666, "value": 0}, {"frame": 0.23333333333333334, "value": 15}, {"frame": 0.43333333333333335, "value": 0}, {"frame": 0.5666666666666667, "value": -5}, {"frame": 0.7666666666666667, "value": 0}, {"frame": 0.9833333333333333, "value": 5}, {"frame": 1.1833333333333333, "value": 0}, {"frame": 1.3166666666666667, "value": -3}, {"frame": 1.6, "value": 0}], "scale": [{"frame": 0.016666666666666666, "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1}, "curve": "sineOut"}, {"frame": 0.23333333333333334, "value": {"__type__": "cc.Vec3", "x": 1.25, "y": 1.25, "z": 1}, "curve": "sineOut"}, {"frame": 0.43333333333333335, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "curve": "sineOut"}, {"frame": 0.5666666666666667, "value": {"__type__": "cc.Vec3", "x": 1.15, "y": 1.15, "z": 1}, "curve": "sineOut"}, {"frame": 0.7666666666666667, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"frame": 0.9833333333333333, "value": {"__type__": "cc.Vec3", "x": 1.25, "y": 1.25, "z": 1}, "curve": "sineOut"}, {"frame": 1.1833333333333333, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "curve": "sineOut"}, {"frame": 1.3166666666666667, "value": {"__type__": "cc.Vec3", "x": 1.15, "y": 1.15, "z": 1}, "curve": "sineOut"}, {"frame": 1.6, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}]}}, "Base2": {"props": {"scale": [{"frame": 0.016666666666666666, "value": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 1}, "curve": [0.18, 0.89, 0.31, 1.21]}, {"frame": 0.3333333333333333, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}]}}, "label_Text": {"props": {"opacity": [{"frame": 0.06666666666666667, "value": 0, "curve": "cubicOut"}, {"frame": 0.21666666666666667, "value": 255}]}}, "Node_ParticlePivot": {"comps": {}, "props": {"scale": [{"frame": 0.016666666666666666, "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1}, "curve": "sineOut"}, {"frame": 0.11666666666666667, "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}]}}, "Node_ParticlePivot/New 3D Particle_Coins": {"comps": {}, "props": {"y": [{"frame": 0.06666666666666667, "value": -5, "curve": "sineOut"}, {"frame": 0.26666666666666666, "value": 15}, {"frame": 0.43333333333333335, "value": 0}]}}, "Base2_Mask/Shine": {"props": {"x": [{"frame": 0.4166666666666667, "value": -78.121}, {"frame": 1.4833333333333334, "value": 80.265}]}}}, "props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.016666666666666666, "value": 255}]}}, "events": []}
{"groups": {"zh_CN": {"strings": {"InquireView_lab_3": "积分", "Cancel": "取消", "Confirm": "确定", "MiniGame_AddAutoBet_Text": "增加20局", "MiniGame_Switch_Table": "切换其他牌桌", "MiniGame_Exit": "退出", "MiniGame_Switch_Content": "牌桌已经关闭", "MiniGame_Select_AddAutoBet_Text": "增加 %d 局", "MiniGame_btn_desc_auto_bet_reached": "续投次数已达上限, 为您增加%d局", "M_UITitle113": "高牌", "M_UITitle114": "一对", "M_UITitle115": "两对", "M_UITitle116": "三条", "M_UITitle117": "顺子", "M_UITitle118": "同花", "M_UITitle119": "葫芦", "M_UITitle120": "四条", "M_UITitle121": "同花顺", "M_UITitle122": "皇家同花顺", "UIOpenNewWindow": "即将在新窗口打开页面", "TipsPanel_sure_button": "确定", "TipsPanel_cancel_button": "取消", "LuckTurntables_des_text": "点击轮盘  即可开奖", "LuckTurntables_result_iphone_panel_des": "请等待客服与您联系", "LuckTurntables_result_ticket_panel_des": "请至“背包”查收", "LuckTurntables_tips_text_0": "恭喜您抽中金币红包[%s]\n请在48小时内联系客服领取", "LuckTurntables_tips_text_1": "恭喜您抽中小游戏币红包[%s]\n请在48小时内联系客服领取", "LuckTurntables_tips_text_2": "恭喜您抽中美元红包[%s]\n请在48小时内联系客服领取", "LuckTurntables_tips_text_3": "恭喜您抽中%s\n请在48小时内联系客服领取", "LuckTurntables_tips_text_5": "恭喜您抽中体育体验金紅包[%s]\n请在48小时内联系客服人员领取", "LuckTurntables_tips_text_help_0": "恭喜您抽中金币红包[%s]\n请到【我】-【我的红包】中查看", "LuckTurntables_tips_text_help_1": "恭喜您抽中小游戏币红包[%s]\n请到【我】-【我的红包】中查看", "LuckTurntables_tips_text_help_2": "恭喜您抽中美元红包[%s]\n请到【我】-【我的红包】中查看", "LuckTurntables_tips_text_help_3": "恭喜您抽中%s\n请到【我】-【我的红包】中查看", "LuckTurntables_tips_text_help_5": "恭喜赢得体育博彩红包[%s]\n请到【我】-【我的红包】中查看", "LuckTurntablesButton_tips_text": "点击抽取红包大奖，今日活动结束前有效。", "RedPackets_des_text": "恭喜抽中幸运红包", "Small_Game_Hongbao_Top": "红包TOP%d", "RedEnvelope_usdt_tool_tip": "数字钱包使用者，使用数字钱包存取款更方便，更安全", "Game_Hongbao_desc_0": "恭喜%s玩家%s获得|%s#FFCC00|金币红包", "Small_Game_Hongbao_desc_0": "恭喜玩家%s在%s中获得|%s#FFCC00|金币红包", "Game_Hongbao_desc_1": "恭喜%s玩家%s获得|%s#FFCC00|小游戏币红包", "Small_Game_Hongbao_desc_1": "恭喜玩家%s在%s中获得|%s#FFCC00|小游戏币红包", "Game_Hongbao_desc_2": "恭喜%s玩家%s获得|%s#FFCC00|美元红包", "Small_Game_Hongbao_desc_2": "恭喜玩家%s在%s中获得|%s#FFCC00|美元红包", "Game_Hongbao_desc_3": "恭喜%s玩家%s获得%s", "Small_Game_Hongbao_desc_3": "恭喜玩家%s在%s中获得%s", "Game_Hongbao_desc_5": "恭喜, |%s#D7C647| 玩家 |%s#D7C647|, 赢得了%s体育体验金", "Small_Game_Hongbao_desc_5": "恭喜, |%s#D7C647| 玩家 |%s#D7C647|, 赢得了%s体育体验金", "ServerErrorCode1": "OK", "ServerErrorCode2": "客户端版本号过低，请重新下载", "ServerErrorCode3": "该玩家未找到", "ServerErrorCode4": "您的账号在其它设备登录，请重新登录", "ServerErrorCode5": "查询密钥失败，请联系客服", "ServerErrorCode6": "无法从世界服务器获取数据", "ServerErrorCode7": "内部RPC错误", "ServerErrorCode8": "内部RPC返回值错误", "ServerErrorCode17": "无法创建更多房间", "ServerErrorCode18": "创建了过多房间", "ServerErrorCode19": "创建房间参数错误", "ServerErrorCode20": "无法支付记录费，请充值", "ServerErrorCode21": "创建房间类型验证失败", "ServerErrorCode22": "该房间已解散", "ServerErrorCode23": "只有房主可以解散房间", "ServerErrorCode24": "房间人数已满", "ServerErrorCode25": "您已经加入该房间", "ServerErrorCode26": "玩家不在该房间内", "ServerErrorCode27": "该座位已有人", "ServerErrorCode28": "需要带入金币才能坐下", "ServerErrorCode29": "牌桌已满", "ServerErrorCode30": "玩家已入座", "ServerErrorCode31": "游戏中牌桌不能入座", "ServerErrorCode32": "金币数量不足", "ServerErrorCode33": "随机入座失败", "ServerErrorCode34": "随机入座失败2", "ServerErrorCode35": "该座位已有玩家", "ServerErrorCode36": "入座失败", "ServerErrorCode37": "入座失败2", "ServerErrorCode38": "站起围观失败", "ServerErrorCode39": "带入金币数量已达上限", "ServerErrorCode39_usdt": "带入美元数量已达上限", "ServerErrorCode40": "不能多次带入", "ServerErrorCode41": "只限房主应答", "ServerErrorCode42": "您的余额不足，请充值", "ServerErrorCode43": "带入申请超时", "ServerErrorCode44": "批准带入数量无效", "ServerErrorCode45": "只有房主才能开始游戏", "ServerErrorCode46": "牌局已开始", "ServerErrorCode47": "玩家人数不够开始牌局", "ServerErrorCode48": "还没轮到您行动", "ServerErrorCode49": "下注金额不正确", "ServerErrorCode50": "非法游戏动作", "ServerErrorCode51": "当前不该您行动", "ServerErrorCode52": "配置文件错误", "ServerErrorCode53": "余额不足，请充值", "ServerErrorCode54": "只有入座玩家和房主才能聊天", "ServerErrorCode55": "保险购买玩家ID无效", "ServerErrorCode56": "购买保险超时", "ServerErrorCode57": "当前游戏轮次不能购买保险", "ServerErrorCode58": "已购买保险", "ServerErrorCode59": "底池ID未找到", "ServerErrorCode60": "超过能够购买的Outs数量", "ServerErrorCode61": "购买的金额超过底池1/3", "ServerErrorCode62": "底池金币数量不足以购买", "ServerErrorCode63": "Outs购买无效", "ServerErrorCode64": "Outs购买无效2", "ServerErrorCode65": "只限入座玩家,才可进行该操作", "ServerErrorCode66": "保位离桌时间到", "ServerErrorCode67": "已向房主发出申请等待批准，180秒超时", "ServerErrorCode68": "当前不在座位上", "ServerErrorCode69": "已经是保位离桌状态", "ServerErrorCode70": "当前不在保位离桌状态", "ServerErrorCode71": "当前玩家ID无效", "ServerErrorCode72": "当前无法加注，只能All in或跟注", "ServerErrorCode73": "与世界服务器通讯失败", "ServerErrorCode74": "不是社区管理员无法创建社区游戏房间", "ServerErrorCode75": "创建房间已达上限", "ServerErrorCode76": "创建房间其它错误", "ServerErrorCode77": "带出金额非法", "ServerErrorCode78": "最后一个玩家", "ServerErrorCode79": "保险需要带回", "ServerErrorCode80": "没有找到房主", "ServerErrorCode81": "错误的Outs数量", "ServerErrorCode82": "错误的购买金额", "ServerErrorCode83": "必须购买保险", "ServerErrorCode84": "错误的协议", "ServerErrorCode85": "当前不能使用发发看", "ServerErrorCode86": "当前不能使用发发看", "ServerErrorCode87": "您不是管理员，无法使用此功能", "ServerErrorCode88": "房间未开始游戏", "ServerErrorCode89": "该玩家已在禁止入座列表中", "ServerErrorCode90": "您已经被房主禁止入座", "ServerErrorCode91": "该玩家不在禁止入座列表中", "ServerErrorCode92": "游戏还未开始", "ServerErrorCode93": "使用了过多次延时", "ServerErrorCode94": "获取公会社区失败", "ServerErrorCode95": "当前正在游戏进行中", "ServerErrorCode96": "撤码未开启", "ServerErrorCode97": "您与[%s]距离过近或IP一致，被系统禁止入座", "ServerErrorCode98": "当前金币不足无法带入", "ServerErrorCode99": "本社区带入额度已超公会限制", "ServerErrorCode100": "本社区已被公会禁止带入", "ServerErrorCode101": "当前无法偷偷看牌", "ServerErrorCode102": "偷偷看牌次数用完了", "ServerErrorCode103": "次数已用完", "ServerErrorCode104": "服务器正在维护中", "ServerErrorCode105": "因为您已在本桌结算离桌", "ServerErrorCode106": "您已发起结算离桌，本手结束后将结算离开", "ServerErrorCode107": "当前游戏您已经结算过,请勿重复请求", "ServerErrorCode108": "当前游戏您未带入,不需要结算", "ServerErrorCode109": "不能同时设置两个密码", "ServerErrorCode110": "密码错误", "ServerErrorCode111": "此房间参与玩家数量已达到1000人", "ServerErrorCode113": "玩家all in 全场禁言中...", "ServerErrorCode117": "创建社区失败", "ServerErrorCode118": "创建社区数量已达上限", "ServerErrorCode119": "创建社区参数错误", "ServerErrorCode120": "创建社区类型错误", "ServerErrorCode121": "社区ID未找到", "ServerErrorCode122": "只限会长能够解散社区", "ServerErrorCode123": "社区已满员，不能加入", "ServerErrorCode124": "您已在该社区中", "ServerErrorCode125": "您已申请该社区", "ServerErrorCode126": "该玩家不在此社区中", "ServerErrorCode127": "社区金币未清空", "ServerErrorCode128": "社区社员未清空", "ServerErrorCode129": "社区管理员已满", "ServerErrorCode130": "钻石数量不够，请充值", "ServerErrorCode131": "社区星级没用", "ServerErrorCode132": "获取社区价格失败", "ServerErrorCode133": "购买星级社区失败", "ServerErrorCode134": "社区未找到", "ServerErrorCode135": "您的权限不够!", "ServerErrorCode136": "错误", "ServerErrorCode137": "该社区名称已存在", "ServerErrorCode138": "其他管理员已操作", "ServerErrorCode139": "其他管理员已操作", "ServerErrorCode149": "已超过公会创建个数", "ServerErrorCode150": "公会名称已存在", "ServerErrorCode151": "创建公会失败", "ServerErrorCode152": "未清空公会成员", "ServerErrorCode153": "公会未找到", "ServerErrorCode154": "公会权限不够", "ServerErrorCode155": "该社区不在公会中", "ServerErrorCode156": "公会未找到", "ServerErrorCode157": "该社区已在公会中", "ServerErrorCode158": "已申请加入公会", "ServerErrorCode159": "公会成员已满", "ServerErrorCode160": "其他管理员已操作", "ServerErrorCode161": "其他错误", "ServerErrorCode162": "服务端Json解析失败", "ServerErrorCode163": "数据库存储失败", "ServerErrorCode164": "加入公会数量已超限制", "ServerErrorCode165": "设置社区JackPot返奖比例失败", "ServerErrorCode166": "获取社区JackPot返奖比例失败", "ServerErrorCode167": "当前有公会牌局正在进行中，禁止将社区踢出公会", "ServerErrorCode168": "当前有牌局正在进行中，无法进行JackPot设置", "ServerErrorCode169": "当前有牌局正在进行中，无法解散社区", "ServerErrorCode170": "请先解散创建的公会", "ServerErrorCode171": "请先解散创建的公会", "ServerErrorCode172": "您的金币不足", "ServerErrorCode173": "取得玩家系统邮件时间戳失败", "ServerErrorCode174": "取得玩家邮件列表失败", "ServerErrorCode175": "请求索引不合法", "ServerErrorCode176": "加入社区达到上限", "ServerErrorCode177": "牌局正在进行中", "ServerErrorCode179": "请求公告列表失败", "ServerErrorCode180": "发送邮件内容不合格", "ServerErrorCode181": "请先退出加入的公会", "ServerErrorCode182": "社区成员未找到", "ServerErrorCode187": "当前有公会牌局正在进行中，禁止社区退出公会", "ServerErrorCode190": "手机号或区号不能为空", "ServerErrorCode191": "手机号码格式错误", "ServerErrorCode192": "邮箱格式错误", "ServerErrorCode193": "彩池有余额未使用，不能退出社区", "ServerErrorCode194": "俱乐部收益为负,不能解散", "ServerErrorCode195": "设置个人百分比失败，没有权限", "ServerErrorCode196": "设置个人百分比失败", "ServerErrorCode197": "您的账号已被封号，如有疑问请联系客服", "ServerErrorCode198": "设置个人百分比失败，请勿重复设置", "ServerErrorCode199": "设置备注已达上限", "ServerErrorCode200": "忘记账户密码确认找回?", "ServerErrorCode201": "请切换至游戏账号登录，%s忘记密码请联系客服。", "ServerErrorCode205": "您的保险箱金币不足", "ServerErrorCode207": "找不到用户记录。", "ServerErrorCode208": "找不到中奖记录。", "ServerErrorCode209": "二级密码不正确", "ServerErrorCode210": "抽奖失败", "ServerErrorCode211": "数据请求失败", "ServerErrorCode212": "红包活动已关闭", "ServerErrorCode213": "没有设置红包等级", "ServerErrorCode214": "红包金额与监听金额不匹配", "ServerErrorCode215": "金币操作失败", "ServerErrorCode216": "发红包失败", "ServerErrorCode217": "红包已抽完", "ServerErrorCode219": "红包不存在", "ServerErrorCode221": "红包已过期", "ServerErrorCode222": "抢红包过于频繁", "ServerErrorCode223": "获取排行榜失败", "ServerErrorCode224": "您的网络不稳定，请重新登录", "ServerErrorCode225": "系统错误", "ServerErrorCode226": "系统维护中，请重新登录", "ServerErrorCode228": "您的二级密码未设置", "ServerErrorCode229": "登录超时，请重新登录", "ServerErrorCode230": "该申请已取消", "ServerErrorCode232": "本月已变更过社区", "ServerErrorCode233": "当前不允许创建社区", "ServerErrorCode234": "当前公会玩家无法加入其他社区", "ServerErrorCode235": "当前社区玩家无法加入其他社区", "ServerErrorCode238": "无法修改第一社区，请联系客服", "ServerErrorCode251": "美元数量不足", "ServerErrorCode252": "兑换异常，请稍后再试", "ServerErrorCode253": "兑换额度 20 美元 ~ 100W 美元", "ServerErrorCode254": "您的保险箱美元不足", "ServerErrorCode255": "房间准时开启", "ServerErrorCode256": "房间已解散", "ServerErrorCode257": "金币兑换美元, 两次兑换至少需间隔%s分钟", "ServerErrorCode280": "助力码红包不存在", "ServerErrorCode281": "助力码红包已经过期", "ServerErrorCode282": "助力人数已经达到,不需要助力", "ServerErrorCode283": "助力玩家已助力", "ServerErrorCode284": "助力玩家未达到,不能领取", "ServerErrorCode285": "领取者不正确", "ServerErrorCode286": "今日助力已达上限", "ServerErrorCode287": "不能给自己助力", "ServerErrorCode288": "请先注册游戏账号", "ServerErrorCode291": "本周的助力机会已用完", "ServerErrorCode292": "注册时间超过%s天的用户才可以助力", "ServerErrorCode293": "充值用户才可以助力", "ServerErrorCode501": "很抱歉，我们在您所在地区无法提供服务", "ServerErrorCode502": "未通过高级IP/GPS检测。", "ServerErrorCode503": "坐下失败，请您换一个桌子尝试", "ServerErrorCode504": "保位离桌失败,该功能今日使用次数已达上限", "ServerErrorCode505": "GAMEUUID 不存在", "ServerErrorCode506": "您已发起退出牌局，弃牌或本手结束后将退出", "ServerErrorCode508": "特邀牌手即将上桌", "ServerErrorCode509": "不支持解说员上桌", "ServerErrorCode512": "%s后可重新进入房间", "ServerErrorCode513": "%s后可重新入座", "ServerErrorCode515": "带入不得小于退出牌桌时的筹码量，请补充后再试。", "ServerErrorCode1002": "该房间即将解散", "ServerErrorCode1201": "本手结束后，退出房间", "ServerErrorCode1204": "超过本桌携带上限，暂时无需带入", "ServerErrorCode1206": "操作失败,请稍后重试", "ServerErrorCode1207": "猜手牌重复下注", "ServerErrorCode1208": "猜手牌下注超时", "ServerErrorCode1209": "余额不足，猜手牌下注失败", "ServerErrorCode1210": "猜手牌设置续投失败", "ServerErrorCode1211": "猜手牌投注选项无效", "ServerErrorCode1212": "猜手牌未知错误", "ServerErrorCode1213": "很抱歉，您所在地区暂不提供该服务", "ServerErrorCode1216": "使用表情过于频繁，请稍候再试", "ServerErrorCode1249": "新增异常-越南玩家不能进入", "ServerErrorCode1250": "围观时长已达到上限，请尝试其它桌", "ServerErrorCode1251": "未找到符合条件的房间", "ServerErrorCode1252": "已点赞", "ServerErrorCode1254": "此位置预留给特邀玩家，系统将在正式开始前5-10分钟清空此座位", "ServerErrorCode1255": "该座位已被预订，%s后明星桌正式开始", "ServerErrorCode1256": "特邀玩家占位中，%s后特邀玩家未上线，将释放", "ServerErrorCode1257": "选择打赏玩家不是明星, 不能打赏", "ServerErrorCode1258": "不是正确的打赏礼物id或者打赏的数量不对", "ServerErrorCode1262": "明星未入桌不能打赏", "ServerErrorCode1263": "不能打赏自己", "ServerErrorCode1302": "抱歉，当前级别没有合适的桌子", "ServerErrorCode31121": "预留座位，请更换其他牌桌", "pop_silence_title": "提示", "pop_silence_tips1": "您当前游戏太激烈了", "pop_silence_tips2": "来日方长，休息一下吧", "pop_silence_tips3": "您正处在冷静期", "pop_silence_btn_quit": "退出游戏", "pop_silence_btn_continue": "继续游戏", "CowboyExit_bg_exit_text": "您确定要退出游戏吗?", "InquireView_content_gold_coin": "金币", "USDTView_title_label": "转换", "USDTView_exchange": "转换", "USDTView_exchange_coin_label": "兑换金币", "USDTView_exchange_usdt_label": "兑换美元", "USDTView_from_label": "从", "USDTView_to_label": "到", "USDTView_coin_label": "金币", "USDTView_input_num_label": "输入数量", "USDTView_exchange_num_label": "转换数量", "USDTView_all_label": "全部", "USDTView_usdt_coin_ex_label_0": "汇率 1美元 ≈ %s金币", "USDTView_usdt_coin_ex_label_1": "汇率 1金币 ≈ %s美元", "USDTView_exchange_label": "转换", "USDTView_explan_label": "兑换额度 20 美元 ~ 100W 美元\n由于汇率随时变化，显示汇率仅供参考，以提交时汇率为准\n兑换过程中不可取消", "USDTView_ex_coin_success_label": "已成功兑换 %s金币", "USDTView_ex_usdt_success_label": "已成功兑换 %s美元", "USDTView_ex_coin_error_0_usdt": "美元账户为0，不能转换金币", "USDTView_usdt_balance_label": "美元余额:", "USDTView_txt_usdt": "美元", "USDTView_exchange_btn_label": "确定兑换", "USDTView_bring_num_label": "带入分数", "USDTView_input_invalid_num_label": "请输入有效数值", "USDTView_usdt_chanage_1": "使用%s美元兑换%s金币", "USDTView_usdt_chanage_2": "使用%s美元兑换%s菠萝蜜分数", "USDTView_usdt_change_free_tips": "剩余免费次数：%s", "USDTView_usdt_change_fee_tips": "手续费%s，实际兑换约%s美元", "USDTView_usdt_change_point_tips": "%s积分兑换%s金币手续费", "USDTView_usdt_change_coin_tips": "金币余额", "USDTView_usdt_change_usdt_tips": "美元余额", "USDTView_usdt_change_title_tips": "账户总余额", "USDTView_exchange_tips_label": "每个账号每天有%s次免费兑换机会\n每天0点刷新免费机会，不累积\n每次兑换之间间隔需超过%s分钟", "Humanboy_advancedSetting_desc": "下注按钮设置, 请选择%d个不同的下注按钮", "Humanboy_advancedSetting_auto": "续投按钮设置", "Humanboy_advancedSetting_opt_normal": "普通续投按钮", "Humanboy_advancedSetting_opt_advance": "高级续投按钮", "Humanboy_advancedSetting_opt_advance_extra": "(可自定义续投局数)", "Cowboy_fuhao_no_text": "富豪NO.%d", "Cowboy_shensuanzi_text": "神算子", "CowBoy_btn_desc_auto_cancel": "取消", "CowBoy_btn_desc_auto_recharge": "充值", "CowBoy_btn_desc_auto_count": "续投%d局", "CowBoy_btn_desc_auto_using_count": "已投%d/%d局", "CowBoy_btn_desc_stop_auto_bet": "终止", "CowBoy_btn_desc_resume_auto_bet": "继续", "CowBoy_btn_desc_exit_game": "退出", "CowBoy_btn_desc_resume_game": "继续", "CowBoy_btn_desc_switch_auto_bet": "仍要切换", "Cowboy_auto_bet_stop_tips": "您确定终止续投吗？\n当前续投进度%d/%d", "Cowboy_auto_bet_switch_tips": "您正在使用续投, 切换将终止当前续投进度%d/%d", "Cowboy_auto_bet_exit_tips": "您正在使用续投, 退出将终止当前续投进度%d/%d", "Cowboy_last20_text": "近20局", "Humanboy_list_online": "在线玩家:", "Humanboy_list_change_rank": "切换榜单", "Humanboy_list_rank_0": "今日盈利榜", "Humanboy_list_rank_1": "今日连胜榜", "Humanboy_list_rank_2": "今日单局盈利榜", "Humanboy_list_rank_3": "历史连胜榜", "Humanboy_list_rank_4": "历史单局盈利榜", "Humanboy_list_rank_5": "历史单日盈利榜", "Humanboy_list_profit": "盈利金额:", "Humanboy_list_frequency_time": "连胜次数:", "Humanboy_list_frequency": "%d连胜", "Humanboy_list_myrank": "我的排名", "Cowboy_ludan_guide_text": "点击查看详细路单", "minigame_rebate_top_rank": "第%s名", "minigame_rebate_top_rank_button": "您超越了%s，成为第%d名", "minigame_rebate_top_rank_tips": "第%d名", "minigame_rebate_no_rank": "未入榜", "minigame_rebate_toast_top_tips": "前%d名", "minigame_rebate_toast_top_no_enemy": "您成为第%d名", "minigame_rebate_period_desc": "%s～%s\n活動期間每天領", "minigame_rebate_text_month": "月", "minigame_rebate_text_period": "%s%s~%s%s", "minigame_rebate_surpassed_reward": "未进排行榜的前%d名将获得%s元奖励补贴。", "minigame_rebate_daily_ranking": "总排行榜:", "minigame_rebate_daily_ranking_daily": "每日排行榜:", "minigame_rebate_claim_note": "<color=#9CB8A3>注: 请及时领取奖励，以免活动过期后无法领取</color>", "minigame_rebate_rank_1_reward": "第一名奖励: %s", "minigame_rebate_rank_2_reward": "第二名奖励: %s", "minigame_rebate_rank_3_reward": "第三名奖励: %s", "minigame_rebate_rank_4_reward": "第四名奖励: %s", "minigame_rebate_rank_5_reward": "第五名奖励: %s", "minigame_rebate_rank_6_reward": "第六名奖励: %s", "minigame_rebate_rank_7_reward": "第七名奖励: %s", "minigame_rebate_rank_8_reward": "第八名奖励: %s", "minigame_rebate_rank_9_reward": "第九名奖励: %s", "minigame_rebate_rank_10_reward": "第十名奖励: %s", "minigame_rebate_reward_popup": "<color=#ffffff>恭喜获得</c>%s", "minigame_rebate_receive_reward_success": "恭喜获得%s", "minigame_number_date": "日", "minigame_number_month": "1月 2月 3月 4月 5月 6月 7月 8月 9月 10月 11月 12月", "minigame_total_betting": "已投注: ", "minigame_cowboy_rebate_today": "今天", "minigame_cowboy_rebate_day": "周日 周一 周二 周三 周四 周五 周六", "minigame_cowboy_rebate_expired": "已过期", "minigame_cowboy_rebate_title": "限时挑战", "minigame_cowboy_rebate_content": "限定时间在<color=#ECD27D>德州牛仔</color>小游戏中投注，满足对应额度<color=#ECD27D>即可领取奖励。</color>", "minigame_cowboy_rebate_title_activity_2": "限时返利", "minigame_cowboy_rebate_content_activity_2": "在活动时间内, 每天在<color=#ECD27D>德州牛仔, 扑克大师或百人德州</color>中投注满%s金币即可获得<color=#ECD27D>当天全部奖励。</color>\n<color=#9CB8A3>注: 请及时领取奖励，以免活动过期后无法领取</color>", "minigame_cowboy_rebate_content_activity_2_one_game": "在活动时间内, 每天在<color=#ECD27D>德州牛仔</color>中投注满%s金币即可获得<color=#ECD27D>当天全部奖励。</color>\n<color=#9CB8A3>注: 请及时领取奖励，以免活动过期后无法领取</color>", "minigame_cowboy_rebate_title_activity_3": "挑战星期几", "minigame_cowboy_rebate_content_activity_3": "限定时间在<color=#ECD27D>德州牛仔</color>小游戏中投注，前20% (%d) 将获得88元奖励补贴。排行前10名的将会获得额外奖励。\n<color=#FFF79B>注: 请及时领取奖励，以免活动过期后无法领取</color>", "minigame_cowboy_rebate_title_activity_4": "总投注排行榜", "minigame_cowboy_rebate_title_activity_4_daily": "限时排行榜", "minigame_cowboy_rebate_content_activity_4": "限定时间在<color=#ECD27D>德州牛仔</color>小游戏中投注，排行前%d名的将会获得固定奖励。%s获奖者请再次进入此活动界面领取。", "minigame_cowboy_rebate_content_activity_4_daily": "%s每天限定时间<color=#ECD27D>德州牛仔</color>小游戏中投注，排行前%d名的将会获得固定奖励。%s获奖者请再次进入此活动界面领取。", "minigame_already_bet": "已投注", "day": "天", "hour": "小时", "minute": "分钟", "seconds": "秒", "CountDown": "倒计时:", "Claimed": "已领取", "Unclaimed": "待领取", "string_comma": "、", "string_and": "和", "minigame_currency_type_2": "金币", "minigame_currency_type_3": "小游戏币", "Cowboy_coin_short_text": "万", "Humanboy_game_gold_short_suffix_w": "W", "Humanboy_game_gold_short_suffix_million": "<PERSON>l", "Humanboy_game_gold_short_suffix_billion": "Bil", "PokerMaster_dialog_recharge": "前往钱包进行充值", "minigame_prompt_coin_remain": "剩余", "minigame_prompt_coin_used": "已消耗", "minigame_prompt_casino_coin": "小游戏币:", "MiniGames_SYSTEM_FORCE_CLOSED": "系统关闭房间", "minigame_Title_10": "德州牛仔", "minigame_Title_30": "百人德州", "minigame_Title_50": "真人视讯", "minigame_Title_70": "扑克大师", "Rebate_betting_bonus": "投注返奖", "Rebate_all_claimed": "已领完", "betting_rebate_event_title": "小游戏限时返利", "bet_bonus_tab": "投注返利", "leaderboard_tab": "排行榜", "total_bet": "已投注:", "next_level_bonus": "下一级返利：再投", "collected": "已领取:", "Rebate_bet_bnous_countdown": "距离结束:", "Rebate_leaderboard_countdown": "结榜倒计时:", "Rebate_has_end_bet_bonus": "活动已结束", "Rebate_has_end_leaderboard": "截止至 %s", "Rebate_start_in": "开始时间: %s", "Rebate_leaderboard_no_ranking_yet": "暂无排名", "Rebate_claim_reward_success": "成功领取%s，请前往【我的钱包】查看", "Rebate_rank": "排名", "Rebate_player": "玩家", "Rebate_reward": "奖励", "Rebate_bet_amount": "实时投注额", "Rebate_not_rank": "未入榜", "Rebate_my_self": "我就是本人"}}}}
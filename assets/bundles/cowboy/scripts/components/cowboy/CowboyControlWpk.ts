/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import { CowboyControl, CowboyWayOutInfo } from './CowboyControl';
import * as domain from '../../domain/cowboy-domain-index';
// import { MiniGameCommonDef } from '../../common/cowboy-define';

import * as cr from '../../../../common-resource/scripts/common-resource';
import { DialogHubControl } from '../../../../common-resource/scripts/components/DialogHubControl';
import { CommonDialogControl } from '../../../../common-resource/scripts/components/CommonDialogControl';
import AvatarControl = cr.components.AvatarControl;
import HumanboyAdvancedAutoControl = cr.components.HumanboyAdvancedAutoControl;
import ConcreteAdvancedAuto = cr.components.ConcreteAdvancedAuto;
import IMiniGameDialog = cr.components.IMiniGameDialog;
import MiniGameDialog = cr.components.MiniGameDialog;
import IMiniGameDialogConfig = cr.components.IMiniGameDialogConfig;
import ConcreteMiniGameDialog = cr.components.ConcreteMiniGameDialog;
import HumanboyDialogControl = cr.components.HumanboyDialogControl;
import MiniGameMenuControl = cr.components.MiniGameMenuControl;
import MiniGameRuleControl = cr.components.MiniGameRuleControl;
import MiniGameAudioSettingControl = cr.components.MiniGameAudioSettingControl;
import MiniGamePlayerListControl = cr.components.MiniGamePlayerListControl;
import MiniGameGuideControl = cr.components.MiniGameGuideControl;
import MiniGameAdvancedSettingControl = cr.components.MiniGameAdvancedSettingControl;
import HumanboyBetCoinControl = cr.components.HumanboyBetCoinControl;
import MiniGameAdvancedAuto = cr.components.MiniGameAdvancedAuto;
import PokerCardControl = cr.components.PokerCardControl;
import MiniGameCommonDef = cr.MiniGameCommonDef;

import { macros } from '../../common/cowboy-macros';
import * as network from '../../network/cowboy-network-index';

export class OtherPlayerHead {
    uid: number = 0;
    bg: cc.Sprite = null;
    textCoin: cc.Label = null;
    nbFlag: cc.Node = null; // 富豪/神算子
    avatarControl: AvatarControl = null;
}

export enum COWBOY_LOCAL_ZORDER {
    COWBOY_LOCAL_ZORDER_DUMMY = 0,
    COWBOY_LOCAL_ZORDER_IMG_HEAD,
    COWBOY_LOCAL_ZORDER_IMG_WIN_COUNT,
    COWBOY_LOCAL_ZORDER_COIN_NODE,
    COWBOY_LOCAL_ZORDER_ANIM_NODE,
    COWBOY_LOCAL_ZORDER_TIMELINE_NODE,
    COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_SELECT, // 高级续投选择面板
    COWBOY_LOCAL_ZORDER_REWRAD_TIP, // 中奖提示面板
    COWBOY_LOCAL_ZORDER_RED_PACKAGE, // 红包面板
    COWBOY_LOCAL_ZORDER_MENU_PANEL = 99,
    COWBOY_LOCAL_ZORDER_TOAST,
    COWBOY_LOCAL_ZORDER_GUIDE,
    COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_ADD_SELECT,
    COWBOY_LOCAL_ZORDER_DIALOGHUB,
    COWBOY_LOCAL_ZORDER_DIALOGBOX
}

export enum LayerZorder {
    Z_IDX_DUMMY = 0, // 默认

    Z_IDX_IMG_HEAD = 9, // 头像
    Z_IDX_IMG_HEAD_TXT, // 头像文本
    Z_IDX_IMG_HEAD_FLAG, // 头像标签
    Z_IDX_IMG_WIN_COUNT, // 玩家连胜

    Z_IDX_COIN_POOL, // 金币池节点
    Z_IDX_ANIM_NODE, // 动画节点
    Z_IDX_ANIM_NODE_0, // 动画节点0
    Z_IDX_ANIM_NODE_1, // 动画节点1
    Z_IDX_ANIM_NODE_2, // 动画节点2
    Z_IDX_ANIM_NODE_3, // 动画节点3

    Z_IDX_PANEL_COUNT_DOWN, // 开局倒计时面板
    Z_IDX_PANEL_ADVANCE_AUTO_SELECT, // 高级续投选择面板
    Z_IDX_PANEL_REWRAD_TIP, // 中奖提示面板
    Z_IDX_PANEL_RED_PACKET, // 红包面板
    Z_IDX_PANEL_AUTO_SELECT, // 高级续投选择面板
    Z_IDX_PANEL_RECORD, // 牌局记录面板
    Z_IDX_PANEL_SETTING, // 设置面板
    Z_IDX_PANEL_SQUINT, // 眯牌面板
    Z_IDX_PANEL_GUID, // 引导面板
    Z_IDX_PANEL_SERVER_TOAST, // 提示面板
    Z_IDX_ADVANCE_AUTO_ADD_SELECT
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class CowboyControlWpk extends CowboyControl {
    @property(cc.Prefab) countDownPrefab: cc.Prefab = null;
    // @property(cc.Prefab) menuPrefab: cc.Prefab = null;
    @property(cc.Prefab) win_count_prefab: cc.Prefab = null;

    _betBtnBot: cc.Node[] = []; // 5
    _betButtonChoice: cc.Sprite[] = []; // 5

    _betButtonNum: number = 5; // 下注按钮数量
    _betButtonPosY: number = -3;
    _betButtonPosOffsetY: number = 8;

    protected _btn_redpacket_grab: cc.Node = null;

    private _redCardTypeNode: cc.Node;
    private _blueCardTypeNode: cc.Node;

    // private _winCardTypeSkl: sp.SkeletonData = null;
    // private _loseCardTypeSkl: sp.SkeletonData = null;

    private _betCountDownAnim: cc.Node;

    private game_betBtn_PLIST: cc.SpriteAtlas = null;

    private _mapWinCountAnim: Map<number, string> = new Map();

    private _dialogHub: DialogHubControl;

    private _commonDialog: CommonDialogControl;

    protected initDialogHub(): void {
        // const dialogHubPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>('common-resource.dialog-hub');
        // const dialogHub = cc.instantiate(dialogHubPrefab);
        // // cc.game.addPersistRootNode(dialogHub);
        // this.node.addChild(dialogHub, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_DIALOGHUB);
        // this._dialogHub = dialogHub.getComponent(DialogHubControl);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        let commonDialogPrefabName = 'common-resource.common-dialog';
        // if (context.platform === 'wpk') commonDialogPrefabName = 'common-resource.wpk-common-dialog';
        const dlgPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>(commonDialogPrefabName);
        const dlg = cc.instantiate(dlgPrefab);
        // cc.game.addPersistRootNode(dlg);
        this.node.addChild(dlg, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_DIALOGHUB);
        this._commonDialog = dlg.getComponent(CommonDialogControl);
    }

    initTimelineAnims(): void {
        console.log('[3in1] CowboyControlWpk::initTimelineAnims');
        // 开局动画
        this._roundStartAnim = this.initAni(this._timelineNodeAnim, this.round_start_prefab);
        // // 出战动画
        this._fightBeginAnim = this.initAni(this._timelineNodeAnim, this.fight_begin_prefab);
        // // 开战动画
        this._fightEndAnim = this.initAni(this._timelineNodeAnim, this.fight_end_prefab);
        // // 等待下一局动画
        this._waitForNextRoundAnim = this.initAni(this._timelineNodeAnim, this.wait_for_next_round_prefab);

        this._mapWinCountAnim.set(3, 'Sanliansheng');
        this._mapWinCountAnim.set(4, 'Siliansheng');
        this._mapWinCountAnim.set(5, 'Wuliansheng');
        this._mapWinCountAnim.set(6, 'Liuliansheng');
        this._mapWinCountAnim.set(7, 'Qiliansheng');
        this._mapWinCountAnim.set(8, 'Baliansheng');
        this._mapWinCountAnim.set(9, 'Jiuliansheng');
        this._mapWinCountAnim.set(10, 'Shiliansheng');
        this._mapWinCountAnim.set(11, 'Liansheng');
    }

    initAni(parent: cc.Node, ani_prefab: cc.Prefab): cc.Node {
        // console.log('[3in1] CowboyControlWpk::initAni:' + ani_prefab.name);
        const node = cc.instantiate(ani_prefab);
        node.active = false;
        parent.addChild(node);
        return node;
    }

    initCowboyAnims(): void {
        this._heroBoy = cc.find('boy', this._gameContent);
        this._heroCow = cc.find('cow', this._gameContent);
    }

    initCards(): void {
        this._cardPanel = this._gameContent.getChildByName('public_card_panel');
        this._oriRedHandCards.push(this._cardPanel.getChildByName('handcard_red_0').getComponent(cc.Sprite));
        this._oriRedHandCards.push(this._cardPanel.getChildByName('handcard_red_1').getComponent(cc.Sprite));
        this._oriBlueHandCards.push(this._cardPanel.getChildByName('handcard_blue_0').getComponent(cc.Sprite));
        this._oriBlueHandCards.push(this._cardPanel.getChildByName('handcard_blue_1').getComponent(cc.Sprite));
        for (let i = 0; i < 2; i++) {
            let RedHandpos: cc.Vec2 = cc.v2(this._oriRedHandCards[i].node.getPosition());
            let BlueHandpos: cc.Vec2 = cc.v2(this._oriBlueHandCards[i].node.getPosition());
            this._oriRedHandCardsPos.push(RedHandpos);
            this._oriBlueHandCardsPos.push(BlueHandpos);
            this._redHandCards.push(this.createPokerCard());
            this._blueHandCards.push(this.createPokerCard());
            this._redHandCards[i].ResetFromNode(this._oriRedHandCards[i].node);
            this._blueHandCards[i].ResetFromNode(this._oriBlueHandCards[i].node);
        }

        for (let i = 0; i < 5; i++) {
            this._oriPublicCards.push(
                this._cardPanel.getChildByName(pf.StringUtil.formatC('handcard_%d', i)).getComponent(cc.Sprite)
            );
            let pos: cc.Vec2 = cc.v2(this._oriPublicCards[i].node.getPosition());
            this._oriPublicCardsPos.push(pos);
            this._publicCards.push(this.createPokerCard());
            this._publicCards[i].ResetFromNode(this._oriPublicCards[i].node);
        }

        // this._redCardType = this._cardPanel.getChildByName('red_card_type').getComponent(cc.Sprite);
        // this._blueCardType = this._cardPanel.getChildByName('blue_card_type').getComponent(cc.Sprite);
        // this._redCardTypeBg = this._cardPanel.getChildByName('red_card_type_bg').getComponent(cc.Sprite);
        // this._blueCardTypeBg = this._cardPanel.getChildByName('blue_card_type_bg').getComponent(cc.Sprite);
        // this._redCardTypeBg.node.zIndex = 1;
        // this._blueCardTypeBg.node.zIndex = 1;
        // this._redCardType.node.zIndex = 2;
        // this._blueCardType.node.zIndex = 2;
        this._redCardTypeNode = this._cardPanel.getChildByName('red_card_type_node');
        this._redCardTypeNode.setSiblingIndex(this._redCardTypeNode.parent.childrenCount - 1);
        this._blueCardTypeNode = this._cardPanel.getChildByName('blue_card_type_node');
        this._blueCardTypeNode.setSiblingIndex(this._blueCardTypeNode.parent.childrenCount - 1);

        this._mapLevelCardTypeImage.set(1, 'Gp');
        this._mapLevelCardTypeImage.set(2, 'Yd');
        this._mapLevelCardTypeImage.set(3, 'Ld');
        this._mapLevelCardTypeImage.set(4, 'St');
        this._mapLevelCardTypeImage.set(5, 'Sz');
        this._mapLevelCardTypeImage.set(6, 'Th');
        this._mapLevelCardTypeImage.set(7, 'Hl');
        this._mapLevelCardTypeImage.set(8, 'Jg');
        this._mapLevelCardTypeImage.set(9, 'Ths');
        this._mapLevelCardTypeImage.set(10, 'Ht');
    }

    initBetArea(): void {
        // ========== pkw start ==========
        let bet_content_bg_ipx = this._gameContent.getChildByName('bet_content_bg_ipx');
        let bet_content_bg = this._gameContent.getChildByName('bet_content_bg');
        // let bet_content_bg_ipad = this._gameContent.getChildByName('bet_content_bg_ipad');

        const canvas = this.getComponent(cc.Canvas);

        const fitScale = canvas.fitWidth
            ? pf.system.view.width / canvas.designResolution.width
            : pf.system.view.height / canvas.designResolution.height;

        this.eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL;
        if (pf.system.view.isNarrowScreen()) {
            let fTotalWidth = 0;
            // fTotalWidth += 2 * pf.system.view.iphoneXOffset;
            if (cc.sys.os === cc.sys.OS_IOS) {
                fTotalWidth += 2 * pf.system.view.iphoneXOffset;
            } else {
                fTotalWidth += 24;
            }
            fTotalWidth += bet_content_bg_ipx.width;
            fTotalWidth += 2 * 138; // 两个头像宽度
            if (fTotalWidth * fitScale <= pf.system.view.width) {
                this.eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW;
            }
        }

        if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            // iphoneX
            // this._isViewX = true;
            this._betContentBg = bet_content_bg_ipx.getComponent(cc.Sprite);
            if (pf.UIUtil.isValidNode(bet_content_bg)) {
                bet_content_bg.removeFromParent(true);
                bet_content_bg.destroy();
            }
            // if (pf.UIUtil.isValidNode(bet_content_bg_ipad)) {
            //     bet_content_bg_ipad.removeFromParent(true);
            //     bet_content_bg_ipad.destroy();
            // }
        } else {
            // 普通分辨率
            this._betContentBg = bet_content_bg.getComponent(cc.Sprite);
            if (pf.UIUtil.isValidNode(bet_content_bg_ipx)) {
                bet_content_bg_ipx.removeFromParent(true);
                bet_content_bg_ipx.destroy();
            }
            // if (pf.UIUtil.isValidNode(bet_content_bg_ipad)) {
            //     bet_content_bg_ipad.removeFromParent(true);
            //     bet_content_bg_ipad.destroy();
            // }
        }

        // if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
        //     // let _bgName = bTrueFullScreen
        //     //     ? macros.Dynamic_Assets.COWBOY_TABLE_NARROW_SPRITE
        //     //     : macros.Dynamic_Assets.COWBOY_TABLE_SPRITE;
        //     // if (this._isIpad) {
        //     //     _bgName = macros.Dynamic_Assets.COWBOY_TABLE_BROAD_SPRITE;
        //     // }
        //     let _bgName = macros.Dynamic_Assets.COWBOY_TABLE_SPRITE;
        //     if (this._eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
        //         _bgName = macros.Dynamic_Assets.COWBOY_TABLE_NARROW_SPRITE;
        //     } else if (this._eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
        //         _bgName = macros.Dynamic_Assets.COWBOY_TABLE_BROAD_SPRITE;
        //     }
        //     pf.addressableAssetManager.loadAsset(_bgName).then((asset: cc.SpriteFrame) => {
        //         this._betContentBg.node.getComponent(cc.Sprite).spriteFrame = asset;
        //     });
        // }

        this._betContentBg.node.active = true;
        this._coinNodeByArea = [];

        // 下注区域映射
        // this._mapBetOptionArea.set(network.BetZoneOption.RED_WIN, 0);
        // this._mapBetOptionArea.set(network.BetZoneOption.EQUAL, 1);
        // this._mapBetOptionArea.set(network.BetZoneOption.BLUE_WIN, 2);

        // this._mapBetOptionArea.set(network.BetZoneOption.HOLE_3_TONG_SAME_SHUN, 3);
        // this._mapBetOptionArea.set(network.BetZoneOption.FIVE_NONE_1DUI, 4);
        // this._mapBetOptionArea.set(network.BetZoneOption.FIVE_2DUI, 5);
        // this._mapBetOptionArea.set(network.BetZoneOption.HOLE_SAME, 6);
        // this._mapBetOptionArea.set(network.BetZoneOption.HOLE_A, 7);

        // this._mapBetOptionArea.set(network.BetZoneOption.FIVE_3_SHUN_TONG_HUA, 8);
        // this._mapBetOptionArea.set(network.BetZoneOption.FIVE_3_2, 9);
        // this._mapBetOptionArea.set(network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4, 10);

        // 按区域索引升序
        // let vAreaIdx: number[] = [];
        // do {
        //     this._mapBetOptionArea.forEach((value: number, key: number) => {
        //         vAreaIdx.push(value);
        //     });

        //     vAreaIdx.sort((a: number, b: number): number => {
        //         return a > b ? 1 : -1;
        //     });
        // } while (0);

        // // 对应区域赔率数组
        // let vAreaIdxLen = vAreaIdx.length;

        // for (let i = 0; i < vAreaIdxLen; ++i) {
        //     let text_self_bet_num = this._betContentBg.node.getChildByName('text_self_bet_num_' + i);
        //     let text_total_bet_num = this._betContentBg.node.getChildByName('text_total_bet_num_' + i);
        //     this._textSelfBetNum.push(text_self_bet_num.getComponent(cc.Label));
        //     this._textTotalBetNum.push(text_total_bet_num.getComponent(cc.Label));
        //     this._oriTextSelfBetNumPos.push(text_self_bet_num.getPosition());
        //     this._oriTextTotalBetNumPos.push(text_total_bet_num.getPosition());

        //     let fnt_odd = this._betContentBg.node.getChildByName('fnt_odd_' + i).getComponent(cc.Label);
        //     fnt_odd.string = '';
        //     this._textBetAreaOdds.push(fnt_odd);
        // }

        // for (let i = 0; i < vAreaIdxLen; ++i) {
        //     let iAreaIdx = vAreaIdx[i];
        //     let betArea = this._betContentBg.node.getChildByName(pf.StringUtil.formatC('bet_area_%d', iAreaIdx));
        //     let coin_content = betArea.getChildByName('coin_content');

        //     betArea.on(cc.Node.EventType.TOUCH_END, (): void => {
        //         this.betAreaClicked(iAreaIdx);
        //     });

        //     this._betAreas.push(betArea);
        //     this._betCoinContents.push(coin_content);
        //     this._coinNodeByArea.push([]);

        //     let winFlag = betArea.getChildByName('win_flag');
        //     this._sprBetAreaWinFlags.push(winFlag.getComponent(cc.Sprite));

        //     // 初始化路子信息
        //     this._initWayOutInfoByAreaIdx(iAreaIdx);
        // }
        // ========== pkw end ==========

        // ========== wpk start ==========
        // const bet_content_bg_ipx = this._gameContent.getChildByName('bet_content_bg_ipx');
        // const bet_content_bg = this._gameContent.getChildByName('bet_content_bg');
        // const bTrueFullScreen = this._checkIsTrueFullScreen();

        // if (bTrueFullScreen && cv.config.IS_FULLSCREEN) {
        //     this._bTrueFullScreen = true;
        //     this._betContentBg = bet_content_bg_ipx.getComponent(cc.Sprite);
        //     if (bet_content_bg) {
        //         bet_content_bg.destroy();
        //     }
        // } else {
        //     this._bTrueFullScreen = false;
        //     this._betContentBg = bet_content_bg.getComponent(cc.Sprite);
        //     if (bet_content_bg_ipx) {
        //         bet_content_bg_ipx.destroy();
        //     }
        // }

        // if (cv.config.getCurrentLanguage() !== cv.Enum.LANGUAGE_TYPE.zh_CN) {
        //     cv.resMgr.setSpriteFrame(
        //         this._betContentBg.node,
        //         'en_US/game/cowboy/' + (bTrueFullScreen ? 'bet_content_ipx' : 'bet_content')
        //     );
        // }

        this._betContentBg.node.active = true;

        // TODO: not dealt with
        // this.adaptIPhone14Pro();

        // 下注区域映射
        this.initBetOptionArea();
        this.initBetAreaContent();
        // ========== wpk end ==========
    }

    initBetCountDown(): void {
        this._betCountDown = this._gameContent.getChildByName('bet_count_down_bg');
        this._textCountDown = this._betCountDown.getChildByName('text_count_down').getComponent(cc.Label);

        this._betCountDownAnim = this.initAni(this._betCountDown, this.countDownPrefab);
        this._betCountDownAnim.setSiblingIndex(0);
    }

    initPlayersInfo(): void {
        // 自己
        this.self_panel = this._bottomPanel.getChildByName('self_panel');
        this._textNickName = this.self_panel.getChildByName('text_nickname').getComponent(cc.Label);
        this._textCoin = this.self_panel.getChildByName('text_coin').getComponent(cc.Label);
        this._selfHeadBg = this.self_panel.getChildByName('img_head_box');
        this._selfCoin = this.self_panel.getChildByName('own_coin').getComponent(cc.Sprite);
        this.selfAvatar = this.self_panel.getChildByName('Avatar').getComponent(AvatarControl);

        // 其他玩家
        this._leftPlayerPanel = this.node.getChildByName('leftPlayerPanel');
        this._rightPlayerPanel = this.node.getChildByName('rightPlayerPanel');
        this._rebate_float_button = this.node.getChildByName('rebate_float_button');
        this.updateRebateFloatButtonPosition();

        for (let i = 0; i < 4; i++) {
            {
                const player = new OtherPlayerHead();
                const playerBg = this._leftPlayerPanel
                    .getChildByName(pf.StringUtil.formatC('player_%d', i))
                    .getComponent(cc.Sprite);
                player.bg = playerBg;
                player.textCoin = this._leftPlayerPanel.getChildByName('text_coin_' + i).getComponent(cc.Label);
                if (i === 0) {
                    player.nbFlag = playerBg.node.getChildByName('nb_flag');
                    player.nbFlag.zIndex = 11;
                }
                const avatar = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.AVATAR));
                playerBg.node.addChild(avatar);
                avatar.setPosition(avatar.position.x, avatar.position.y + macros.AVATAR_LOCAL_OFFSET);
                player.avatarControl = avatar.getComponent(AvatarControl);
                this._otherPlayerHeads.push(player);
            }
            {
                const player = new OtherPlayerHead();
                const playerBg = this._rightPlayerPanel
                    .getChildByName(pf.StringUtil.formatC('player_%d', i))
                    .getComponent(cc.Sprite);
                player.bg = playerBg;
                player.textCoin = this._rightPlayerPanel.getChildByName('text_coin_' + i).getComponent(cc.Label);
                if (i === 0) {
                    player.nbFlag = playerBg.node.getChildByName('nb_flag');
                    player.nbFlag.zIndex = 11;
                }
                const avatar = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.AVATAR));
                playerBg.node.addChild(avatar);
                avatar.setPosition(avatar.position.x, avatar.position.y + macros.AVATAR_LOCAL_OFFSET);
                player.avatarControl = avatar.getComponent(AvatarControl);
                this._otherPlayerHeads.push(player);
            }
        }
    }

    initWinFlagAnims(): void {
        const len = this._betAreas.length;
        // win旗子动画
        for (let i = 0; i < len; i++) {
            //   const winNode = this._betAreaContents[i].winFlag;
            const winNode = this._betAreas[i].getChildByName('win_flag');
            const winAnim: cc.Node = this.initAni(
                this.node,
                pf.addressableAssetManager.getAsset(macros.Assets.WIN_FLAG)
            );
            winAnim.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ANIM_NODE;
            winAnim.setPosition(
                winAnim.parent.convertToNodeSpaceAR(winNode.parent.convertToWorldSpaceAR(cc.v2(0 + winNode.x, -5)))
            );

            this._winFlagAnims.push(winAnim);
        }
    }

    initBetButtons(): void {
        // TODO: pick a better place
        this.game_betBtn_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.BET_BUTTON_ATLAS);

        this._betButtons = [];
        this._betButtonTexts = [];
        this._betButtonMasks = [];

        for (let betBtnIdx = 0; betBtnIdx < this._betButtonNum; betBtnIdx++) {
            const btnBet = this._panel_betbtn.getChildByName('btn_bet_' + betBtnIdx).getComponent(cc.Button);
            this._betButtons.push(btnBet);
            this._betBtnBot.push(this._panel_betbtn.getChildByName('btn_bot_' + betBtnIdx));
            this._betButtonTexts.push(btnBet.node.getChildByName('textBet'));
            this._betButtonMasks.push(btnBet.node.getChildByName('imgMask').getComponent(cc.Sprite));
            this._betButtonChoice.push(btnBet.node.getChildByName('choice').getComponent(cc.Sprite));

            btnBet.node.on(
                'click',
                (event: cc.Event): void => {
                    console.log('GameCowboyScene btnBet %d clicked', betBtnIdx);
                    this.betButtonSelected(betBtnIdx);
                    pf.audioManager.playSoundEffect(macros.Audio.PRESS);
                },
                this
            );
        }

        this._betButtonPosY = this._betButtons[0].node.y;

        // 初始化高级续投面板
        if (!this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_ADVANCED_AUTO)
            );
            this.node.addChild(this._humanboyAdvancedAuto, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_SELECT);
        }

        // 续投按钮
        this._btnBetAuto = this._panel_betbtn.getChildByName('btn_bet_auto').getComponent(cc.Button);
        this._btnBetAuto.node.on('click', (event: cc.Event) => {
            pf.audioManager.playSoundEffect(macros.Audio.PRESS);

            switch (this._eAutoBtnStyle) {
                // 常规续投点击
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                    // cv.getCowboyNet().RequestAutoBet();
                    this._cowboyRoom.autoBet().then(() => {
                        this.OnAutoBetSucc();
                    });
                    break;
                // 高级续投已激活(再次点击 弹出高级续投选项面板)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                    {
                        // this._humanboyAdvancedAuto?.adaptSelectPanelPos(this._btnBetAuto.node);
                        // this._humanboyAdvancedAuto?.showSelectPanel(true);
                        const miniGameAdvanceAuto =
                            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl);
                        const advanceAuto = new ConcreteAdvancedAuto(miniGameAdvanceAuto);
                        advanceAuto.adaptSelectPanelPos(this._btnBetAuto.node);
                        advanceAuto.showSelectPanel(true);
                    }
                    break;
                // 高级续投中(再次点击取消)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                    {
                        // let dialogNode = null;
                        // if ((<any>window).CurrentUserInfo.user.wasUserInDiamondGame) {
                        //     dialogNode = cc.instantiate(this.HumanboyDialog_prefab);
                        // } else {
                        //     dialogNode = cc.instantiate(this.HumanboyDialog_prefab2);
                        // }
                        const iUsedAutoBetCount = this._cowboyRoom.betSettings.usedAutoBetCount;
                        const iSelectAutoBetCount = this._cowboyRoom.betSettings.selectAutoBetCount;
                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_DIALOG)
                        );
                        this.node.addChild(dialogNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST);
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);

                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent: pf.StringUtil.formatC(
                                pf.languageManager.getString('Cowboy_auto_bet_stop_tips_zh_cn'),
                                iUsedAutoBetCount,
                                iSelectAutoBetCount
                            ),
                            stringLeftBtn: pf.languageManager.getString('CowBoy_btn_desc_stop_auto_bet_zh_cn'),
                            stringRightBtn: pf.languageManager.getString('CowBoy_btn_desc_resume_auto_bet_zh_cn'),
                            cbLeftBtn: (dialog: IMiniGameDialog) => {
                                // cv.cowboyNet.ReqCancelAdvanceAutoBet();
                                this._cowboyRoom.cancelAdavnceAutoBet();
                            },
                            cbRightBtn: (dialog: IMiniGameDialog) => {
                                miniGameDialog?.close();
                            },
                            isReachedMax: this._cowboyRoom.betSettings.reachLimitBet,
                            legacyDialog: legacyDialog,
                            isShowBtnCenter: true,
                            stringCenterButton: pf.languageManager.getString('MiniGame_AddAutoBet_Text_zh_cn'),
                            cbCenterBtn: (dialog: MiniGameDialog) => {
                                this.showAutoAddBetList(dialog);
                            },
                            onUpdateContent: (dialog: IMiniGameDialog) => {
                                if (legacyDialog) {
                                    legacyDialog.txt_content.string = pf.StringUtil.calculateAutoWrapString(
                                        legacyDialog.txt_content.node,
                                        pf.StringUtil.formatC(
                                            pf.languageManager.getString('Cowboy_auto_bet_stop_tips'),
                                            this._cowboyRoom.betSettings.usedAutoBetCount,
                                            this._cowboyRoom.betSettings.selectAutoBetCount
                                        )
                                    );
                                }
                                if (this._cowboyRoom.betSettings.reachLimitBet) {
                                    miniGameDialog?.blockCenterButton();
                                }
                            }
                        };
                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                    }
                    break;
                default:
                    break;
            }
        });

        // 清屏按钮
        this._btnBetClean = this._panel_betbtn.getChildByName('btn_bet_clean').getComponent(cc.Button);
        this._btnBetClean.node.on('click', (event: cc.Event) => {
            this.clearAllBetAreaCoins();
        });
    }

    resetAllBetButtons(): void {
        const len = this._betButtons.length;
        for (let i = 0; i < len; i++) {
            this._betButtonTexts[i].active = true;
            this._betButtonMasks[i].node.active = false;
            this._betButtonChoice[i].node.active = false;
            this._betButtons[i].enabled = true;
            this._betButtons[i].node.setScale(1);
        }
        this._curBetButtonIdx = -1;
    }

    betButtonSelected(betBtnIdx: number, ignoreCheckCoin: boolean = false): void {
        // 未完
        this.resetAllBetButtons();
        if (!ignoreCheckCoin) {
            this._updateBetButtonState();
        }

        if (betBtnIdx < 0 || betBtnIdx > this._betButtonNum - 1) {
            return;
        }
        this._curBetButtonIdx = betBtnIdx;

        const vBetCoinOption = this._cowboyRoom.betSettings.betCoinOptions;

        for (let i = 0; i < this._betButtons.length; i++) {
            const choice = this._betButtons[i].node.getChildByName('choice');
            const _coin = pf.StringUtil.clientGoldByServer(vBetCoinOption[i]);

            if (betBtnIdx === i) {
                this.setCoinChipByMount(1, this._betButtons[i], _coin);
                choice.active = true;
                this._betButtons[i].node.setScale(1.0);
                this._betButtons[i].node.y = this._betButtonPosY + this._betButtonPosOffsetY;
                this.setCoinText(this._betButtonTexts[i], _coin, false, true);
            } else {
                this.setCoinChipByMount(2, this._betButtons[i], _coin);
                choice.active = false;
                if (_coin >= this._cowboyRoom.llCoinUICritical) {
                    // 金砖
                    this._betButtons[i].node.setScale(1.0);
                } else {
                    this._betButtons[i].node.setScale(0.9); // 非选中的金币缩小
                }
                this._betButtons[i].node.y = this._betButtonPosY;
                this.setCoinText(this._betButtonTexts[i], _coin, false, false);
            }
        }
    }

    _updateBetButtonState(): void {
        // 检测下注按钮禁用与否
        const vBetCoinOption = this._cowboyRoom.betSettings.betCoinOptions; // 房间下注级别

        const curCoin = this._cowboyRoom.selfPlayer.curCoin; // 当前自身携带金币
        const vBetCoinOption_len = pf.StringUtil.getArrayLength(vBetCoinOption);

        // 检测下注按钮可触摸与否
        const bEffective: boolean =
            this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0;

        for (let i = 0; i < vBetCoinOption_len; ++i) {
            // 钱是否够按钮上的金额
            const imgMask = this._betButtons[i].node.getChildByName('imgMask');
            imgMask.setScale(1.0);
            const llAmountLevel = pf.StringUtil.clientGoldByServer(vBetCoinOption[i]);
            const _maxText = this._curBetButtonIdx === i && bEffective;

            if (curCoin >= vBetCoinOption[i]) {
                this._betButtons[i].interactable = true;
                this._betButtons[i].enabled = true;
                this.setCoinText(this._betButtonTexts[i], llAmountLevel, false, _maxText);
                imgMask.active = false;
            } else {
                this._betButtons[i].interactable = false;
                this._betButtons[i].enabled = false;
                this.setCoinText(this._betButtonTexts[i], llAmountLevel, false, _maxText);
                imgMask.active = true;
                if (this._curBetButtonIdx === i && bEffective) {
                    // 当前选中的筹码，金币不足，遮罩放大
                    imgMask.setScale(1.25);
                }
            }

            if (this._curBetButtonIdx === i) {
                if (!bEffective) {
                    this.setCoinChipByMount(2, this._betButtons[i], llAmountLevel);
                } else {
                    this.setCoinChipByMount(1, this._betButtons[i], llAmountLevel);
                }
            }
        }

        const betButtons_len: number = pf.StringUtil.getArrayLength(this._betButtons);

        for (let i = 0; i < betButtons_len; ++i) {
            this._betButtonMasks[i].node.active = !bEffective;

            // 如果金币不足的 也设置为不可选
            if (this._betButtons[i].enabled === false) {
                this._betButtonMasks[i].node.active = true;
            }

            this._betButtonChoice[i].node.active = false;
            this._betButtonMasks[i].enabled = true;
            this._betButtons[i].enabled = bEffective;
        }

        // 可以下注阶段
        if (bEffective && this._curBetButtonIdx !== -1) {
            this._betButtonChoice[this._curBetButtonIdx].node.active = true;
            this._betButtons[this._curBetButtonIdx].node.setScale(1.0);
            this._betButtons[this._curBetButtonIdx].node.y = this._betButtonPosY + this._betButtonPosOffsetY;
        }

        // 非下注阶段
        if (!bEffective && this._curBetButtonIdx !== -1) {
            const llAmountLevel = pf.StringUtil.clientGoldByServer(vBetCoinOption[this._curBetButtonIdx]);

            if (llAmountLevel >= this._cowboyRoom.llCoinUICritical) {
                // 金砖
                this._betButtons[this._curBetButtonIdx].node.setScale(1.0);
            } else {
                this._betButtons[this._curBetButtonIdx].node.setScale(0.9);
            }

            this._betButtons[this._curBetButtonIdx].node.y = this._betButtonPosY;
        }

        // 更新续投按钮状态
        this._updateAutoBetBtnStatus();

        // 更新清屏按钮状态
        this._updateCleanBtnStatus();

        // this.adaptiveBetBtnPanel();
    }

    createFlyCoin(areaIdx: number, amount: number, isWin?: boolean): cc.Sprite {
        const checkedIsWin = isWin === true ? true : false;
        // let isCircleCoin = this.isCircleCoin(coin);
        let node: cc.Node = null;

        let len = pf.StringUtil.getArrayLength(this._coinNodeByArea[areaIdx]);
        if (len >= this._areaCoinMax[areaIdx]) {
            let removeNode = this._coinNodeByArea[areaIdx][0];
            this.nodePutToPool(removeNode);
            this._coinNodeByArea[areaIdx].splice(0, 1);
        }

        // if (isCircleCoin) {
        //     if (this._circlePool.size() > 0) {
        //         node = this._circlePool.get();
        //     } else {
        //         node = cc.instantiate(this.btnBet_0_prefab);
        //     }
        // } else {
        //     if (this._squarePool.size() > 0) {
        //         node = this._squarePool.get();
        //     } else {
        //         node = cc.instantiate(this.btnBet_3_prefab);
        //     }
        // }
        if (this._coinPool.size() > 0) {
            node = this._coinPool.get();
        } else {
            node = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.BET_COIN));
            // node.setScale(this._fFlyCoinScaleRate);
        }
        let coin: HumanboyBetCoinControl = node.getComponent(HumanboyBetCoinControl);
        // coin.setShape(this._getBetCoinShapeByAmount(amount));
        // coin.setTxtNum(pf.StringUtil.serverGoldToShowNumber(amount));
        coin.btn.enabled = false;

        if (!checkedIsWin) {
            this._coinNode.addChild(node);

            this._coinNodeByArea[areaIdx].push(node);
        }
        node.active = true;
        node.opacity = 255;

        this.setCoinText(node.getChildByName('txtBetNode'), pf.StringUtil.clientGoldByServer(amount), true);

        const _coin = pf.StringUtil.clientGoldByServer(amount);
        const spr = node.getComponent(cc.Sprite);
        const coinName = this.getCoinChipSpriteByAmount(3, _coin);
        spr.spriteFrame = this.game_betBtn_PLIST.getSpriteFrame(coinName);

        return node.getComponent(cc.Sprite);
    }

    hideAllCardsAndCardType(): void {
        this.setAllCardsVisible(false);
        this._redCardTypeNode.active = false;
        this._blueCardTypeNode.active = false;
    }

    hideWinFlagAnim(areaIdx: number): void {
        if (areaIdx >= pf.StringUtil.getArrayLength(this._winFlagAnims)) return;
        this._winFlagAnims[areaIdx].active = false;
        this._winFlagAnims[areaIdx].getComponent(sp.Skeleton).setToSetupPose();
    }

    showCowboyNormalAnim(): void {
        this._heroBoy.active = true;
        this._heroCow.active = true;
    }

    hideAllTimelineAnims(): void {
        this._roundStartAnim.active = false;
        this._fightBeginAnim.active = false;
        this._fightEndAnim.active = false;
        this._waitForNextRoundAnim.active = false;

        const len = this._winFlagAnims.length;
        for (let i = 0; i < len; i++) {
            this._winFlagAnims[i].getComponent(sp.Skeleton).setToSetupPose();
            this._winFlagAnims[i].active = false;
        }
    }

    _updateBetAmountLevel(): void {
        const vBetCoinOption = this._cowboyRoom.betSettings.betCoinOptions;
        const vBetCoinOption_len = pf.StringUtil.getArrayLength(vBetCoinOption);
        for (let i = 0; i < vBetCoinOption_len; ++i) {
            if (i < this._betButtonNum) {
                const llAmountLevel = pf.StringUtil.clientGoldByServer(vBetCoinOption[i]);
                const btnMasks = this._betButtonMasks[i];
                const btnChoice = this._betButtonChoice[i];
                const btnBot = this._betBtnBot[i].getComponent(cc.Sprite);

                if (llAmountLevel < this._cowboyRoom.llCoinUICritical) {
                    // 金币
                    // _betButtons
                    btnMasks.spriteFrame = this.game_betBtn_PLIST.getSpriteFrame('betBtnMask');
                    btnBot.spriteFrame = this.game_betBtn_PLIST.getSpriteFrame('betBtnBottom');
                    btnChoice.spriteFrame = this.game_betBtn_PLIST.getSpriteFrame('betBtnChoice');
                    btnChoice.node.setScale(1.02);
                    btnChoice.node.y = 2;

                    if (this._curBetButtonIdx === i) {
                        this._betButtons[i].node.setScale(1.0);
                    } else {
                        this._betButtons[i].node.setScale(0.9);
                    }
                } else {
                    // 金砖
                    btnMasks.spriteFrame = this.game_betBtn_PLIST.getSpriteFrame('betBtnMask1000');
                    btnBot.spriteFrame = this.game_betBtn_PLIST.getSpriteFrame('betBtnBottom1000');
                    btnChoice.spriteFrame = this.game_betBtn_PLIST.getSpriteFrame('betBtnChoice1000Big');
                    btnChoice.node.setScale(0.92);
                    btnChoice.node.y = -2;
                    this._betButtons[i].node.setScale(1.0);
                }

                this.setCoinText(this._betButtonTexts[i], llAmountLevel, false, this._curBetButtonIdx === i);

                const type = this._curBetButtonIdx === i ? 1 : 2;
                this.setCoinChipByMount(type, this._betButtons[i], llAmountLevel);
            } else {
                console.log(
                    'error!! HumanboyMainView._updateBetAmountLevel vBetCoinOption must be %d, size: %d',
                    this._betButtonNum,
                    vBetCoinOption_len
                );
            }
        }
        this._updateAutoBetBtnStyle();
        this.adaptiveBetBtnPanel();
    }

    setCoinText(node: cc.Node, num: number, isFlyCoin: boolean, bSelect = false) {
        const str = pf.StringUtil.numberToShowString(num);
        const len = pf.StringUtil.getArrayLength(str);
        node.setContentSize(34 * len, 48);
        // cv.resMgr.adaptWidget(node);
        pf.UIUtil.adaptWidget(node);
        node.removeAllChildren(true);
        node.destroyAllChildren();
        const llAmountLevel = this._cowboyRoom.llCoinUICritical;
        let scale = 0.7;
        if (isFlyCoin) {
            if (num >= llAmountLevel) {
                // 金砖筹码
                if (num >= 5000) {
                    scale = 0.4;
                } else if (num >= 1000) {
                    scale = 0.4;
                } else if (num >= 500) {
                    scale = 0.4;
                }
            } else {
                // 圆形筹码
                if (num >= 10000) {
                    scale = 0.2;
                } else if (num >= 1000) {
                    scale = 0.26;
                } else if (num >= 100) {
                    scale = 0.35;
                } else {
                    scale = 0.4;
                }
            }
        } else {
            if (num >= llAmountLevel) {
                // 金砖筹码
                if (bSelect) {
                    // 被选中
                    scale = 0.85;
                } else {
                    scale = 0.7;
                }
            } else {
                // 圆形筹码
                if (bSelect) {
                    if (num >= 1000) {
                        scale = 0.55;
                    } else if (num >= 100) {
                        scale = 0.7;
                    } else {
                        scale = 0.8;
                    }
                } else {
                    if (num >= 1000) {
                        scale = 0.48;
                    } else if (num >= 100) {
                        scale = 0.6;
                    } else {
                        scale = 0.7;
                    }
                }
            }
        }

        let _str = '';
        for (let i = 0; i < len; i++) {
            const tempNode = new cc.Node();
            tempNode.setContentSize(34, 48);
            node.addChild(tempNode);
            node.scale = scale;
            const spr = new cc.Node().addComponent(cc.Sprite);
            spr.node.color = this.getChipTextColorByCoin(num);

            if (i === 0) {
                _str = str.charAt(i);
            }
            spr.spriteFrame = this.game_betBtn_PLIST.getSpriteFrame('coin_' + str.charAt(i));
            tempNode.addChild(spr.node);
        }

        if (_str === '1') {
            // 如果是1开头的筹码，因为1比较瘦，看起来不居中。所以1开头的筹码左移两个像素。
            node.x = -2;
        } else {
            node.x = 0;
        }
    }

    setCoinChipByMount(type: number, button: cc.Button, num: number) {
        const pngName = this.getCoinChipSpriteByAmount(type, num);
        // cb.loadButtonTextureByPlist(this.game_betBtn_PLIST, button.node, pngName, pngName, pngName, pngName);
        button.normalSprite = this.game_betBtn_PLIST.getSpriteFrame(pngName);
        button.pressedSprite = this.game_betBtn_PLIST.getSpriteFrame(pngName);
        button.hoverSprite = this.game_betBtn_PLIST.getSpriteFrame(pngName);
        button.disabledSprite = this.game_betBtn_PLIST.getSpriteFrame(pngName);
    }

    getCoinChipSpriteByAmount(type: number, coin: number) {
        const llAmountLevel = this._cowboyRoom.llCoinUICritical;
        const rankValue = llAmountLevel / 1000;

        let num = '1';
        let chipStr = 'chip1';
        if (coin < 5 * rankValue) {
            num = '1'; // 索引第一张图片
        } else if (coin < 10 * rankValue) {
            num = '5';
        } else if (coin < 50 * rankValue) {
            num = '10';
        } else if (coin < 100 * rankValue) {
            num = '50';
        } else if (coin < 250 * rankValue) {
            num = '100';
        } else if (coin < 500 * rankValue) {
            num = '250';
        } else if (coin < 750 * rankValue) {
            num = '500';
        } else if (coin < 1000 * rankValue) {
            num = '750';
        } else {
            num = '1000';
        }
        // 大筹码
        if (type === 1) {
            chipStr = 'chip' + num;
        } else if (type === 2) {
            // 选中状态大筹码
            chipStr = 'chip' + num + '_choice';
        } else if (type === 3) {
            // 小筹码
            if (coin >= rankValue * 5000) {
                // 大于5000，用大的金砖筹码
                num = '5000';
            }
            chipStr = 'chip' + num + '_small';
        }

        return chipStr;
    }

    getChipTextColorByCoin(coin: number) {
        const llAmountLevel = this._cowboyRoom.llCoinUICritical;
        const rankValue = llAmountLevel / 1000;
        let _color = cc.color(55, 94, 153);

        if (coin < 5 * rankValue) {
            _color = cc.color(55, 94, 153);
        } else if (coin < 10 * rankValue) {
            _color = cc.color(43, 110, 79);
        } else if (coin < 50 * rankValue) {
            _color = cc.color(143, 103, 34);
        } else if (coin < 100 * rankValue) {
            _color = cc.color(162, 83, 34);
        } else if (coin < 250 * rankValue) {
            _color = cc.color(91, 66, 136);
        } else if (coin < 500 * rankValue) {
            _color = cc.color(114, 63, 131);
        } else if (coin < 750 * rankValue) {
            _color = cc.color(144, 70, 53);
        } else if (coin < 1000 * rankValue) {
            _color = cc.color(88, 84, 76);
        } else {
            _color = cc.color(151, 74, 36);
        }
        return _color;
    }

    protected _updateAutoBetBtnStyle(): void {
        switch (this._cowboyRoom.betSettings.autoBetLevel) {
            case pf.client.session.AutoBetLevel.Level_Normal:
                this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL);
                break;
            case pf.client.session.AutoBetLevel.Level_Advance:
                if (this._cowboyRoom.betSettings.selectAutoBetCount > 0) {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING);
                } else {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE);
                }
                break;
            default:
                break;
        }
    }

    protected _setAutoBetBtnStytle(eAutoBtnStyle: MiniGameCommonDef.eGameboyAutoBtnStyle): void {
        // 隐藏高级续投子面板
        this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).hideAdvanceAutoTips();
        this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).hideAdvanceAutoCount();
        this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).hideSelectPanel(false);
        this._eAutoBtnStyle = eAutoBtnStyle;
        switch (this._eAutoBtnStyle) {
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                this._humanboyAdvancedAuto
                    .getComponent(HumanboyAdvancedAutoControl)
                    .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
                this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).showAdvanceAutoCount();
                break;
            default:
                break;
        }
    }

    showRoundStartAnim(): void {
        this._roundStartAnim.active = true;
        this._roundStartAnim.getComponent(sp.Skeleton).setAnimation(0, 'Vs', false);
        this._roundStartAnim.getComponent(sp.Skeleton).setCompleteListener(() => {
            this._roundStartAnim.active = false;
            this.sendCardsAnim();
        });
    }

    showFightBeginAnim(): void {
        this._playSoundEffect(macros.Audio.BEGIN_BET);
        this._fightBeginAnim.active = true;
        // TODO: lang
        this._fightBeginAnim.getComponent(sp.Skeleton).setAnimation(0, 'Place_your_bets_C', false);
        this._fightBeginAnim.getComponent(sp.Skeleton).setCompleteListener(() => {
            this._fightBeginAnim.active = false;
        });
    }

    showFightEndAnim(): void {
        this.playPointAni();
        this._fightEndAnim.active = true;
        // TODO: lang
        this._fightEndAnim.getComponent(sp.Skeleton).setAnimation(0, 'Betting_is_closed_C', false);
        this._fightEndAnim.getComponent(sp.Skeleton).setCompleteListener(() => {
            this._fightEndAnim.active = false;
            // 翻牌动画
            this.scheduleOnce(() => {
                this.showHandCardsAnim();
            }, 0.3);
        });
        this.scheduleOnce(() => {
            this._playSoundEffect(macros.Audio.END_BET);
        }, 0.4);
    }

    showWaitForNextRoundInAnim(): void {
        this._waitForNextRoundAnim.active = true;
        this._waitForNextRoundAnim.getComponent(sp.Skeleton).setAnimation(0, 'Please_hold_all_bets_C_appear', false);
        this._waitForNextRoundAnim.getComponent(sp.Skeleton).addAnimation(0, 'Please_hold_all_bets_C_LOOP', true);
    }

    showWaitForNextRoundOutAnim(): void {
        if (this._waitForNextRoundAnim && this._waitForNextRoundAnim.active) {
            const track = this._waitForNextRoundAnim
                .getComponent(sp.Skeleton)
                .setAnimation(0, 'Please_hold_all_bets_C_dissolve', false);
            this._waitForNextRoundAnim.getComponent(sp.Skeleton).setTrackCompleteListener(track, () => {
                this._waitForNextRoundAnim.active = false;
            });
        }
    }

    updateCardType(): void {
        const redLevel = this._cowboyRoom.roundInfo.roundResult.redLevel;
        const blueLevel = this._cowboyRoom.roundInfo.roundResult.blueLevel;

        // 0 平 1 牛仔胜 -1 小牛胜
        if (this._cowboyRoom.roundInfo.roundResult.result === 0) {
            const redAnimName = this.getCardTypeAnimName(true, '', redLevel);
            const blueAnimName = this.getCardTypeAnimName(true, '', blueLevel);
            this._playSpineAnim(
                this._redCardTypeNode.getComponent(sp.Skeleton),
                pf.addressableAssetManager.getAsset(macros.Assets.WIN_CARD_TYPE_ANIMATION),
                redAnimName
            );
            this._playSpineAnim(
                this._blueCardTypeNode.getComponent(sp.Skeleton),
                pf.addressableAssetManager.getAsset(macros.Assets.WIN_CARD_TYPE_ANIMATION),
                blueAnimName
            );
        } else if (this._cowboyRoom.roundInfo.roundResult.result === 1) {
            const redAnimName = this.getCardTypeAnimName(true, '', redLevel);
            const blueAnimName = this.getCardTypeAnimName(false, '', blueLevel);
            this._playSpineAnim(
                this._redCardTypeNode.getComponent(sp.Skeleton),
                pf.addressableAssetManager.getAsset(macros.Assets.WIN_CARD_TYPE_ANIMATION),
                redAnimName
            );
            this._playSpineAnim(
                this._blueCardTypeNode.getComponent(sp.Skeleton),
                pf.addressableAssetManager.getAsset(macros.Assets.LOSE_CARD_TYPE_ANIMATION),
                blueAnimName
            );
        } else if (this._cowboyRoom.roundInfo.roundResult.result === -1) {
            const redAnimName = this.getCardTypeAnimName(false, '', redLevel);
            const blueAnimName = this.getCardTypeAnimName(true, '', blueLevel);
            this._playSpineAnim(
                this._redCardTypeNode.getComponent(sp.Skeleton),
                pf.addressableAssetManager.getAsset(macros.Assets.LOSE_CARD_TYPE_ANIMATION),
                redAnimName
            );
            this._playSpineAnim(
                this._blueCardTypeNode.getComponent(sp.Skeleton),
                pf.addressableAssetManager.getAsset(macros.Assets.WIN_CARD_TYPE_ANIMATION),
                blueAnimName
            );
        }
    }

    private getCardTypeAnimName(bWin: boolean, lang: string, level: number): string {
        const str1 = bWin ? 'Win' : 'Lose';
        const str2 = 'Zw'; // TODO
        const str3 = this._mapLevelCardTypeImage.get(level);
        return str1 + '_' + str2 + '_' + str3;
    }

    private _playSpineAnim(playNode: sp.Skeleton, sklData: sp.SkeletonData, animName: string): void {
        playNode.skeletonData = sklData;
        playNode.node.active = true;
        playNode.setAnimation(0, animName, false);
        playNode.setCompleteListener(() => {
            playNode.node.active = false;
        });
    }

    // 翻牌型动画
    showCardTypeAnim(): void {
        this.updateCardType();
        this._cowboyRoom.showTheNewestTrend = true;
    }

    // 牛仔输时哭的动画
    showCowboyLoseAnim(): void {
        this.showCowboyNormalAnim();
        this._playSoundEffect(macros.Audio.WIN_LOSE);

        // 0 平, 1 牛仔胜, -1 小牛胜
        if (this._cowboyRoom.roundInfo.roundResult.result === 1) {
            // 牛仔赢
            this._heroBoy.getComponent(sp.Skeleton).setAnimation(0, 'Victory_Nz', false);
            this._heroBoy.getComponent(sp.Skeleton).addAnimation(0, 'Idle_Nz_01', true);
            // 牛输
            this._heroCow.getComponent(sp.Skeleton).setAnimation(0, 'Fail_Gn', false);
            this._heroCow.getComponent(sp.Skeleton).addAnimation(0, 'Idle_Gn_01', true);
        } else if (this._cowboyRoom.roundInfo.roundResult.result === -1) {
            // 牛赢
            this._heroCow.getComponent(sp.Skeleton).setAnimation(0, 'Victory_Gn', false);
            this._heroCow.getComponent(sp.Skeleton).addAnimation(0, 'Idle_Gn_01', true);
            // 牛仔输
            this._heroBoy.getComponent(sp.Skeleton).setAnimation(0, 'Fail_Nz', false);
            this._heroBoy.getComponent(sp.Skeleton).addAnimation(0, 'Idle_Nz_01', true);
        }
    }

    showSpecialCardTypeAnim(stayLastFrame: boolean = false, lastDuration: number = 0): void {
        this.clearSpecialCardTypeAnim();
        if (!this.isResultSpecialCardType()) return;

        let specialBetOption = -1;

        // 优先判断：金刚/同花顺/皇家
        const matchArr = this._cowboyRoom.roundInfo.matchOptions;
        const matchLen = matchArr.length;
        for (let i = 0; i < matchLen; i++) {
            const areaIdx = this.getAreaIdxByBetOption(matchArr[i]);
            const betOption = this.getBetOptionByAreaIdx(areaIdx);

            if (betOption === network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4) {
                specialBetOption = betOption;
                break;
            }
        }
        if (specialBetOption < 0) {
            const matchArr = this._cowboyRoom.roundInfo.matchOptions;
            const matchLen = matchArr.length;
            for (let i = 0; i < matchLen; i++) {
                const areaIdx = this.getAreaIdxByBetOption(matchArr[i]);
                const betOption = this.getBetOptionByAreaIdx(areaIdx);

                if (betOption === network.BetZoneOption.HOLE_A) {
                    specialBetOption = betOption;
                    break;
                }
            }
        }

        if (specialBetOption < 0) return;

        // 胜利牌型
        // 0 平 1 牛仔胜 -1 小牛胜
        let winLevel = 0;
        if (this._cowboyRoom.roundInfo.roundResult.result === 0) {
            winLevel = this._cowboyRoom.roundInfo.roundResult.redLevel;
        } else if (this._cowboyRoom.roundInfo.roundResult.result === 1) {
            winLevel = this._cowboyRoom.roundInfo.roundResult.redLevel;
        } else if (this._cowboyRoom.roundInfo.roundResult.result === -1) {
            winLevel = this._cowboyRoom.roundInfo.roundResult.blueLevel;
        }

        let animName = '';
        if (specialBetOption === network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4) {
            if (winLevel === 8) {
                // 金刚
                animName = 'Tspx_Quads_C';
            } else if (winLevel === 9) {
                // 同花顺
                animName = 'Tspx_Straight_Flush_C';
            } else if (winLevel === 10) {
                // 皇家同花顺
                animName = 'Tspx_Royal_Flush_C';
            } else {
                console.log('showSpecialCardTypeAnim, show special cardtype anim error1');
                return;
            }
        } else if (specialBetOption === network.BetZoneOption.HOLE_A) {
            // 对A
            animName = 'Tspx_Pair_of_Aces_C';
        } else {
            console.log('showSpecialCardTypeAnim, show special cardtype anim error2');
            return;
        }

        let winAnim: cc.Node = this._nodeAnim.getChildByName('special_card_type_anim');
        if (!winAnim) {
            // 创建动画
            winAnim = this.initAni(this._nodeAnim, this.special_card_type_prefab);
            winAnim.name = 'special_card_type_anim';
            // this._winFlagAnims.push(winAnim);
            // this._winFlagActions.push(winAction);
        }
        winAnim.active = true;
        if (stayLastFrame) {
            winAnim.getComponent(sp.Skeleton).setAnimation(0, animName + '_IDLE', true);
            this.scheduleOnce(() => {
                winAnim.active = false;
                this.showBetWinFlagsAndFlyCoinsAnim();
            }, lastDuration);
        } else {
            this._playSoundEffect(macros.Audio.SPECIAL_CARD_TYPE_BIG);
            winAnim.getComponent(sp.Skeleton).setAnimation(0, animName, false);
            winAnim.getComponent(sp.Skeleton).setCompleteListener(() => {
                winAnim.active = false;
                this.showBetWinFlagsAndFlyCoinsAnim();
            });
        }
    }

    showWinFlagAnim(areaIdx: number): void {
        this._winFlagAnims[areaIdx].active = true;
        this._winFlagAnims[areaIdx].getComponent(sp.Skeleton).setAnimation(0, 'Win_appear', false);
        this._winFlagAnims[areaIdx].getComponent(sp.Skeleton).setAnimation(0, 'Win_Loop', true);
    }

    hideGameTips(): void {
        if (this._gameTipsBg.active === true) {
            this._gameTipsBg.getComponent(sp.Skeleton).setAnimation(0, 'Next round will start_C_dissolve', false);
            this.scheduleOnce(() => {
                this._gameTipsBg.active = false;
            }, 0.3);
        }
        this.unschedule(this.updateNextRoundTipsTimer);
    }

    showNextRoundTips(): void {
        if (this._cowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND && this._leftTime > 0) {
            this.clearRound();
            this._gameTipsBg.active = true;
            this._gameTipsBg.getComponent(sp.Skeleton).setAnimation(0, 'Next round will start_C_appear', false);
            this._gameTipsBg.getComponent(sp.Skeleton).addAnimation(0, 'Next round will start_C_LOOP', true);
            this._textGameTips.string = this._leftTime.toString();
            this.unschedule(this.updateNextRoundTipsTimer);
            this.schedule(this.updateNextRoundTipsTimer, 1.0);

            if (this._waitForNextRoundAnim.active && this._leftTime <= this._waitForNextRoundOutTheshould) {
                this.showWaitForNextRoundOutAnim();
            }
        }
    }

    updateNextRoundTipsTimer(f32Delta: number): void {
        if (this._cowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND && this._leftTime > 0) {
            this._textGameTips.string = this._leftTime.toString();

            if (this._waitForNextRoundAnim.active && this._leftTime <= this._waitForNextRoundOutTheshould) {
                this.showWaitForNextRoundOutAnim();
            }
        } else {
            this.hideGameTips();
        }
    }

    showSendCardTips(): void {
        if (this._cowboyRoom.gameState.roundState === network.RoundState.NEW_ROUND) {
            // 暂时不要提示
            // this._gameTipsBg.node.active = (true);
            this._gameTipsBg.active = false;
            // this._textGameTips.string = pf.languageManager.getString('Cowboy_game_tips_send_card_text');
        }
    }

    showOpenCardTips(): void {
        if (
            this._cowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND &&
            this._leftTime > this._betWinFlagsAndFlyCoinsDuration + this._showNextRoundDuration
        ) {
            // 暂时不要提示
            // this._gameTipsBg.node.active = (true);
            this._gameTipsBg.active = false;
            // this._textGameTips.string = pf.languageManager.getString('Cowboy_game_tips_open_card_text');
        }
    }

    // 下注倒计时开始动画
    showBetCoutDownBeginAnim(): void {
        this.updateBetCoutDown();

        // 动画
        cc.tween(this._textCountDown.node).to(0.2, { opacity: 255 }).start();
        this._betCountDownAnim.getComponent(sp.Skeleton).setAnimation(0, 'Choose your lucky box_C_appear', false);
        this._betCountDownAnim.getComponent(sp.Skeleton).addAnimation(0, 'Choose your lucky box_C_LOOP', true);
    }

    // 下注倒计时结束动画
    showBetCoutDownEndAnim(): void {
        this._updateBetButtonState();
        this.showOpenCardTips();
        cc.tween(this._textCountDown.node).to(0.2, { opacity: 0 }).start();

        // 动画
        this.scheduleOnce(() => {
            this.hideBetCountDown();
        }, 0.5);

        this._betCountDownAnim.getComponent(sp.Skeleton).setAnimation(0, 'Choose your lucky box_C_dissolve', false);

        // 开战动画
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._betCountDownEndDuration),
                cc.callFunc(() => {
                    this.showFightEndAnim();
                })
            )
        );
    }

    updateBetCoutDown(): void {
        if (this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            // 可以下注
            if (this._betCountDownAnim.active === false) {
                this._betCountDownAnim.active = true;
                this._betCountDownAnim
                    .getComponent(sp.Skeleton)
                    .setAnimation(0, 'Choose your lucky box_C_appear', false);
                this._betCountDownAnim.getComponent(sp.Skeleton).addAnimation(0, 'Choose your lucky box_C_LOOP', true);
            }
            this._textCountDown.string = pf.StringUtil.formatC('%lld', this._leftTime);
            this.schedule(this.updateBetTimer, 1.0);
        }
    }

    updateBetTimer(f32Delta: number): void {
        if (this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            this._textCountDown.string = pf.StringUtil.formatC('%lld', this._leftTime);
            this._playSoundEffect(macros.Audio.TIME_TICK);
        } else {
            this._textCountDown.string = '0';
        }
    }

    // 显示玩家胜利头像框光环动画
    showWinPlayerLightAnim(uid: number): void {
        const playerHeads: cc.Node[] = this.getPlayerHeadNodesByUid(uid);
        if (playerHeads.length === 0) {
            this.updatePlayerWinCount(uid, true);
            return;
        }

        for (const head of playerHeads) {
            // const head = playerHeads[i];

            // 自己不显示光环
            if (head === this._selfHeadBg) {
                continue;
            }

            let winPlayerLightAnim: cc.Node = head.getChildByName('win_player_light');
            if (!winPlayerLightAnim) {
                winPlayerLightAnim = this.initAni(
                    head,
                    pf.addressableAssetManager.getAsset(macros.Assets.WIN_PLAYER_LIGHT)
                );
                winPlayerLightAnim.name = 'win_player_light';
                winPlayerLightAnim.setScale(1.0);
                winPlayerLightAnim.setPosition(cc.v2(0, 22));
                winPlayerLightAnim.zIndex = 10;
            }

            winPlayerLightAnim.active = true;
            winPlayerLightAnim.getComponent(sp.Skeleton).setAnimation(0, 'Player', true);
        }

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.5),
                cc.callFunc(() => {
                    this.updatePlayerWinCount(uid, true);
                })
            )
        );
    }

    // initSpineAni(parent: cc.Node, ani_prefab: cc.Prefab): cc.Node {
    //     const node = cc.instantiate(ani_prefab);
    //     node.active = false;
    //     parent.addChild(node);
    //     return node;
    // }

    hideBetCountDown(): void {
        this._betCountDownAnim.active = false;
        this._textCountDown.string = '';
        this.unschedule(this.updateBetTimer);
    }

    initButtonEvents(): void {
        // 菜单按钮
        this._btnMenu = this.node.getChildByName('btnMenu').getComponent(cc.Button);
        this._btnMenu.node.on('click', (event: cc.Event): void => {
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            if (!this._humanboyMenu) {
                this._humanboyMenu = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_MENU));
                const menuLayer = this._humanboyMenu.getComponent(MiniGameMenuControl);
                this.node.addChild(this._humanboyMenu, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL);

                // 菜单 - 规则
                menuLayer.getBtnRule().node.on('click', (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    if (this._cowboyRule === null) {
                        // this._cowboyRule = cc.instantiate(
                        //     pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_RULE)
                        // );
                        // this._cowboyRule.setAnchorPoint(cc.v2(0.5, 0.5));
                        // this._cowboyRule.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                        // this.node.addChild(this._cowboyRule);
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.COWBOY_RULE)
                            .then((asset: cc.Prefab) => {
                                this._cowboyRule = cc.instantiate(asset);
                                this._cowboyRule.setAnchorPoint(cc.v2(0.5, 0.5));
                                this._cowboyRule.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                                this.node.addChild(this._cowboyRule);
                                this._cowboyRule.getComponent(MiniGameRuleControl).openView('');
                            });
                    } else {
                        this._cowboyRule.getComponent(MiniGameRuleControl).openView('');
                    }
                });

                // 菜单 - 音效设置
                menuLayer.getBtnSoundSetting().node.on('click', (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);
                    if (this._cowboySetting === null) {
                        // this._cowboySetting = cc.instantiate(
                        //     pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_AUDIO_SETTING)
                        // );
                        // this._cowboySetting.setAnchorPoint(cc.v2(0.5, 0.5));
                        // this._cowboySetting.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                        // this.node.addChild(this._cowboySetting);
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.COWBOY_AUDIO_SETTING)
                            .then((asset: cc.Prefab) => {
                                this._cowboySetting = cc.instantiate(asset);
                                this._cowboySetting.setAnchorPoint(cc.v2(0.5, 0.5));
                                this._cowboySetting.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                                this.node.addChild(this._cowboySetting);
                            });
                    } else {
                        this._cowboySetting.getComponent(MiniGameAudioSettingControl).initSwitch();
                        this._cowboySetting.active = true;
                    }
                });

                // // 菜单 - 切换皮肤
                // menuLayer.getBtnSwitchUI().node.on('click', (event: cc.Event): void => {
                //     this.playCowboyEffect(SoundPath.Button);
                //     menuLayer.hide(false);

                //     this._isSwitchingUI = true;
                //     CowboyUtils.setUIStyle(CowboyUIStyle.NEW);
                //     cv.roomManager.RequestJoinRoom();
                // });

                // 菜单 - 高级设置
                menuLayer.getBtnAdvancedSetting().node.on('click', (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    if (!this._humanboyAdvancedSetting) {
                        // this._humanboyAdvancedSetting = cc.instantiate(
                        //     pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_ADVANCED_SETTING)
                        // );
                        // this.node.addChild(
                        //     this._humanboyAdvancedSetting,
                        //     COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                        // );
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.COWBOY_ADVANCED_SETTING)
                            .then((asset: cc.Prefab) => {
                                this._humanboyAdvancedSetting = cc.instantiate(asset);
                                this.node.addChild(
                                    this._humanboyAdvancedSetting,
                                    COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                                );
                                this._humanboyAdvancedSetting.getComponent(MiniGameAdvancedSettingControl).show();
                            });
                    } else {
                        this._humanboyAdvancedSetting.getComponent(MiniGameAdvancedSettingControl).show();
                    }
                });

                // 菜单 - 退出
                menuLayer.getBtnExit().node.on('click', (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    const iUsedAutoBetCount = this._cowboyRoom.betSettings.usedAutoBetCount;
                    const iSelectAutoBetCount = this._cowboyRoom.betSettings.selectAutoBetCount;

                    if (iSelectAutoBetCount > 0) {
                        let dialogNode = null;
                        // if ((<any>window).CurrentUserInfo.user.wasUserInDiamondGame) {
                        //     dialogNode = cc.instantiate(this.HumanboyDialog_prefab);
                        // } else {
                        //     dialogNode = cc.instantiate(this.HumanboyDialog_prefab2);
                        // }
                        dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_DIALOG)
                        );
                        this.node.addChild(dialogNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST);
                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent: pf.StringUtil.formatC(
                                pf.languageManager.getString('Cowboy_auto_bet_exit_tips_zh_cn'),
                                iUsedAutoBetCount,
                                iSelectAutoBetCount
                            ),
                            stringLeftBtn: pf.languageManager.getString('CowBoy_btn_desc_exit_game_zh_cn'),
                            stringRightBtn: pf.languageManager.getString('CowBoy_btn_desc_resume_game_zh_cn'),
                            cbLeftBtn: (dialog: IMiniGameDialog) => {
                                // cv.cowboyNet.RequestLeaveRoom();
                                this.tryLeaveRoom();
                            },
                            cbRightBtn: (dialog: IMiniGameDialog) => {
                                miniGameDialog?.close();
                            },
                            isReachedMax: this._cowboyRoom.betSettings.reachLimitBet,
                            legacyDialog: legacyDialog,
                            isShowBtnCenter: true,
                            stringCenterButton: pf.languageManager.getString('MiniGame_AddAutoBet_Text_zh_cn'),
                            cbCenterBtn: (dialog: MiniGameDialog) => {
                                this.showAutoAddBetList(dialog);
                            },
                            onUpdateContent: (dialog: IMiniGameDialog) => {
                                if (legacyDialog) {
                                    legacyDialog.txt_content.string = pf.StringUtil.calculateAutoWrapString(
                                        legacyDialog.txt_content.node,
                                        pf.StringUtil.formatC(
                                            pf.languageManager.getString('Cowboy_auto_bet_exit_tips_zh_cn'),
                                            this._cowboyRoom.betSettings.usedAutoBetCount,
                                            this._cowboyRoom.betSettings.selectAutoBetCount
                                        )
                                    );
                                }
                                if (this._cowboyRoom.betSettings.reachLimitBet) {
                                    miniGameDialog?.blockCenterButton();
                                }
                            }
                        };
                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                    } else {
                        if (this._cowboyExit === null) {
                            // this._cowboyExit = cc.instantiate(
                            //     pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_EXIT)
                            // );
                            // this._cowboyExit.setAnchorPoint(cc.v2(0.5, 0.5));
                            // this._cowboyExit.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                            // this.node.addChild(this._cowboyExit);
                            pf.addressableAssetManager
                                .loadAsset(macros.Dynamic_Assets.COWBOY_EXIT)
                                .then((asset: cc.Prefab) => {
                                    this._cowboyExit = cc.instantiate(asset);
                                    this._cowboyExit.setAnchorPoint(cc.v2(0.5, 0.5));
                                    this._cowboyExit.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                                    this.node.addChild(this._cowboyExit);
                                });
                        } else {
                            this._cowboyExit.active = true;
                        }
                        // this.playCowboyEffect(SoundPath.Button);
                    }
                });
            }

            this._humanboyMenu.getComponent(MiniGameMenuControl).show(true);
            const widthBg = this._btnMenu.node.getChildByName('bg').width;
            const heightBg = this._btnMenu.node.getChildByName('bg').height;
            this._humanboyMenu
                .getComponent(MiniGameMenuControl)
                .setMenuPosition(cc.v2(this._btnMenu.node.x + widthBg / 2, this._btnMenu.node.y - heightBg / 2));
        });

        // 玩家列表
        this._btnPlayerList = this._bottomPanel.getChildByName('btnPlayerList').getComponent(cc.Button);
        // this._btnPlayerListNew = this.node.getChildByName('btnPlayerListNew').getComponent(cc.Button);
        this._btnPlayerList.node.on('click', (event: cc.Event): void => {
            // cv.getCowboyNet().RequestPlayerList();
            this._playSoundEffect(macros.Audio.PRESS);
            this._cowboyRoom.getPlayerList().then((resp) => {
                this.OnPlayerListUpdate(resp.players, resp.playerNum);
            });
        });

        // 商店
        this.self_panel = this._bottomPanel.getChildByName('self_panel');
        let btn_shop_valid = this.self_panel.getChildByName('btn_shop_valid');
        btn_shop_valid.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
            if (pf.app.clientType === pf.client.ClientType.CowboyWeb) {
                // document.location.href = "ccjs://recharge";
            } else {
                this.openShop(null);
            }
        });
    }

    protected OnPlayerListUpdate(gamePlayers: pf.services.GamePlayer[], playerNum: number): void {
        if (this._cowboyList === null) {
            // this._cowboyList = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_PLAYER_LIST));
            // this._cowboyList.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST;
            // this.node.addChild(this._cowboyList);

            // this._cowboyList.getComponent(MiniGamePlayerListControl).setCowboyData(gamePlayers, playerNum);
            // this._cowboyList.getComponent(MiniGamePlayerListControl).displayCell(0);
            pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.COWBOY_PLAYER_LIST).then((asset: cc.Prefab) => {
                this._cowboyList = cc.instantiate(asset);
                this._cowboyList.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST;
                this.node.addChild(this._cowboyList);

                this._cowboyList.getComponent(MiniGamePlayerListControl).setCowboyData(gamePlayers, playerNum);
                this._cowboyList.getComponent(MiniGamePlayerListControl).displayCell(0);
            });
        } else {
            this._cowboyList.active = true;
            this._cowboyList.getComponent(MiniGamePlayerListControl).setCowboyData(gamePlayers, playerNum);
            this._cowboyList.getComponent(MiniGamePlayerListControl).displayCell(-1);
        }
    }

    initGuide(): void {
        let storeGuideKey = 'cowboy_has_show_guide_2';
        if (pf.localStorage.getItem(storeGuideKey) !== 'true') {
            let panelRecord = this._topBg.node.getChildByName('panelRecord');
            for (let i = 0; i < this._recordNum; i++) {
                this._recordDotsTemp.push(
                    panelRecord.getChildByName(pf.StringUtil.formatC('recordDot%d', i)).getComponent(cc.Sprite)
                );
            }
            if (!this._humanboyGuide) {
                this._humanboyGuide = cc.instantiate(
                    pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_GAME_GUIDE)
                );
                this.node.addChild(this._humanboyGuide, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_GUIDE);
            }
            let guideLayer = this._humanboyGuide.getComponent(MiniGameGuideControl);
            guideLayer.setDescString(pf.languageManager.getString('Cowboy_ludan_guide_text_zh_cn'));
            guideLayer.show(
                this._topBg.node,
                () => {
                    let hasShowGuide = 'true';
                    pf.localStorage.setItem(storeGuideKey, hasShowGuide);
                    // cv.cowboyNet.RequestTrend();
                    this._cowboyRoom.queryTrend();
                    this._cowboyChart.active = true;
                    // cv.MessageCenter.send('on_display_page1');
                    this._cowboyChartControl.onPage1();
                    this._playSoundEffect(macros.Audio.PRESS);
                    pf.StringUtil.clearArray(this._recordDotsTemp);
                },
                true
            );
        }
    }

    adaptiveBetBtnPanel(): void {
        // 若为空, 则填充按钮数组
        if (this._vBottomBetBtns.length === 0) {
            // 下注按钮
            let betButtons_len = pf.StringUtil.getArrayLength(this._betButtons);
            for (let i = 0; i < betButtons_len; ++i) {
                this._vBottomBetBtns.push(
                    new MiniGameCommonDef.GameNodeScale(this._betButtons[i].node, this.fBetBtnSrcScaleRate)
                );
            }
            // 续投按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btnBetAuto.node, this._btnBetAuto.node.scale)
            );
            // 清屏按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btnBetClean.node, this._btnBetClean.node.scale)
            );
            // 红包节按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btn_redpacket_festival, this._btn_redpacket_festival.scale)
            );
            // 欢乐红包按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btn_redpacket_grab, this._btn_redpacket_grab.scale)
            );
        }

        let w = this._btnPlayerList.node.x - this._btnPlayerList.node.getContentSize().width / 2;
        w -= this.self_panel.x + this.self_panel.getContentSize().width / 2;
        this._panel_betbtn.setContentSize(cc.size(w, this._panel_betbtn.getContentSize().height));
        this._panel_betbtn.setPosition(
            this.self_panel.x + w / 2 + this.self_panel.getContentSize().width / 2,
            this._panel_betbtn.y
        );

        let iTotal_w = 0; // 所有可见子节点宽度和
        let iSpacing_x = 0; // 子节点之间的间距
        let iChildrenCount = 0; // 可见的子节点个数

        let vBottomBetBtns_len = pf.StringUtil.getArrayLength(this._vBottomBetBtns);
        for (let i = 0; i < vBottomBetBtns_len; ++i) {
            const node = this._vBottomBetBtns[i].node;
            const fScale = this._vBottomBetBtns[i].scale;
            if (node.active) {
                ++iChildrenCount;
                iTotal_w += node.getContentSize().width * fScale;
            }
        }
        iSpacing_x = (this._panel_betbtn.getContentSize().width - iTotal_w) / (iChildrenCount + 1);
        let iLast_w = -this._panel_betbtn.width * 0.5;
        const vBetCoinOption = this._cowboyRoom.betSettings.betCoinOptions;
        for (let i = 0; i < vBottomBetBtns_len; ++i) {
            const node = this._vBottomBetBtns[i].node;
            const fScale = this._vBottomBetBtns[i].scale;
            if (node.active) {
                const szNode = node.getContentSize();
                let nodeSize = 0;
                if (i < 5) {
                    const llAmountLevel = pf.StringUtil.clientGoldByServer(vBetCoinOption[i]);
                    if (llAmountLevel < this._cowboyRoom.llCoinUICritical) {
                        nodeSize = 116;
                    } else {
                        nodeSize = 147;
                    }
                } else {
                    nodeSize = szNode.width;
                }
                const x = iLast_w + iSpacing_x + (nodeSize * fScale) / 2;
                let pos = this._panel_betbtn.convertToWorldSpaceAR(cc.v2(x, 0));
                pos = node.getParent().convertToNodeSpaceAR(pos);
                node.setPosition(pos.x, node.y);
                if (this._betBtnBot[i]) {
                    this._betBtnBot[i].setPosition(pos.x, this._betBtnBot[i].y);
                }
                iLast_w = pos.x + (nodeSize * fScale) / 2;
            }
        }

        // 适配红包节入口节点提示层
        if (this._btn_redpacket_festival_layer) {
            const wpos: cc.Vec2 = this._btn_redpacket_festival.convertToWorldSpaceAR(cc.Vec2.ZERO);
            this._btn_redpacket_festival_layer.setPosition(
                this._btn_redpacket_festival_layer.parent.convertToNodeSpaceAR(wpos)
            );
        }

        // 适配高级续投提示语位置
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoTipsPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
        }
    }

    protected initRedPackage(): void {
        // 红包节按钮
        this._btn_redpacket_festival = this._panel_betbtn.getChildByName('btn_redpacket_festival');
        this._btn_redpacket_festival.active = false;

        // 红包节按钮提示层
        this._btn_redpacket_festival_layer = cc.instantiate(this._btn_redpacket_festival);
        this.node.addChild(this._btn_redpacket_festival_layer, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_RED_PACKAGE);

        const wpos: cc.Vec2 = this._btn_redpacket_festival.convertToWorldSpaceAR(cc.Vec2.ZERO);
        this._btn_redpacket_festival_layer.setPosition(
            this._btn_redpacket_festival_layer.parent.convertToNodeSpaceAR(wpos)
        );

        // 欢乐红包按钮
        this._btn_redpacket_grab = this._panel_betbtn.getChildByName('btn_redpacket_grab');
        this._btn_redpacket_grab.on('click', (event: cc.Event): void => {
            console.log('CowboyGameScene - click - btn_redpacket_grab');
        });
        this._btn_redpacket_grab.active = false;

        // 初始执行一次
        this.showLuckButton(pf.client.RedPacketLotteryMode.Classical);
    }

    initWayOutInfoByAreaIdx(iAreaIdx: number): void {
        if (iAreaIdx < 0 || iAreaIdx >= pf.StringUtil.getArrayLength(this._betAreas)) return;

        const panelWayOut = this._betAreas[iAreaIdx].getChildByName('panel_way_out');
        if (!panelWayOut) return;
        if (panelWayOut.getComponent(cc.Mask)) {
            panelWayOut.removeComponent(cc.Mask);
        }

        const tWayOutInfo = new CowboyWayOutInfo();
        this._mapWayOutInfo.set(iAreaIdx, tWayOutInfo);

        tWayOutInfo.iAreaIdx = iAreaIdx;
        tWayOutInfo.panelWayOut = panelWayOut;
        tWayOutInfo.panelWayOut.getChildByName('touchPanel').on(cc.Node.EventType.TOUCH_END, (): void => {
            // 点击路子入口事件
            // cv.getCowboyNet().RequestTrend();
            this._cowboyRoom.queryTrend();
            this._cowboyChart.active = true;
            // cv.MessageCenter.send('on_display_page2');
            this._cowboyChartControl.onPage2();
            // this.playCowboyEffect(SoundPath.Button);
            this._playSoundEffect(macros.Audio.PRESS);
        });

        // 路子球状图片
        const children = tWayOutInfo.panelWayOut.children;
        const count = children.length;
        for (let i_wayout_index = 0; i_wayout_index < count; ++i_wayout_index) {
            const strImgName = pf.StringUtil.formatC('img_%d', i_wayout_index);
            const img = tWayOutInfo.panelWayOut.getChildByName(strImgName);
            if (img) {
                img.active = false;
                tWayOutInfo.vWayOutImg.push(img);
                tWayOutInfo.vWayOutImgSrcPos.push(img.getPosition());
            }
        }
        // 文本
        const txt = tWayOutInfo.panelWayOut.getChildByName('txt_way_out');
        if (txt) {
            if (!tWayOutInfo.rtxtWayOut) tWayOutInfo.rtxtWayOut = new cc.Node().addComponent(cc.RichText);
            tWayOutInfo.rtxtWayOut.fontSize = txt.getComponent(cc.Label).fontSize;
            tWayOutInfo.rtxtWayOut.node.setAnchorPoint(txt.getAnchorPoint());
            tWayOutInfo.rtxtWayOut.node.setContentSize(txt.getContentSize());
            const pos = this._betContentBg.node.convertToNodeSpaceAR(
                txt.getParent().convertToWorldSpaceAR(txt.getPosition())
            );
            tWayOutInfo.rtxtWayOut.node.setPosition(pos);
            tWayOutInfo.rtxtWayOut.node.active = false;
            tWayOutInfo.rtxtWayOut.handleTouchEvent = false;

            this._betContentBg.node.addChild(tWayOutInfo.rtxtWayOut.node);
            txt.removeFromParent();
            txt.destroy();
        }
        // 路子显示风格
        const option = this.getBetOptionByAreaIdx(iAreaIdx);
        switch (option) {
            // 牛仔胜利
            case network.BetZoneOption.EQUAL:
                tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                tWayOutInfo.iWayOutLoseLimitCount = 200;
                break;
            // 对子
            case network.BetZoneOption.HOLE_SAME:
                tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO;
                tWayOutInfo.iWayOutLoseLimitCount = 200;
                break;
            // 对A
            case network.BetZoneOption.HOLE_A:
                tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                tWayOutInfo.iWayOutLoseLimitCount = 200;
                break;
            // 三条/顺子/同花
            case network.BetZoneOption.FIVE_3_SHUN_TONG_HUA:
                tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                break;
            // 葫芦
            case network.BetZoneOption.FIVE_3_2:
                tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                tWayOutInfo.iWayOutLoseLimitCount = 200;
                break;
            // 金刚/同花顺/皇家
            case network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4:
                tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                tWayOutInfo.iWayOutLoseLimitCount = 300;
                break;
            case network.BetZoneOption.HOLE_3_TONG_SAME_SHUN:
                tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                break; // 顺子/同花/同花顺
            case network.BetZoneOption.FIVE_NONE_1DUI:
                tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                break; // 高牌/一对
            case network.BetZoneOption.FIVE_2DUI:
                tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                break; // 两对
            default:
                break;
        }
    }

    _showWayOutAnim(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        const panelWayOut = this._mapWayOutInfo.get(iAreaIdx).panelWayOut;
        const vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        const vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
        if (!panelWayOut || vWayOutImgLen <= 0) return;

        // const mapZoneData = cb.getCowboyRoom().mapZoneData;
        // const it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._cowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        // 路子显示风格
        switch (this._mapWayOutInfo.get(iAreaIdx).eWayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(iAreaIdx, 1);
                this._showWayOutImgAnim(iAreaIdx);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(iAreaIdx);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    const pattern = panelWayOut.getChildByName('pattern'); // 是否有花纹
                    let bShowTxt = false;
                    const vHistoryResults = zoneData.optionResult.historyResults;
                    if (vHistoryResults.length > 0 && vHistoryResults.length > vWayOutImg.length) {
                        let bDefeat = true;
                        const vWayOutImgLen = vWayOutImg.length;
                        for (let i = 0; i <= vWayOutImgLen; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] === 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                        if (pattern) {
                            pattern.active = bShowTxt;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(iAreaIdx);
                    } else {
                        this._updateWayOutImg(iAreaIdx, 1);
                        this._showWayOutImgAnim(iAreaIdx);
                    }
                }
                break;

            default:
                break;
        }
    }

    _showWayOutImgAnim(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        const vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        const vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
        if (vWayOutImgLen <= 0) return;

        // const mapZoneData = cb.getCowboyRoom().mapZoneData;
        // const it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._cowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        // 隐藏路单文本
        const rtxtWayOut: cc.RichText = this._mapWayOutInfo.get(iAreaIdx).rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 该区域输赢(0 - 未击中, 1 - 击中)
        // const result = it_zoneData.result;
        let result = zoneData.optionResult.result;
        let fileName = '';

        // 输
        if (result === 0) {
            fileName = 'cowboy_icon_circle_small_gray';
        }
        // 赢
        else if (result === 1) {
            // if (pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN) {
            fileName = 'cowboy_icon_circle_small_red'; // zh_CN only in wpk cowboy
            // } else {
            //     fileName = 'cowboy_icon_circle_small_red_en';
            // }
        }

        // 计算空闲路子索引
        let freeIndex = vWayOutImg.length;
        for (let i = 0; i < freeIndex; ++i) {
            if (!vWayOutImg[i].active) {
                freeIndex = i;
                break;
            }
        }

        // 路子满了挤动动画
        if (freeIndex > vWayOutImgLen - 1) {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.3 * this._fActExecute_WayOut),
                    cc.callFunc(() => {
                        this._showWayOutMoveAnim(iAreaIdx);
                    })
                )
            );
        } else {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.8 * this._fActExecute_WayOut),
                    cc.callFunc(() => {
                        vWayOutImg[freeIndex].active = true;
                        // cb.loadSpriteTextureByPlist(
                        //     this.game_dznz_PLIST,
                        //     vWayOutImg[freeIndex].getComponent(cc.Sprite),
                        //     fileName
                        // );
                        vWayOutImg[freeIndex].getComponent(cc.Sprite).spriteFrame =
                            this._atlasDZNZ.getSpriteFrame(fileName);
                    })
                )
            );
        }
    }

    _updateWayOutImg(iAreaIdx: number, reduce: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        const panelWayOut = this._mapWayOutInfo.get(iAreaIdx).panelWayOut;
        panelWayOut.active = true;

        const vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        const vWayOutImgSrcPos = this._mapWayOutInfo.get(iAreaIdx).vWayOutImgSrcPos;

        // const mapZoneData = cb.getCowboyRoom().mapZoneData;
        // const it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._cowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        // 隐藏路单文本
        const rtxtWayOut: cc.RichText = this._mapWayOutInfo.get(iAreaIdx).rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 逆序取历史记录
        let fileName = '';
        // const vHistoryResults = it_zoneData.vHistoryResults;
        let vHistoryResults = zoneData.optionResult.historyResults;

        const vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
        const vHistoryResultsLen = pf.StringUtil.getArrayLength(vHistoryResults);
        const min_count = vWayOutImgLen < vHistoryResultsLen ? vWayOutImgLen : vHistoryResultsLen;
        let end_index = 0;
        let end_count = 0;

        // ui显示个数 >= 路子数据个数, 少显示 reduce 个
        if (vWayOutImgLen >= vHistoryResultsLen) {
            end_index = min_count - 1;
            end_count = min_count - reduce;
        }
        // ui显示个数 < 路子数据个数, 偏移 reduce 位数据显示
        else {
            end_index = min_count - 1 + reduce;
            end_count = min_count;
        }

        for (let i = 0; i < vWayOutImgLen; ++i) {
            // 复原位置
            vWayOutImg[i].setPosition(vWayOutImgSrcPos[i]);

            const index = end_index - i;
            if (i < end_count && index >= 0 && index < vHistoryResultsLen) {
                vWayOutImg[i].active = true;

                // 该区域输赢(0 - 未击中, 1 - 击中)
                const result = vHistoryResults[index];
                if (result === 0) {
                    fileName = 'cowboy_icon_circle_small_gray';
                } else if (result === 1) {
                    // if (cv.config.getCurrentLanguage() === cv.Enum.LANGUAGE_TYPE.zh_CN) {
                    fileName = 'cowboy_icon_circle_small_red'; // zh_CN only in wpk cowboy
                    // } else {
                    //     fileName = 'cowboy_icon_circle_small_red_en';
                    // }
                }
                // cb.loadSpriteTextureByPlist(this.game_dznz_PLIST, vWayOutImg[i].getComponent(cc.Sprite), fileName);
                vWayOutImg[i].getComponent(cc.Sprite).spriteFrame = this._atlasDZNZ.getSpriteFrame(fileName);
            } else {
                vWayOutImg[i].active = false;
            }
        }
    }

    _updateWayOutTxt(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;
        const rtxtWayOut: cc.RichText = this._mapWayOutInfo.get(iAreaIdx).rtxtWayOut;
        if (!rtxtWayOut) return;

        // 隐藏路单球图片面板
        const vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        const iWayOutLoseLimitCount = this._mapWayOutInfo.get(iAreaIdx).iWayOutLoseLimitCount;
        const vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
        for (let i = 0; i < vWayOutImgLen; ++i) {
            vWayOutImg[i].active = false;
        }

        // const eCurState = cb.getCowboyRoom().curState;
        let eCurState = this._cowboyRoom.gameState.roundState;
        // const mapZoneData = cb.getCowboyRoom().mapZoneData;
        let mapZoneData = this._cowboyRoom.betZones;

        const it = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (it) {
            // 连续多少手未出现(< 0 房间刚刚开始,不需要统计; > 0 多少手; = 0 上一手出现过)
            const luckLoseHand = it.optionResult.luckLoseHand;

            if (luckLoseHand < 0) {
                rtxtWayOut.string = '';
            } else if (luckLoseHand === 0) {
                if (eCurState === network.RoundState.WAIT_NEXT_ROUND) {
                    pf.StringUtil.setRichTextString(
                        rtxtWayOut.node,
                        pf.languageManager.getString('Cowboy_game_wayout_hit_txt')
                    );
                } else {
                    pf.StringUtil.setRichTextString(
                        rtxtWayOut.node,
                        pf.languageManager.getString('Cowboy_game_wayout_hit_last_txt')
                    );
                }
            } else {
                let strCountDest = '';
                if (iWayOutLoseLimitCount !== 0 && luckLoseHand > iWayOutLoseLimitCount) {
                    strCountDest = pf.StringUtil.formatC('%d+', iWayOutLoseLimitCount);
                } else {
                    strCountDest = pf.StringUtil.formatC('%d', luckLoseHand);
                }

                pf.StringUtil.setRichTextString(
                    rtxtWayOut.node,
                    pf.StringUtil.formatC(pf.languageManager.getString('Cowboy_game_wayout_lose_txt'), strCountDest)
                );
            }
            rtxtWayOut.node.active = true;
        }
    }

    _updateWayOut(iAreaIdx: number, reduce: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        const panelWayOut = this._mapWayOutInfo.get(iAreaIdx).panelWayOut;
        const vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        // const mapZoneData = cb.getCowboyRoom().mapZoneData;
        // const it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._cowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        switch (this._mapWayOutInfo.get(iAreaIdx).eWayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(iAreaIdx, reduce);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(iAreaIdx);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    const pattern = panelWayOut.getChildByName('pattern'); // 是否有花纹
                    let bShowTxt = false;
                    // const vHistoryResults = it_zoneData.vHistoryResults;
                    let vHistoryResults = zoneData.optionResult.historyResults;
                    const vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
                    const vHistoryResultsLen = pf.StringUtil.getArrayLength(vHistoryResults);
                    if (vHistoryResultsLen > 0 && vHistoryResultsLen > vWayOutImgLen) {
                        let bDefeat = true;
                        for (let i = 0; i <= vWayOutImgLen; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] === 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                        if (pattern) {
                            pattern.active = bShowTxt;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(iAreaIdx);
                    } else {
                        this._updateWayOutImg(iAreaIdx, reduce);
                    }
                }
                break;

            default:
                break;
        }
    }

    protected showAutoAddBetList(dialog: MiniGameDialog) {
        if (!this.advanceAutoAddBet) {
            this.advanceAutoAddBet = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_ADVANCED_AUTO)
            );
            this.node.addChild(this.advanceAutoAddBet, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_ADD_SELECT);
        }
        const miniGameAdvanceAuto = this.advanceAutoAddBet.getComponent(MiniGameAdvancedAuto);
        const advanceAuto = new ConcreteAdvancedAuto(miniGameAdvanceAuto);
        advanceAuto.adaptSelectPanelPos(dialog.btn_center.node);
        advanceAuto.showSelectPanel(true);
        advanceAuto.setCountUpdateCallback(() => {
            dialog.updateCenterButton();
        });
    }

    // 显示玩家连胜动画
    updatePlayerWinCount(uid: number, bAnim: boolean = false): void {
        const playerHeads: cc.Node[] = this.getPlayerHeadNodesByUid(uid);
        if (playerHeads.length === 0) {
            return;
        }

        for (const head of playerHeads) {
            // 富豪No1 和 神算子 不显示连胜
            if (this._otherPlayerHeads[0].bg.node === head || this._otherPlayerHeads[1].bg.node === head) {
                continue;
            }
            let keepWinCount = this._cowboyRoom.getPlayerKeepWinCountByUid(uid);
            if (keepWinCount >= 3) {
                keepWinCount = keepWinCount >= 11 ? 11 : keepWinCount;
                const animName = this._mapWinCountAnim.get(keepWinCount);
                const win_player_win_count = this.node.getChildByName('win_player_win_count_' + head.uuid);
                let sprWinCount: sp.Skeleton = null;
                if (win_player_win_count) {
                    win_player_win_count.active = true;
                    sprWinCount = win_player_win_count.getComponent(sp.Skeleton);
                    sprWinCount.setAnimation(0, animName, true);
                } else {
                    sprWinCount = cc.instantiate(this.win_count_prefab).getComponent(sp.Skeleton);
                    sprWinCount.node.name = 'win_player_win_count_' + head.uuid;
                    const offsetY = head === this._selfHeadBg ? 40 : 70;
                    const offsetX = 30;
                    let tmpPos = head.convertToWorldSpaceAR(cc.v2(offsetX, offsetY));
                    tmpPos = this.node.convertToNodeSpaceAR(tmpPos);
                    sprWinCount.node.setPosition(tmpPos);
                    this.node.addChild(sprWinCount.node, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_IMG_WIN_COUNT);
                    sprWinCount.setAnimation(0, animName, true);
                }

                if (bAnim) {
                    // animation
                    const targetPos = sprWinCount.node.getPosition();
                    let bornPos = targetPos;
                    let headMidWorldPos = head.getParent().convertToWorldSpaceAR(head.getPosition());
                    headMidWorldPos = head.convertToNodeSpaceAR(headMidWorldPos);
                    if (headMidWorldPos.x < pf.system.view.width / 2) {
                        bornPos = cc.v2(-200 + bornPos.x, 0 + bornPos.y);
                    } else {
                        bornPos = cc.v2(200 + bornPos.x, 0 + bornPos.y);
                    }
                    sprWinCount.node.setPosition(bornPos);
                    sprWinCount.node.runAction(
                        cc.moveTo(0.18, targetPos).easing(cc.easeBackOut()).easing(cc.easeSineOut())
                    );
                }
            } else {
                const win_player_win_count = this.node.getChildByName('win_player_win_count_' + head.uuid);
                if (win_player_win_count) {
                    win_player_win_count.active = false;
                }
            }
        }
    }

    protected createPokerCard(): PokerCardControl {
        const ctrl = cc
            .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.POKER_CARD))
            .getComponent(PokerCardControl);
        ctrl.init();
        // ctrl.setCardBackSpriteFrame(this.cardbackSpriteFrame);
        return ctrl;
    }

    openShop(sender: any): void {
        const dialog: HumanboyDialogControl = cc
            .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_COMMON_DIALOG))
            .getComponent(HumanboyDialogControl);
        dialog.node.name = 'Cowboy_Dialog';
        dialog.show(
            pf.languageManager.getString('PokerMaster_dialog_recharge_zh_cn'),
            pf.languageManager.getString('Cancel_zh_cn'),
            pf.languageManager.getString('Confirm_zh_cn'),
            (sender: HumanboyDialogControl) => {},
            (sender: HumanboyDialogControl) => {
                const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                context.isSelfRecharge = true;
                context.exitCallback(pf.client.ExitType.Standard);
            }
        );
        this.node.addChild(dialog.node, LayerZorder.Z_IDX_PANEL_SERVER_TOAST);
    }

    // iPad/iPhoneX等宽窄屏适配
    adaptiveScreen(): void {
        // 底部木板bg适配
        const background = this._bottomPanel.getChildByName('background');
        const bg_bottom_long = this._bottomPanel.getChildByName('bg_bottom_long');
        background.active = true;
        bg_bottom_long.active = false;

        // const HU = (<any>window).HMFUtils;

        // if (this._bTrueFullScreen && this._openIphoneXAdapter && cv.config.IS_FULLSCREEN) {
        if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            // if (HMFUtils.isIPhone14Pro()) {
            //     this._leftPlayerPanel.scale = 0.95;
            //     this._rightPlayerPanel.scale = 0.95;
            // }
            // const tmp_x = HMFUtils.isIPhone14Pro() ? 75 : 120; // cv.viewAdaptive.IPHONEX_OFFSETY - 50;
            const tmp_x = 120;
            this._leftPlayerPanel.setPosition(this._leftPlayerPanel.x - tmp_x, this._leftPlayerPanel.y);
            this._rightPlayerPanel.setPosition(this._rightPlayerPanel.x + tmp_x, this._rightPlayerPanel.y);
            // if (HMFUtils.isIPhone14Pro()) {
            // this._btnMenu.node.setPosition(this._leftPlayerPanel.x + 10, this._btnMenu.node.y);
            // } else {
            this._btnMenu.node.setPosition(this._leftPlayerPanel.x, this._btnMenu.node.y);
            // }
            this._btnPlayerList.node.setPosition(this._rightPlayerPanel.x, this._btnPlayerList.node.y);
            this.self_panel.setPosition(this._leftPlayerPanel.x + 130, this.self_panel.y);
            // 底部木板bg适配
            background.active = false;
            bg_bottom_long.active = true;

            const _offx = 100;
            const node_boy = cc.find('boy', this._gameContent);
            const bg_boy = cc.find('clothRed', this.node);
            node_boy.x -= _offx;
            bg_boy.x -= _offx;

            const node_cow = cc.find('cow', this._gameContent);
            const bg_cow = cc.find('clothblue', this.node);
            node_cow.x += _offx;
            bg_cow.x += _offx;
            // } else if (!HU.isMobileBrowser() && cv.config.IS_WIDESCREEN) {
            this.updateRebateFloatButtonPosition();
        } else if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
            const heroOffsetY = 56;
            const cardOffsetY = 60;

            const img = this._heroBoy;
            if (
                img.parent.convertToWorldSpaceAR(img.position).y + img.height * 0.5 + cardOffsetY >
                this._topBg.node.parent.convertToWorldSpaceAR(this._topBg.node.position).y +
                    this._topBg.node.height * 0.5
            )
                return;

            this._heroBoy.setPosition(this._heroBoy.x, heroOffsetY + this._heroBoy.y);
            this._heroCow.setPosition(this._heroCow.x, heroOffsetY + this._heroCow.y);

            this._cardPanel.setPosition(this._cardPanel.x, cardOffsetY + this._cardPanel.y);
            this._betCountDown.setPosition(this._betCountDown.x, cardOffsetY + this._betCountDown.y);

            this._btnMenu.node.setPosition(this._leftPlayerPanel.x, this._btnMenu.node.y);
        } else {
            this._btnMenu.node.setPosition(this._leftPlayerPanel.x, this._btnMenu.node.y);
        }
    }

    showSwitchTable() {
        if (this._bSwitchTable) return;
        this._bSwitchTable = true;
        // cv.TP.showMsg(
        //     pf.languageManager.getString('MiniGame_Switch_Content'),
        //     cv.Enum.ButtonStyle.TWO_BUTTON,
        //     () => {
        //         cv.MessageCenter.send('HideWebview_ShowWindows', true);
        //         cv.roomManager.setCurrentRoomID(cb.getCowboyRoom().idle_roomid);
        //         cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
        //         cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
        //         cv.roomManager.RequestJoinRoom();
        //     },
        //     () => {
        //         cv.MessageCenter.send('HideWebview_ShowWindows', true);
        //         this._backToCowboyListScene();
        //     }
        // );
        // cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_SWITCH_TABLE);
        this._commonDialog.showMsg(
            pf.languageManager.getString('MiniGame_Switch_Content'),
            [pf.languageManager.getString('MiniGame_Switch_Table'), pf.languageManager.getString('MiniGame_Exit')],
            async () => {
                pf.app.emit('hideWebview');
                const roomId = this._cowboyRoom.roundInfo.idleRoomId;
                const cowboyService = pf.serviceManager.get(domain.CowboyService);
                await cowboyService.login();
                await this._cowboyRoom.joinRoom(roomId);
            },
            () => {
                pf.app.emit('hideWebview');
                this._backToCowboyListScene();
            }
        );
    }

    _onMsgUpdateWorldServerGold(isForce?: boolean): void {
        // world服接收接口已过滤只发自己, 因此这里无需再次判断(同时没有别的需求, 所以也不用缓存下发的结构)
        const checkedIsForce = isForce === true ? true : false;
        let llCurGold = this._walletService.getWallet().totalAmount;

        // 结算阶段跳过(否则会提前知道输赢结果)
        if (this._cowboyRoom.canUpdateWorldServerGold || checkedIsForce) {
            // 更新自己金币信息
            this._cowboyRoom.selfPlayer.curCoin = Number(this._walletService.getWallet().totalAmount);

            this.updateSelfCoin();

            // 更新其他人信息(因为自己有可能会在8人列表中)
            let bOnMainPlayerList = false;
            let otherPlayersInfo = this._cowboyRoom.otherPlayers;
            let otherInfoLen = otherPlayersInfo.length;
            for (let i = 0; i < otherInfoLen; ++i) {
                if (this._authService.currentUser.userId === otherPlayersInfo[i].uid) {
                    bOnMainPlayerList = true;
                    otherPlayersInfo[i].curCoin = llCurGold;
                }
            }
            if (bOnMainPlayerList) {
                this.updateOtherCoin();
            }
        }
    }

    protected _getEventTitle(){
        const content = this._rebateEventStatus.content;
        if (!content) {
            return '';
        }
        return content[pf.LANGUAGE_GROUPS.zh_CN]?.title || '';
    }
}

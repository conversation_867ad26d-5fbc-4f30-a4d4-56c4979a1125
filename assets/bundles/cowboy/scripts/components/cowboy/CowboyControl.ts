/* eslint-disable camelcase */
import * as pf from '../../../../../poker-framework/scripts/pf';
import Deque = pf.Deque;
import PlayerOneBet = pf.services.PlayerOneBet;

import { macros } from '../../common/cowboy-macros';
import * as domain from '../../domain/cowboy-domain-index';
import * as network from '../../network/cowboy-network-index';

import * as cr from '../../../../common-resource/scripts/common-resource';
import type { DialogHubControl } from '../../../../common-resource/scripts/components/DialogHubControl';
import MiniGameMenuControl = cr.components.MiniGameMenuControl;
import MiniGameExchangeControl = cr.components.MiniGameExchangeControl;
import MiniGameRuleControl = cr.components.MiniGameRuleControl;
import MiniGameAudioSettingControl = cr.components.MiniGameAudioSettingControl;
import MiniGameAdvancedSettingControl = cr.components.MiniGameAdvancedSettingControl;
import MiniGamePlayerListControl = cr.components.MiniGamePlayerListControl;
import HumanboyDialogControl = cr.components.HumanboyDialogControl;
import HeadPointsAniControl = cr.components.HeadPointsAniControl;
import HumanboyAdvancedAutoControl = cr.components.HumanboyAdvancedAutoControl;
import ConcreteAdvancedAuto = cr.components.ConcreteAdvancedAuto;
import MiniGameAdvancedAuto = cr.components.MiniGameAdvancedAuto;
import IMiniGameDialog = cr.components.IMiniGameDialog;
import IMiniGameDialogConfig = cr.components.IMiniGameDialogConfig;
import MiniGameDialog = cr.components.MiniGameDialog;
import ConcreteMiniGameDialog = cr.components.ConcreteMiniGameDialog;
import LuckTurntableButtonControl = cr.components.LuckTurntableButtonControl;
import PokerCardControl = cr.components.PokerCardControl;
import AvatarControl = cr.components.AvatarControl;
import HumanboyBetCoinControl = cr.components.HumanboyBetCoinControl;
import ThemeSystemType = cr.components.ThemeSystemType;
import MiniGameGuideControl = cr.components.MiniGameGuideControl;
import TagControl = cr.components.TagControl;
import RebateFloatingButtonControl = cr.components.RebateFloatingButtonControl;
import RebateRewardPopupControl = cr.components.RebateRewardPopupControl;
import MiniGameDialogPopupId = cr.components.MiniGameDialogPopupId;
import PushNotification = pf.services.PushNotification;
import ConsumingPromptControl = cr.components.ConsumingPromptControl;
import MiniGameCommonDef = cr.MiniGameCommonDef;

import { CowboyChartControl } from './CowboyChartControl';
import type { PlayerSettle } from '../../domain/cowboy-player-settle';
import RebateCoinsFlyControl from '../../../../common-resource/scripts/components/rebate-promotion/RebateCoinsFlyControl';
import BettingRebateEventControl from '../../../../common-resource/scripts/components/rebate-promotion/BettingRebateEventControl';

export enum COWBOY_LOCAL_ZORDER {
    COWBOY_LOCAL_ZORDER_DUMMY = 0,
    COWBOY_LOCAL_ZORDER_IMG_HEAD,
    COWBOY_LOCAL_ZORDER_IMG_WIN_COUNT,
    COWBOY_LOCAL_ZORDER_COIN_NODE,
    COWBOY_LOCAL_ZORDER_ANIM_NODE,
    COWBOY_LOCAL_ZORDER_TIMELINE_NODE,
    COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_SELECT, // 高级续投选择面板
    COWBOY_LOCAL_ZORDER_REWRAD_TIP, // 中奖提示面板
    COWBOY_LOCAL_ZORDER_RED_PACKAGE, // 红包面板
    COWBOY_LOCAL_ZORDER_MENU_PANEL = 99,
    COWBOY_LOCAL_ZORDER_TOAST,
    COWBOY_LOCAL_ZORDER_GUIDE,
    COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_ADD_SELECT
}

// 牛仔路子结构信息
export class CowboyWayOutInfo {
    iAreaIdx: number = -1; // 区域索引
    iWayOutLoseLimitCount: number = 0; // 路单描述文本"xxx局"未出上限(超过上限显示: "xxx+ 局未出", 默认0表示无上限)
    panelWayOut: cc.Node = null; // 路单层
    rtxtWayOut: cc.RichText = null; // 路子描述文本
    vWayOutImg: cc.Node[] = []; // 路子精灵数组
    vWayOutImgSrcPos: cc.Vec2[] = []; // 路子精灵原始位置数组
    eWayOutStyle: number = MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE; // 路子显示风格
}

export class OtherPlayerHead {
    uid: number = 0;
    bg: cc.Sprite = null;
    textCoin: cc.Label = null;
    nbFlag: cc.Node = null; // 富豪/神算子
    avatarControl: AvatarControl = null;
}

const { ccclass, property } = cc._decorator;

@ccclass
export class CowboyControl extends cc.Component {
    // 抽中红包动画
    // @property(cc.Prefab) redPacket_prefab: cc.Prefab = null;
    @property(cc.Prefab) round_start_prefab: cc.Prefab = null;
    @property(cc.Prefab) fight_begin_prefab: cc.Prefab = null;
    @property(cc.Prefab) fight_end_prefab: cc.Prefab = null;
    @property(cc.Prefab) wait_for_next_round_prefab: cc.Prefab = null;
    // @property(cc.Prefab) win_flag_prefab: cc.Prefab = null;
    @property(cc.Prefab) special_card_type_prefab: cc.Prefab = null;
    // @property(cc.Prefab) win_player_light_prefab: cc.Prefab = null;
    // @property(cc.Prefab) btnBet_0_prefab: cc.Prefab = null;
    // @property(cc.Prefab) btnBet_3_prefab: cc.Prefab = null;
    @property(cc.Prefab) cow_win_prefab: cc.Prefab = null;
    @property(cc.Prefab) cow_lose_prefab: cc.Prefab = null;
    @property(cc.Prefab) boy_win_prefab: cc.Prefab = null;
    @property(cc.Prefab) boy_lose_prefab: cc.Prefab = null;

    // 子界面
    @property(cc.Prefab) cowboyChart: cc.Prefab = null;
    // @property(cc.Prefab) humanboyExchange_prefab: cc.Prefab = null;
    // @property(cc.Prefab) cowboyRule: cc.Prefab = null;
    // @property(cc.Prefab) cowboySetting: cc.Prefab = null;
    // @property(cc.Prefab) cowboyExit: cc.Prefab = null;
    // @property(cc.Prefab) cowboyList: cc.Prefab = null;
    // @property(cc.Prefab) humanboyAdvancedSetting_prefab: cc.Prefab = null;
    // @property(cc.Prefab) humanboyGuid_prefab: cc.Prefab = null;
    // @property(cc.Prefab) HumanboyMenu_prefab: cc.Prefab = null;
    // @property(cc.Prefab) HumanboyAdvancedAuto_prefab: cc.Prefab = null;
    // @property(cc.Prefab) MiniGameAddAdvancedAuto_prefab: cc.Prefab = null;
    // @property(cc.Prefab) HumanboyDialog_prefab: cc.Prefab = null;

    // @property(cc.SpriteAtlas) game_dznz_PLIST: cc.SpriteAtlas = null;
    // @property(cc.SpriteAtlas) cowboy_trend_anim_PLIST: cc.SpriteAtlas = null;
    // @property(cc.SpriteAtlas) special_card_type_PLIST: cc.SpriteAtlas = null;
    // @property(cc.SpriteAtlas) chart_PLIST: cc.SpriteAtlas = null;
    // @property(cc.SpriteAtlas) en_animation_PLIST: cc.SpriteAtlas = null;
    @property(cc.Font) win_num_FNT: cc.Font = null;
    @property(cc.Font) bet_btn_num_gray: cc.Font = null;
    @property(cc.Font) bet_btn_num_yellow: cc.Font = null;

    // @property(cc.Prefab) luckButton_prefab: cc.Prefab = null;
    // @property(cc.Prefab) points_ani_prefab: cc.Prefab = null;
    // @property(cc.Prefab) popSilencePre: cc.Prefab = null; //冷静预制件
    // @property(cc.Prefab) rebate_button_prefab: cc.Prefab = null;
    // @property(cc.Prefab) consumingNotifyPrefab: cc.Prefab = null;
    @property(cc.Node) consumingNotifyHolder: cc.Node = null;

    // @property(cc.Prefab) prefab_avatar: cc.Prefab = null;
    // @property(cc.Prefab) prefab_card: cc.Prefab = null;
    // @property(cc.SpriteFrame) cardbackSpriteFrame: cc.SpriteFrame = null;

    private _rebateActivity: RebateFloatingButtonControl = null;
    points_node: cc.Node = null;

    language_PLIST: cc.SpriteAtlas = null;

    // 牌/牌型
    _cardPanel: cc.Node = null;
    _oriRedHandCards: cc.Sprite[] = []; // 2
    _oriBlueHandCards: cc.Sprite[] = []; // 2
    _oriPublicCards: cc.Sprite[] = []; // 5
    _oriRedHandCardsPos: cc.Vec2[] = []; // 2
    _oriBlueHandCardsPos: cc.Vec2[] = []; // 2
    _oriPublicCardsPos: cc.Vec2[] = []; // 5

    _redHandCards: PokerCardControl[] = [];
    _blueHandCards: PokerCardControl[] = [];
    _publicCards: PokerCardControl[] = [];
    _redCardType: cc.Sprite = null;
    _blueCardType: cc.Sprite = null;
    _redCardTypeBg: cc.Sprite = null;
    _blueCardTypeBg: cc.Sprite = null;
    _mapLevelCardTypeImage: Map<number, string> = new Map(); // 映射： 牌型 <. 图片

    _gameContent: cc.Node = null;
    _menuPanel: cc.Node = null;
    _betContentBg: cc.Sprite = null;
    _menuBg: cc.Sprite = null;
    _bottomPanel: cc.Node = null;
    _btnMenu: cc.Button = null;
    _btnPlayerList: cc.Button = null;
    self_panel: cc.Node = null;
    _topBg: cc.Sprite = null;
    _btnZouShi: cc.Button = null;

    _betButtons: cc.Button[] = []; // 5
    _betButtonTexts: cc.Node[] = []; // vector<@property(cc.Label) this._betButtonTexts;
    _betButtonMasks: cc.Sprite[] = []; // 5
    _vBottomBetBtns: MiniGameCommonDef.GameNodeScale[] = []; // 底部下注按钮数组(用于适配位置)
    _betButtonNum: number = 5; // 下注按钮数量
    @property({
        type: cc.Float,
        tooltip: '下注筹码原始缩放比例'
    })
    fBetBtnSrcScaleRate: number = 0.75; // 下注筹码原始缩放比例
    _fBetBtnTarScaleRate: number = 1.0; // 下注筹码目标缩放比例
    _fFlyCoinScaleRate: number = 0.5; // 创建的金币缩放比例

    @property(cc.Color) toastLabelColor: cc.Color = cc.Color.WHITE;
    @property toastLabelSize: number = 30;

    _curBetButtonIdx: number = 0;
    bottom_bg: cc.Node = null;
    _panel_betbtn: cc.Node = null;

    _btnBetAuto: cc.Button = null;
    _btnBetClean: cc.Button = null; // 清屏

    // areaIdx <. xxx
    _betAreas: cc.Node[] = [];
    _betCoinContents: cc.Node[] = [];
    _textSelfBetNum: cc.Label[] = [];
    _textTotalBetNum: cc.Label[] = [];
    _oriTextSelfBetNumPos: cc.Vec2[] = [];
    _oriTextTotalBetNumPos: cc.Vec2[] = [];
    _sprBetAreaWinFlags: cc.Sprite[] = [];
    _textBetAreaOdds: cc.Label[] = [];
    _mapBetOptionArea: Map<number, number> = new Map(); // 映射：BetZoneOption <. index of this._betAreas

    _betCountDown: cc.Node = null; // 闹钟背景
    _textCountDown: cc.Label = null; // 闹钟时间文本
    _oriBetCountDownBgPos: cc.Vec2 = new cc.Vec2(0, 0); // 闹钟背景初始位置

    // 个人信息
    _textNickName: cc.Label = null;
    _textCoin: cc.Label = null;
    _selfHeadBg: cc.Node = null;
    _selfCoin: cc.Sprite = null;
    protected selfAvatar: AvatarControl = null;

    _otherPlayerHeads: OtherPlayerHead[] = [];

    _leftPlayerPanel: cc.Node = null;
    _rightPlayerPanel: cc.Node = null;

    _heroBoy: cc.Node = null; // 牛仔
    _heroCow: cc.Node = null; // 小牛

    // 动画
    _nodeAnim: cc.Node = null; // 动态动画节点
    _timelineNodeAnim: cc.Node = null; // 动画节点

    _roundStartAnim: cc.Node = null;
    _roundStartAction: cc.Animation = null;
    _fightBeginAnim: cc.Node = null;
    _fightBeginAction: cc.Animation = null;
    _fightEndAnim: cc.Node = null;
    _fightEndAction: cc.Animation = null;
    _waitForNextRoundAnim: cc.Node = null;
    _waitForNextRoundAction: cc.Animation = null;
    _effNode: cc.Node = null;
    _prizeAnim: cc.Node = null;
    _prizeAction: cc.Animation = null;
    _prizeActionIndex: number = 0;

    _winFlagAnims: cc.Node[] = [];
    _winFlagActions: cc.Animation[] = [];

    // 游戏提示语
    _gameTipsBg: cc.Node = null;
    _textGameTips: cc.Label = null;

    // 牛仔中奖信息
    _rewardPanel: cc.Node = null;
    _rewardTips: cc.RichText = null;
    _rewardPanel_width: number = 0;

    // 历史纪录 新手提示
    _recordDotsTemp: cc.Sprite[] = [];
    // 历史纪录
    _recordDots: cc.Sprite[] = [];
    _oriRecordDotsPos: cc.Vec2[] = [];
    _lastRecordDotWorldPos: cc.Vec2; // 显示的最后一个球的世界坐标
    _recordNum: number = 12; // 历史纪录的数量

    _humanboyGuide: cc.Node = null; // 引导面板
    _humanboyMenu: cc.Node = null; // 菜单面板
    _humanboyAdvancedSetting: cc.Node = null; // 高级设置面板
    _humanboyAdvancedAuto: cc.Node = null; // 高级续投面板
    _eAutoBtnStyle: number = MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NONE; // 续投按钮样式
    protected eGameboyScreenType: MiniGameCommonDef.eGameboyScreenType =
        MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW; // 屏幕类型

    _cowboyChart: cc.Node = null;
    _cowboyChartControl: CowboyChartControl = null;
    _cowboyExchange: MiniGameExchangeControl = null;
    _cowboyRule: cc.Node = null;
    _cowboySetting: cc.Node = null;
    _cowboyExit: cc.Node = null;
    _cowboyList: cc.Node = null;
    //  _cowboyList: CowboyList = null;
    // Chart * this._chart = null;
    // CowboyMilitary * _minitary = null;
    // Dznzsetting * _dznzsetting = null;
    // Dznzexit * _dznzexit = null;
    // _rule: CowboyRule = null;

    // toast
    _toastNode: cc.Node = null;

    _HEAD_IMG_TAG: string = 'cowboy_head_tag';

    _openIphoneXAdapter: boolean = true; // iPhoneX适配开关
    _leftTime: number = 0;

    protected _btn_redpacket_festival: cc.Node = null; // 红包节按钮
    protected _btn_redpacket_festival_layer: cc.Node = null; // 红包节按钮提示层
    private _luckButton: LuckTurntableButtonControl = null; // 红包节实例

    _waitForNextRoundOutTheshould: number = 2; // 剩余时间小于此值播放退出动画

    // 新进房间动画节奏时间
    _betCountDownEndDuration: number = 0.3;
    _fightEndDuration: number = 1.05;
    _showHandCardsDuration: number = 1.0;
    _showPublicCardsDuration: number = 1.5;
    _hideLoseBetCoinsDuration: number = 1.5;
    _specialCardTypeDuration: number = 8; // 特殊牌型动画时间,在显示win动画之前
    _betWinFlagsAndFlyCoinsDuration: number = 2.7;
    _showNextRoundDuration: number = 3;

    AREA_SIZE: number = 10;
    trend_anim: cc.AnimationClip = null;

    _coinNode: cc.Node = null;
    _coinNodeByArea: cc.Node[][] = [];
    // _circlePool: cc.NodePool = new cc.NodePool();
    // _squarePool: cc.NodePool = new cc.NodePool();
    _coinPool: cc.NodePool = new cc.NodePool();

    _areaCoinMax: number[] = [200, 200, 200, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100];
    // _isAddEvent: boolean = false;

    _mapWayOutInfo: Map<number, CowboyWayOutInfo> = new Map(); // 路单结构信息数组
    _fActExecute_WayOut: number = 1.0; // 显示路子动画 执行时间

    _cowWinAnim: cc.Node = null; // 牛赢动画
    _cowWinAction: cc.Animation = null;
    _fActExecute_CowWin: number = 3.0;

    _cowLoseAnim: cc.Node = null; // 牛输动画
    _cowLoseAction: cc.Animation = null;
    _fActExecute_CowLose: number = 3.0;

    _boyWinAnim: cc.Node = null; // 牛仔赢动画
    _boyWinAction: cc.Animation = null;
    _fActExecute_BoyWin: number = 3.0;

    _boyLoseAnim: cc.Node = null; // 牛仔输动画
    _boyLoseAction: cc.Animation = null;
    _fActExecute_BoyLose: number = 3.0;
    _vCoinOptimizationDeque: Deque<PlayerOneBet> = new Deque();

    // private _msInterval: number = 1; // 定时器间隔(单位: 秒)
    // private _msNowTime: number = 0; // 当前时间
    // private _msLastTime: number = 0; // 上次时间
    // private _nLeftTime: number = 0; // 剩余时间
    // private _nMinTime: number = 0; // 最小时间
    // private _isIpad: boolean = false;
    private _isEnterBackground: boolean = false;
    // private _effectMap: HashMap<string, effectLoop> = new HashMap();
    // private _isViewX: boolean = false;
    protected _bSwitchTable: boolean = false;
    private consumingNotify: ConsumingPromptControl = null;

    protected _cowboyRoom: pf.Nullable<domain.CowboyRoom> = null;
    protected _authService: pf.services.AuthService = null;
    protected _walletService: pf.services.WalletService = null;
    private _luckTurntableService: pf.services.LuckTurntableService = null;
    private _rebateService: pf.services.RebateService = null;
    private _pushNotificationService: pf.services.PushNotificationService = null;

    private _boundLuckTurntableStartOrEnd = this.showLuckButton.bind(this);
    private _boundLuckTurntableResult = this.onTurntableResultNotice.bind(this);
    private _boundEnterBackgroundHandler = this.OnAppEnterBackground.bind(this);
    private _boundEnterForegroundHandler = this.OnAppEnterForeground.bind(this);
    private _boundUpdateGoldHandler = this._onMsgUpdateWorldServerGold.bind(this);
    private _boundRebateEventStop = this.hideRebateActivity.bind(this);
    private _boundPushNotification = this._onPushNotification.bind(this);
    private _boundGetRebateEventStatus = this._getRebateEventStatus.bind(this);
    private _boundRebateEventStatusNotify = this._onRebateStatusNotify.bind(this);
    private _boundRebateClaimNotify = this._onResponseRebateReceiveReward.bind(this);

    protected _atlasDZNZ: cc.SpriteAtlas = null;

    protected _rebateEventStatus: pf.client.IEventStatusClient = null;

    private _platform: string = '';

    private _lastSoundTime: number = 0;
    private _lastSoundName: string;
    private _rebateCoinsFly: cc.Node = null;
    private _boundBarMaxAnimationEnd = this.onBarMaxAnimationEnd.bind(this);
    protected _rebate_float_button: cc.Node = null;

    private _rebateClaimToast: Function = null;
    private _lastTimeGetRebateEventStatus: number = 0;
    private _lastSystemTime: number = 0;
    private _lastTimeClick = 0;

    onLoad() {
        this.language_PLIST = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_LANGUAGE_ATLAS);
        this._atlasDZNZ = pf.addressableAssetManager.getAsset(macros.Assets.DZNZ_ATLAS);
        // cv.resMgr.adaptWidget(this.node, true);
        // layer.initScent();  未处理

        // TODO: not dealt with
        // cv.pushNotice.hideNoticeLayer();

        if (pf.app.clientType === pf.client.ClientType.CowboyWeb) {
            cc.find('bottomPanel/self_panel/btn_shop_valid', this.node).active = false;
        }

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        this._cowboyRoom = context.room as domain.CowboyRoom;

        this._authService = pf.serviceManager.get(pf.services.AuthService);
        this._walletService = pf.serviceManager.get(pf.services.WalletService);
        this._luckTurntableService = pf.serviceManager.get(pf.services.LuckTurntableService);
        this._rebateService = pf.serviceManager.get(pf.services.RebateService);
        this._pushNotificationService = pf.serviceManager.get(pf.services.PushNotificationService);

        this._platform = context.platform || 'pkw';

        pf.app.setCurrentScene(macros.SCENE_NANE);
        // pf.system.view.adaptScreenHen(this.node);
        // pf.UIUtil.adaptWidget(this.node, true);
    }

    // on "init" you need to initialize your instance
    start() {
        pf.UIUtil.adaptWidget(this.node, true);

        // TODO: not dealt with
        // if (cv.SHOP && cv.SHOP.msgNode) cv.SHOP.msgNode.active = false;
        // cv.pushNotice.setPushNoticeType(PushNoticeType.PUSH_COWBOY);

        this._gameContent = this.node.getChildByName('game_content');

        this._bottomPanel = this.node.getChildByName('bottomPanel');
        // console.log('this._bottomPanel.position = ' + this._bottomPanel.position);

        // 暂时不要提示
        this._gameTipsBg = this.node.getChildByName('game_tips_bg');
        this._textGameTips = this._gameTipsBg.getChildByName('text_game_tips').getComponent(cc.Label);
        this._gameTipsBg.active = false;

        // 牛仔中奖信息
        this._rewardPanel = this.node.getChildByName('rewardPanel');
        this._rewardPanel_width = this._rewardPanel.width;
        this._rewardTips = this._rewardPanel.getChildByName('notice_text').getComponent(cc.RichText);
        this._rewardPanel.active = false;

        this._panel_betbtn = this._bottomPanel.getChildByName('panel_betbtn');

        this._coinNode = new cc.Node();
        this.node.addChild(this._coinNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_COIN_NODE);
        this._coinNode.setContentSize(pf.system.view.width, pf.system.view.height);

        this._nodeAnim = new cc.Node();
        this.node.addChild(this._nodeAnim, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ANIM_NODE);
        this._nodeAnim.setContentSize(pf.system.view.width, pf.system.view.height);

        this._timelineNodeAnim = new cc.Node();
        this.node.addChild(this._timelineNodeAnim, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TIMELINE_NODE);
        this._timelineNodeAnim.setContentSize(pf.system.view.width, pf.system.view.height);
        this.initDialogHub();
        this.initTimelineAnims();
        this.initCowboyAnims();
        this.initCowboyToastNode();
        this.initButtonEvents();
        this.initCards();
        this.initBetArea();
        this.initBetButtons();
        this.initBetCountDown();
        this.initPlayersInfo();
        this.initHistoryDots();

        this.adaptiveScreen(); // iPad/iPhoneX等宽窄屏适配
        this._fitSafeArea();
        this.initRedPackage(); // 初始化红包按钮入口
        this.adaptiveBetBtnPanel(); // 适配下注按钮面板布局

        this.initWinFlagAnims();
        this.initTrendChangeAnim();
        this.initGuide();
        this.initChart();

        this._addObservers();

        this.clearRound(); // 清除场景动态信息
        this.betButtonSelected(0, true); // 默认第一个按钮选中

        // this.playCowboyBGM();
        this.OnSoundSwitchNotify();

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        // context.exitCallback = this.exitGame.bind(this);
        context.exitCallback = this.tryLeaveRoom.bind(this);

        if (this._cowboyRoom.isDataSynced) {
            this.OnGameDataSynNotify();
        }
    }

    onDestroy() {
        this._cleanData();
    }

    /**
     * 游戏进入后台时触发的事件
     */
    OnAppEnterBackground(): void {
        // 私语版本, 切回后台后，将所有音频暂停
        // if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.H5WebPage) {
        // if (cc.sys.os == cc.sys.OS_ANDROID) {
        //     cv.AudioMgr.stopMusic();
        //     cv.AudioMgr.pauseAll();
        // } else {
        //     if (!cv.tools.isPlayMusic()) {
        //         cv.AudioMgr.play(this.silenceMusic, true, 0.5, true);
        //     }
        // }
        pf.audioManager.stopAll();

        // cv.tools.setEnterbackState(true);

        // this._effectMap.forEach((key: string, value: effectLoop, i?: number) => {
        //     cc.audioEngine.stopEffect(value.audioId);
        //     this.unschedule(value.func);
        // });
        // this._effectMap.clear();
        this._isEnterBackground = true;

        // 解决结算飞金币时疯狂秒切前后台卡死的bug, 原因是依赖"this"的定时器回调后金币对象已被销毁
        // 停止根节点所有定时器和动画回调(暂时只能写在房间同步逻辑之前, 否则会造成音效循环播放bug)
        this.node.stopAllActions();
        this.unscheduleAllCallbacks();
    }

    /**
     * 游戏进入前台运行时触发的事件
     */
    OnAppEnterForeground(): void {
        // if (cc.sys.isBrowser && cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.H5WebPage) {
        // cv.tools.setEnterbackState(false);
        // if (cc.sys.os == cc.sys.OS_ANDROID) {
        //     cv.AudioMgr.resumeAll();
        //     this.OnSoundSwitchNotify();
        // } else {
        //     if (!cv.tools.isPlayMusic()) {
        //         cv.AudioMgr.stop(cv.AudioMgr.getAudioID(this.silenceMusic));
        //     }
        // }
        // }
        pf.audioManager.playMusic(macros.Audio.BGM);

        this._isEnterBackground = false;
    }

    protected update(dt: number): void {
        if (this._vCoinOptimizationDeque.size() <= 0) return;
        this._updateCoinOptimization(dt);
    }

    /**
     * 刷新"金币最优队列"(每帧创建, 稳定帧率)
     */
    private _updateCoinOptimization(dt: number): void {
        let nTotalCount: number = this._vCoinOptimizationDeque.size();
        if (nTotalCount <= 0) return;

        if (this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            let nCount = 0;

            // 剩余时间 > 1s 逐帧喷吐金币
            if (this._leftTime > 1) {
                nCount = nTotalCount / cc.game.getFrameRate();
                nCount = Math.ceil(nCount);
            }
            // 否则, 一次性喷吐剩余金币(弥补金币数量多、卡帧导致喷吐金币不完整的情况)
            else {
                nCount = nTotalCount;
            }

            // console.log(cv.StringTools.formatC("CowboyGame_Coin: sec = %02d, dt = %05f, total = %05f, count = %05f", this._getLeftTime(), dt, nTotalCount, nCount));

            for (let i = 0; i < nCount; ++i) {
                let t: PlayerOneBet = this._vCoinOptimizationDeque.pop_front();
                this.showBetInAnim(t);
            }
        } else {
            // 更新剩余的金币数等(在卡帧情况下, 计时误差等情况下, 飞金币被强行停止, 但数据要保持最新, 因为这是一个逐帧队列, 不是及时更新)
            for (let i = 0; i < nTotalCount; ++i) {
                let t: PlayerOneBet = this._vCoinOptimizationDeque.pop_front();
                this.updatePlayerCoin(t.uid);
                this.updateBetArea(t.betOption);
            }

            // 清除队列
            this._vCoinOptimizationDeque.clear();
        }
    }

    // ingorePutInQuenue(path: string) {
    //     //私语平台，开始下注，停止下注不放在队列播放
    //     if (
    //         path != this.s_begin_bet &&
    //         path != this.s_end_bet &&
    //         path != this.s_win_lose &&
    //         path != this.s_special_card_type &&
    //         path != this.s_time_tick
    //     ) {
    //         return true;
    //     }

    //     return false;
    // }

    private _addObservers() {
        this._cowboyRoom.addListener('dataSync', this.OnGameDataSynNotify.bind(this));

        // cv.MessageCenter.register('on_cowboy_game_round_end_notify', this.OnGameRoundEndNotify.bind(this), this.node);
        this._cowboyRoom.addListener('gameRoundEnd', this.OnGameRoundEndNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_deal_notify', this.OnDealNotify.bind(this), this.node);
        this._cowboyRoom.addListener('deal', this.OnDealNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_start_bet_notify', this.OnStartBetNotify.bind(this), this.node);
        this._cowboyRoom.addListener('startBet', this.OnStartBetNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_bet_notify', this.OnBetNotify.bind(this), this.node);
        this._cowboyRoom.addListener('bet', this.OnBetNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_auto_bet_notify', this.OnAutoBetNotify.bind(this), this.node);
        this._cowboyRoom.addListener('autoBet', this.OnAutoBetNotify.bind(this));
        // cv.MessageCenter.register(
        //     'on_cowboy_auto_bet_notify_handle_over',
        //     this.OnAutoBetNotifyHandleOver.bind(this),
        //     this.node
        // );
        // cv.MessageCenter.register('on_cowboy_leave_room_succ', this.OnLeaveRoomSucc.bind(this), this.node);
        // cv.MessageCenter.register('on_cowboy_auto_bet_succ', this.OnAutoBetSucc.bind(this), this.node);
        // cv.MessageCenter.register(
        //     'on_cowboy_room_param_change_notify',
        //     this.OnRoomParamChangeNotify.bind(this),
        //     this.node
        // );
        this._cowboyRoom.addListener('roomParamChange', this.OnRoomParamChangeNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_server_error', this.OnServerError.bind(this), this.node);
        this._cowboyRoom.addListener('serverError', this.OnServerError.bind(this));
        // cv.MessageCenter.register('on_cowboy_kick_notify', this.OnKickNotify.bind(this), this.node);
        this._cowboyRoom.addListener('kicked', this.OnKickNotify.bind(this));
        // cv.MessageCenter.register('on_cowboy_join_room_failed', this.OnJoinRoomFailed.bind(this), this.node);
        // cv.MessageCenter.register('showShopPanel', this.openShop.bind(this), this.node);

        // cv.MessageCenter.register('showLuckButton', this.showLuckButton.bind(this), this.node); // 红包节
        // cv.MessageCenter.register('turntableResultNotice', this.onTurntableResultNotice.bind(this), this.node);

        // cv.MessageCenter.register('update_gold', this._onMsgUpdateWorldServerGold.bind(this), this.node); // world服金币有变动通知
        // cv.MessageCenter.register(
        //     'on_cowboy_bet_amount_level_change',
        //     this._onMsgBetAmountLevelChange.bind(this),
        //     this.node
        // ); // 下注级别变更
        this._cowboyRoom.addListener('betCoinOptionsChange', this._onMsgBetAmountLevelChange.bind(this)); // 下注级别变更
        // cv.MessageCenter.register('on_cowboy_advance_autobet_set', this._onMsgAdvanceAutobetSet.bind(this), this.node); // 设置高级续投次数成功
        this._cowboyRoom.addListener('advanceAutoBetCountSet', this._onMsgAdvanceAutobetSet.bind(this)); // 设置高级续投次数成功
        // cv.MessageCenter.register('on_advance_autobet', this._onMsgAdvanceAutobet.bind(this), this.node); // 高级续投
        this._cowboyRoom.addListener('advanceAutoBet', this._onMsgAdvanceAutobet.bind(this)); // 高级续投
        // cv.MessageCenter.register(
        //     'on_cowboy_advance_autobet_cancel',
        //     this._onMsgAdvanceAutobetCancel.bind(this),
        //     this.node
        // ); // 取消高级续投成功
        this._cowboyRoom.addListener('advanceAutoBetCancel', this._onMsgAdvanceAutobetCancel.bind(this)); // 取消高级续投成功
        // cv.MessageCenter.register(
        //     'on_cowboy_advance_autobet_limit_reached',
        //     this._onMsgAdvanceAutobetLimitReached.bind(this),
        //     this.node
        // ); // 高级续投接近或者已达上限
        this._cowboyRoom.addListener('advanceAutoBetLimitReached', this._onMsgAdvanceAutobetLimitReached.bind(this)); // 高级续投接近或者已达上限
        this._cowboyRoom.addListener('advanceAutoBetCountAdd', this.onMsgAdvanceAutobetAdd.bind(this));

        // cv.MessageCenter.register('on_cowboy_gamedata_syn_notify', this.OnGameDataSynNotify.bind(this), this.node);
        // cv.MessageCenter.register('on_update_trend_notify', this.OnTrendUpdate.bind(this), this.node);
        this._cowboyRoom.addListener('trendNotify', this.OnTrendUpdate.bind(this));
        // cv.MessageCenter.register('on_update_playerlist_notify', this.OnPlayerListUpdate.bind(this), this.node);
        // cv.MessageCenter.register('on_cowboy_sound_switch_notify', this.OnSoundSwitchNotify.bind(this), this.node);
        // cv.MessageCenter.register('on_selfinfo_notify', this.OnSelfInfo.bind(this), this.node);
        // cv.MessageCenter.register('showMedalMsg', this.OnCowboyRewardTips.bind(this), this.node);
        // cv.MessageCenter.register('goldViewShop', this.onGoldViewShop.bind(this), this.node);
        // cv.MessageCenter.register('onNoticeOpenCalmDownWindow', this.onCalmDownShowTip.bind(this), this.node);

        this._cowboyRoom.addListener('leftGameCoin', this._onMsgConsumingNotify.bind(this));

        this._cowboyRoom.addListener('leaveRoom', this.exitGame.bind(this));

        // cv.MessageCenter.register('onCalmDownMsg', this.onCalmDownShowTip.bind(this), this.node);
        // cv.MessageCenter.register('on_advance_autobet_add', this.onMsgAdvanceAutobetAdd.bind(this), this.node);
        // cv.MessageCenter.register(
        //     'onResponseRebateEventStatus',
        //     this._onResponseRebateEventStatus.bind(this),
        //     this.node
        // ); // 高级续投接近或者已达上限
        // cv.MessageCenter.register(
        //     'onResponseRebateReceiveReward',
        //     this._onResponseRebateReceiveReward.bind(this),
        //     this.node
        // ); // 高级续投接近或者已达上限
        // cv.MessageCenter.register('on_rebate_reward_popup', this.showRebateRewardPopup.bind(this), this.node);
        // cv.MessageCenter.register('onRebateEventStop', this.hideRebateActivity.bind(this), this.node);

        // 私语版本，走私语切换后台注册
        // if (cv.config.isSiyuType()) {
        //     cv.MessageCenter.register('on_syOnEnterBackground', this.OnAppEnterBackground.bind(this), this.node);
        //     cv.MessageCenter.register('on_syOnEnterForeground', this.OnAppEnterForeground.bind(this), this.node);
        // } else {
        //     cc.game.on(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
        //     cc.game.on(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
        // }
        pf.app.addListener('appEnterBackground', this._boundEnterBackgroundHandler);
        pf.app.addListener('appEnterForeground', this._boundEnterForegroundHandler);

        this._walletService.addListener('userGoldNum', this._boundUpdateGoldHandler);

        this._luckTurntableService.addListener('luckTurntableStart', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.addListener('luckTurntableEnd', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.addListener('luckTurntableResult', this._boundLuckTurntableResult);

        this._rebateService.addListener('eventStatusStop', this._boundRebateEventStop);
        this._rebateService.addListener('refreshEventStatus', this._boundRebateEventStatusNotify);
        this._rebateService.addListener('rebateRewardResult', this._boundRebateClaimNotify);

        this._pushNotificationService.addListener('pushNotification', this._boundPushNotification);
    }

    private _removeObservers() {
        // cv.MessageCenter.unregister('on_cowboy_gamedata_syn_notify', this.node);
        // cv.MessageCenter.unregister('on_update_trend_notify', this.node);
        // cv.MessageCenter.unregister('on_update_playerlist_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_sound_switch_notify', this.node);
        // cv.MessageCenter.unregister('on_selfinfo_notify', this.node);
        // cv.MessageCenter.unregister('showMedalMsg', this.node);// TODO: not dealt with
        // cv.MessageCenter.unregister('goldViewShop', this.node);

        // cv.MessageCenter.unregister('on_cowboy_game_round_end_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_deal_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_start_bet_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_bet_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_auto_bet_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_auto_bet_notify_handle_over', this.node);
        // cv.MessageCenter.unregister('on_cowboy_leave_room_succ', this.node);
        // cv.MessageCenter.unregister('on_cowboy_auto_bet_succ', this.node);
        // cv.MessageCenter.unregister('on_cowboy_room_param_change_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_server_error', this.node);
        // cv.MessageCenter.unregister('on_cowboy_kick_notify', this.node);
        // cv.MessageCenter.unregister('on_cowboy_join_room_failed', this.node);
        // cv.MessageCenter.unregister('showShopPanel', this.node);

        // cv.MessageCenter.unregister('showLuckButton', this.node);
        // cv.MessageCenter.unregister('turntableResultNotice', this.node);

        // cv.MessageCenter.unregister('update_gold', this.node);
        // cv.MessageCenter.unregister('on_cowboy_bet_amount_level_change', this.node);
        // cv.MessageCenter.unregister('on_cowboy_advance_autobet_set', this.node);
        // cv.MessageCenter.unregister('on_advance_autobet', this.node);
        // cv.MessageCenter.unregister('on_cowboy_advance_autobet_cancel', this.node);
        // cv.MessageCenter.unregister('on_cowboy_advance_autobet_limit_reached', this.node);
        // cv.MessageCenter.unregister('onNoticeOpenCalmDownWindow', this.node);
        // cv.MessageCenter.unregister('onCalmDownMsg', this.node);
        // cv.MessageCenter.unregister('on_advance_autobet_add', this.node);

        // cv.MessageCenter.unregister('onResponseRebateEventStatus', this.node);
        // cv.MessageCenter.unregister('onResponseRebateReceiveReward', this.node);
        // cv.MessageCenter.unregister('on_rebate_reward_popup', this.node);
        // cv.MessageCenter.unregister('onRebateEventStop', this.node);

        // if (cv.config.isSiyuType()) {
        //     cv.MessageCenter.unregister('on_syOnEnterBackground', this.node);
        //     cv.MessageCenter.unregister('on_syOnEnterForeground', this.node);
        // } else {
        //     cc.game.off(cc.game.EVENT_HIDE, this.OnAppEnterBackground, this);
        //     cc.game.off(cc.game.EVENT_SHOW, this.OnAppEnterForeground, this);
        // }
        pf.app.removeListener('appEnterBackground', this._boundEnterBackgroundHandler);
        pf.app.removeListener('appEnterForeground', this._boundEnterForegroundHandler);

        this._walletService.removeListener('userGoldNum', this._boundUpdateGoldHandler);

        this._luckTurntableService.removeListener('luckTurntableStart', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.removeListener('luckTurntableEnd', this._boundLuckTurntableStartOrEnd);
        this._luckTurntableService.removeListener('luckTurntableResult', this._boundLuckTurntableResult);

        this._rebateService.removeListener('eventStatusStop', this._boundRebateEventStop);
        this._rebateService.removeListener('refreshEventStatus', this._boundRebateEventStatusNotify);
        this._rebateService.removeListener('rebateRewardResult', this._boundRebateClaimNotify);
        this._pushNotificationService.removeListener('pushNotification', this._boundPushNotification);
        this._rebateActivity?.node?.off('barMaxAnimationEnd', this._boundBarMaxAnimationEnd);
    }

    protected _playSoundEffect(name: string): void {
        // if (cv.tools.isSoundEffectOpen() && this._isEnterBackground == false) {
        //     //
        //     if (cc.sys.isBrowser && this.ingorePutInQuenue(path)) {
        //         this.playEffectForPath(path);
        //     } else {
        //         cv.AudioMgr.playEffect(path);
        //     }
        // }
        pf.audioManager.playSoundEffect(name);
    }

    protected _backToCowboyListScene(): void {
        // cb.getCowboyRoom().Reset();
        // cv.netWorkManager.closeGameConnect();

        // cv.viewAdaptive.isselfchange = false;
        // cv.viewAdaptive.cowboyroomid = 0;

        // 回到牛仔房间列表界面
        // cv.roomManager.reset();
        // cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene) => {
        //     cv.MessageCenter.send('switchSceneToMiniGame');
        // });
        // this.exitGame();
        this.tryLeaveRoom();
    }

    private _cleanData(): void {
        this._removeObservers();

        // this.stopCowboyBGM();
        this.unschedule(this.onLeftTimeUpdate);

        // 清除路单数组信息
        this._clearWayOutInfo();

        pf.audioManager.stopAll();
        this.GetDialogHub().processClose();
    }

    private _backToMainScene(backToMainTips?: string): void {
        const checkedTip = backToMainTips === undefined ? '' : backToMainTips;

        // cv.netWorkManager.closeGameConnect();
        // cb.getCowboyRoom().backToMainTips = backToMainTips;

        // 回到大厅
        // cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE);

        const context = pf.app.getGameContext<pf.services.MiniGameContext>();
        context.backToMainTips = checkedTip;
        // this.exitGame();
        this.tryLeaveRoom();
    }

    // 清除场景动态信息
    clearRound(): void {
        this.hideAllWinPlayerLightAnim();
        this.hideAllCardsAndCardType();
        this.hideWinCards();
        this.clearAllBetArea();
        this.hideBetCountDown();
        this.showCowboyNormalAnim();
        this._nodeAnim.destroyAllChildren();
        this._nodeAnim.removeAllChildren(true);
        this._nodeAnim.stopAllActions();
        this.hideAllTimelineAnims();
        this.hideGameTips();
        this.hideHistoryMoveAnim();
        this.resetPointAni();
        // this._hideWayOutMoveAnim();
        this.hideTrendChangeAnim();
        // clearAllCowboyToasts();
    }

    protected createPokerCard(): PokerCardControl {
        const ctrl = cc
            .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.POKER_CARD))
            .getComponent(PokerCardControl);
        ctrl.init();
        ctrl.setCardBackSpriteFrame(pf.addressableAssetManager.getAsset(macros.Assets.CARD_BACK));
        return ctrl;
    }

    // /* 牌型：（redLevel/blueLevel）
    // 皇家同花顺：10
    // 同花顺    ：9
    // 四条      ：8
    // 葫芦      ：7
    // 同花      ：6
    // 顺子      ：5
    // 三条      ：4
    // 两对      ：3
    // 一对      ：2
    // 高牌      ：1
    // */
    initCards(): void {
        this._cardPanel = this._gameContent.getChildByName('public_card_panel');
        this._oriRedHandCards.push(this._cardPanel.getChildByName('handcard_red_0').getComponent(cc.Sprite));
        this._oriRedHandCards.push(this._cardPanel.getChildByName('handcard_red_1').getComponent(cc.Sprite));
        this._oriBlueHandCards.push(this._cardPanel.getChildByName('handcard_blue_0').getComponent(cc.Sprite));
        this._oriBlueHandCards.push(this._cardPanel.getChildByName('handcard_blue_1').getComponent(cc.Sprite));
        for (let i = 0; i < 2; i++) {
            let RedHandpos: cc.Vec2 = cc.v2(this._oriRedHandCards[i].node.getPosition());
            let BlueHandpos: cc.Vec2 = cc.v2(this._oriBlueHandCards[i].node.getPosition());
            this._oriRedHandCardsPos.push(RedHandpos);
            this._oriBlueHandCardsPos.push(BlueHandpos);
            this._redHandCards.push(this.createPokerCard());
            this._blueHandCards.push(this.createPokerCard());
            this._redHandCards[i].ResetFromNode(this._oriRedHandCards[i].node);
            this._blueHandCards[i].ResetFromNode(this._oriBlueHandCards[i].node);
        }

        for (let i = 0; i < 5; i++) {
            this._oriPublicCards.push(
                this._cardPanel.getChildByName(pf.StringUtil.formatC('handcard_%d', i)).getComponent(cc.Sprite)
            );
            let pos: cc.Vec2 = cc.v2(this._oriPublicCards[i].node.getPosition());
            this._oriPublicCardsPos.push(pos);
            this._publicCards.push(this.createPokerCard());
            this._publicCards[i].ResetFromNode(this._oriPublicCards[i].node);
        }

        this._redCardType = this._cardPanel.getChildByName('red_card_type').getComponent(cc.Sprite);
        this._blueCardType = this._cardPanel.getChildByName('blue_card_type').getComponent(cc.Sprite);
        this._redCardTypeBg = this._cardPanel.getChildByName('red_card_type_bg').getComponent(cc.Sprite);
        this._blueCardTypeBg = this._cardPanel.getChildByName('blue_card_type_bg').getComponent(cc.Sprite);
        this._redCardTypeBg.node.zIndex = 1;
        this._blueCardTypeBg.node.zIndex = 1;
        this._redCardType.node.zIndex = 2;
        this._blueCardType.node.zIndex = 2;

        this._mapLevelCardTypeImage.set(1, 'gaopai');
        this._mapLevelCardTypeImage.set(2, 'yidui');
        this._mapLevelCardTypeImage.set(3, 'liangdui');
        this._mapLevelCardTypeImage.set(4, 'santiao');
        this._mapLevelCardTypeImage.set(5, 'shunzi');
        this._mapLevelCardTypeImage.set(6, 'tonghua');
        this._mapLevelCardTypeImage.set(7, 'hulu');
        this._mapLevelCardTypeImage.set(8, 'jingang');
        this._mapLevelCardTypeImage.set(9, 'tonghuashun');
        this._mapLevelCardTypeImage.set(10, 'huangtong');
    }

    hideAllCardsAndCardType(): void {
        this.setAllCardsVisible(false);

        this._redCardType.node.active = false;
        this._blueCardType.node.active = false;
        this._redCardTypeBg.node.active = false;
        this._blueCardTypeBg.node.active = false;
    }

    setAllHandCardsVisible(visible: boolean): void {
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].node.active = visible;
            this._blueHandCards[i].node.active = visible;
            this._redHandCards[i].node.stopAllActions();
            this._blueHandCards[i].node.stopAllActions();
            this._redHandCards[i].unscheduleAllCallbacks();
            this._blueHandCards[i].unscheduleAllCallbacks();
            if (visible) {
                this._redHandCards[i].node.setPosition(this._oriRedHandCardsPos[i]);
                this._blueHandCards[i].node.setPosition(this._oriBlueHandCardsPos[i]);
            }
        }
    }

    setAllPublicCardsVisible(visible: boolean): void {
        for (let i = 0; i < 5; i++) {
            this._publicCards[i].node.active = visible;
            this._publicCards[i].node.stopAllActions();
            this._publicCards[i].unscheduleAllCallbacks();
            if (visible) {
                this._publicCards[i].node.setPosition(this._oriPublicCardsPos[i]);
            }
        }
    }

    setAllCardsVisible(visible: boolean): void {
        this.setAllHandCardsVisible(visible);
        this.setAllPublicCardsVisible(visible);
    }

    // 直接更新牌：公共牌、手牌
    updateAllCardsBack(): void {
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].SetFace(false);
            this._blueHandCards[i].SetFace(false);
        }

        for (let i = 0; i < 5; i++) {
            this._publicCards[i].SetFace(false);
        }
    }

    // 直接更新牌：公共牌、手牌
    updateCards(): void {
        // update cards
        this.setAllCardsVisible(true);

        // 更新所有背面牌
        this.updateAllCardsBack();

        // 更新所有正面牌
        if (
            this._cowboyRoom.roundInfo.redHandCards.length === 2 &&
            this._cowboyRoom.roundInfo.blueHandCards.length === 2
        ) {
            for (let i = 0; i < 2; i++) {
                this._redHandCards[i].SetContent(
                    this._cowboyRoom.roundInfo.redHandCards[i].number,
                    this._cowboyRoom.roundInfo.redHandCards[i].suit
                );
                this._blueHandCards[i].SetContent(
                    this._cowboyRoom.roundInfo.blueHandCards[i].number,
                    this._cowboyRoom.roundInfo.blueHandCards[i].suit
                );
                this._redHandCards[i].SetFace(true);
                this._blueHandCards[i].SetFace(true);
            }
        }

        let publicCardNum = this._cowboyRoom.roundInfo.publicCards.length;
        if (publicCardNum === 1) {
            console.log('1111111111-》publicCardNum == 1');
            return;
        }

        for (let i = 0; i < 5; i++) {
            if (i < publicCardNum) {
                this._publicCards[i].SetContent(
                    this._cowboyRoom.roundInfo.publicCards[i].number,
                    this._cowboyRoom.roundInfo.publicCards[i].suit
                );
                this._publicCards[i].SetFace(true);
            }
        }
    }

    // 直接更新牌型
    updateCardType(): void {
        this._redCardType.node.active = true;
        this._blueCardType.node.active = true;
        this._redCardTypeBg.node.active = true;
        this._blueCardTypeBg.node.active = true;
        let redCardTypeImage = this._mapLevelCardTypeImage.get(this._cowboyRoom.roundInfo.roundResult.redLevel);
        let blueCardTypeImage = this._mapLevelCardTypeImage.get(this._cowboyRoom.roundInfo.roundResult.blueLevel);

        // 0 平 1 牛仔胜 -1 小牛胜
        if (this._cowboyRoom.roundInfo.roundResult.result === 0) {
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._redCardType, redCardTypeImage + '');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._blueCardType, blueCardTypeImage + '');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._redCardTypeBg, 'win_cardtype_bg');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._blueCardTypeBg, 'win_cardtype_bg');
            this._redCardType.spriteFrame = this.language_PLIST.getSpriteFrame(redCardTypeImage);
            this._blueCardType.spriteFrame = this.language_PLIST.getSpriteFrame(blueCardTypeImage);
            this._redCardTypeBg.spriteFrame = this.language_PLIST.getSpriteFrame('win_cardtype_bg');
            this._blueCardTypeBg.spriteFrame = this.language_PLIST.getSpriteFrame('win_cardtype_bg');
        } else if (this._cowboyRoom.roundInfo.roundResult.result === 1) {
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._redCardType, redCardTypeImage + '');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._blueCardType, blueCardTypeImage + '_gray');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._redCardTypeBg, 'win_cardtype_bg');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._blueCardTypeBg, 'lose_cardtype_bg');
            this._redCardType.spriteFrame = this.language_PLIST.getSpriteFrame(redCardTypeImage);
            this._blueCardType.spriteFrame = this.language_PLIST.getSpriteFrame(blueCardTypeImage + '_gray');
            this._redCardTypeBg.spriteFrame = this.language_PLIST.getSpriteFrame('win_cardtype_bg');
            this._blueCardTypeBg.spriteFrame = this.language_PLIST.getSpriteFrame('lose_cardtype_bg');
        } else if (this._cowboyRoom.roundInfo.roundResult.result === -1) {
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._redCardType, redCardTypeImage + '_gray');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._blueCardType, blueCardTypeImage + '');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._redCardTypeBg, 'lose_cardtype_bg');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._blueCardTypeBg, 'win_cardtype_bg');
            this._redCardType.spriteFrame = this.language_PLIST.getSpriteFrame(redCardTypeImage + '_gray');
            this._blueCardType.spriteFrame = this.language_PLIST.getSpriteFrame(blueCardTypeImage);
            this._redCardTypeBg.spriteFrame = this.language_PLIST.getSpriteFrame('lose_cardtype_bg');
            this._blueCardTypeBg.spriteFrame = this.language_PLIST.getSpriteFrame('win_cardtype_bg');
        }
    }

    updateWinCards(): void {
        // 先全部置灰
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].Gray(true);
            this._blueHandCards[i].Gray(true);
        }
        for (let i = 0; i < 5; i++) {
            this._publicCards[i].Gray(true);
        }

        // 高亮赢的5张牌
        // let winCardNum = cb.getCowboyRoom().winCards.length;
        // for (let i = 0; i < winCardNum; i++) {
        //     let winCard = cb.getCowboyRoom().winCards[i];
        //     let isFind = false;
        //     for (let j = 0; j < 2; j++) {
        //         if (
        //             this._redHandCards[j].GetNumber() == winCard.number &&
        //             this._redHandCards[j].GetSuit() == winCard.suit
        //         ) {
        //             this._redHandCards[j].Gray(false);
        //             isFind = true;
        //             break;
        //         }

        //         if (
        //             this._blueHandCards[j].GetNumber() == winCard.number &&
        //             this._blueHandCards[j].GetSuit() == winCard.suit
        //         ) {
        //             this._blueHandCards[j].Gray(false);
        //             isFind = true;
        //             break;
        //         }
        //     }

        //     if (!isFind) {
        //         for (let j = 0; j < 5; j++) {
        //             if (
        //                 this._publicCards[j].GetNumber() == winCard.number &&
        //                 this._publicCards[j].GetSuit() == winCard.suit
        //             ) {
        //                 this._publicCards[j].Gray(false);
        //                 isFind = true;
        //                 break;
        //             }
        //         }
        //     }
        // }
        this._cowboyRoom.roundInfo.roundResult.Cards.forEach((winCard) => {
            let isFind = false;
            for (let j = 0; j < 2; j++) {
                if (
                    this._redHandCards[j].GetNumber() === winCard.number &&
                    this._redHandCards[j].GetSuit() === winCard.suit
                ) {
                    this._redHandCards[j].Gray(false);
                    isFind = true;
                    break;
                }

                if (
                    this._blueHandCards[j].GetNumber() === winCard.number &&
                    this._blueHandCards[j].GetSuit() === winCard.suit
                ) {
                    this._blueHandCards[j].Gray(false);
                    isFind = true;
                    break;
                }
            }

            if (!isFind) {
                for (let j = 0; j < 5; j++) {
                    if (
                        this._publicCards[j].GetNumber() === winCard.number &&
                        this._publicCards[j].GetSuit() === winCard.suit
                    ) {
                        this._publicCards[j].Gray(false);
                        isFind = true;
                        break;
                    }
                }
            }
        });
    }

    hideWinCards(): void {
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].Gray(false);
            this._blueHandCards[i].Gray(false);
        }
        for (let i = 0; i < 5; i++) {
            this._publicCards[i].Gray(false);
        }
    }

    updateWinFlags(): void {
        let arr = this._cowboyRoom.roundInfo.matchOptions;
        let len = arr.length;
        // 显示win标记/隐藏区域数字和金币
        for (let i = 0; i < len; i++) {
            let areaIdx = this.getAreaIdxByBetOption(arr[i]);
            this.clearBetArea(areaIdx);
            this.hideWinFlagAnim(areaIdx);
            // this._sprBetAreaWinFlags[areaIdx]..node.active = (true);
            this.showWinFlagAnim(areaIdx);
        }
    }

    hideWinFlags(): void {
        let len = this._winFlagAnims.length;
        for (let i = 0; i < len; i++) {
            this.hideWinFlagAnim(i);
            this._sprBetAreaWinFlags[i].node.active = false;
        }
    }

    initCowboyToastNode(): void {
        this._toastNode = new cc.Node();
        this.node.addChild(this._toastNode);
        this._toastNode.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST;
        this._toastNode.setContentSize(pf.system.view.width, pf.system.view.height);
    }

    showCowboyToast(text: string): void {
        let toastBg = new cc.Node().addComponent(cc.Sprite);
        // cv.resMgr.setSpriteFrame(toastBg.node, 'zh_CN/game/cowboy/cowboy_tips_bg');
        pf.addressableAssetManager
            .loadAsset(macros.Dynamic_Assets.COWBOY_TIPS_BACKGROUND)
            .then((asset: cc.SpriteFrame) => {
                toastBg.node.getComponent(cc.Sprite).spriteFrame = asset;
            });
        // cb.loadSpriteTextureByPlist(this._atlasDZNZ, toastBg, "");
        // toastBg.node.setPosition(cv.config.WIDTH, cv.config.HEIGHT / 2);
        this._toastNode.addChild(toastBg.node);

        let textToast = new cc.Node().addComponent(cc.Label);
        textToast.verticalAlign = cc.Label.VerticalAlign.CENTER;
        textToast.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        textToast.fontSize = this.toastLabelSize;
        textToast.lineHeight = 45 * 2;
        // textToast.setColor(Color3B.WHITE);
        textToast.node.color = this.toastLabelColor; // cc.color(140, 204, 156);
        // textToast.node.setPosition(toastBg.node.width / 2, toastBg.node.height / 2);
        textToast.string = text;
        toastBg.node.addChild(textToast.node);

        toastBg.node.runAction(cc.sequence(cc.delayTime(0.1), cc.moveBy(1.0, cc.v2(0, 120))));
        toastBg.node.runAction(
            cc.sequence(cc.delayTime(0.4), cc.fadeOut(0.8).easing(cc.easeInOut(1.0)), cc.destroySelf())
        );
    }

    clearAllCowboyToasts(): void {
        this._toastNode.destroyAllChildren();
        this._toastNode.removeAllChildren(true);
    }

    onLeftTimeUpdate(f32Delta: number): void {
        this._leftTime--;
        this._leftTime = this._leftTime < 0 ? 0 : this._leftTime;
    }

    resetLeftTimer(): void {
        this._leftTime = this._cowboyRoom.gameState.leftSeconds;
        this.unschedule(this.onLeftTimeUpdate);
        this.schedule(this.onLeftTimeUpdate, 1.0);
    }

    protected initDialogHub(): void {}

    protected GetDialogHub(): DialogHubControl {
        return cr.commonResourceAgent.dialogHub;
    }

    // ===============animation begin===============
    initTimelineAnims(): void {
        // 开局动画
        this._roundStartAnim = this.initAni(this._timelineNodeAnim, this.round_start_prefab);
        this._roundStartAction = this._roundStartAnim.getComponent(cc.Animation);
        // this._roundStartAnim.runAction(this._roundStartAction);
        if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
            const cowboyTip = cc.find('WZ1_4', this._roundStartAnim);
            pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.COWBOY_TITLE).then((asset: cc.SpriteFrame) => {
                cowboyTip.getComponent(cc.Sprite).spriteFrame = asset;
            });

            const bullTip = cc.find('WZ2_6', this._roundStartAnim);
            pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.BULL_TITLE).then((asset: cc.SpriteFrame) => {
                bullTip.getComponent(cc.Sprite).spriteFrame = asset;
            });
        }

        // // 出战动画
        this._fightBeginAnim = this.initAni(this._timelineNodeAnim, this.fight_begin_prefab);
        this._fightBeginAction = this._fightBeginAnim.getComponent(cc.Animation);
        // this._fightBeginAnim.runAction(this._fightBeginAction);

        // // 开战动画
        this._fightEndAnim = this.initAni(this._timelineNodeAnim, this.fight_end_prefab);
        this._fightEndAction = this._fightEndAnim.getComponent(cc.Animation);
        // this._fightEndAnim.runAction(this._fightEndAction);

        // // 等待下一局动画
        this._waitForNextRoundAnim = this.initAni(this._timelineNodeAnim, this.wait_for_next_round_prefab);
        this._waitForNextRoundAction = this._waitForNextRoundAnim.getComponent(cc.Animation);
        // this._waitForNextRoundAnim.runAction(this._waitForNextRoundAction);
    }

    initAni(parent: cc.Node, ani_prefab: cc.Prefab): cc.Node {
        let node = cc.instantiate(ani_prefab);
        // node.setPosition(cv.config.WIDTH / 2, cv.config.HEIGHT / 2);
        if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
            let prefabArr: cc.Prefab[] = [
                this.fight_begin_prefab,
                this.fight_end_prefab,
                this.wait_for_next_round_prefab
            ];
            let imgPthArr: string[] = ['CZ', 'KZ', 'xyj'];
            let prefabLen = prefabArr.length;
            let atlas: cc.SpriteAtlas = pf.addressableAssetManager.getAsset(macros.Assets.SPECIAL_CARD_TYPE_ATLAS);
            for (let i = 0; i < prefabLen; i++) {
                if (ani_prefab === prefabArr[i]) {
                    // cb.loadSpriteTextureByPlist(
                    //     this.en_animation_PLIST,
                    //     cc.find('CZ_2', node).getComponent(cc.Sprite),
                    //     imgPthArr[i]
                    // );
                    cc.find('CZ_2', node).getComponent(cc.Sprite).spriteFrame = atlas.getSpriteFrame(imgPthArr[i]);
                    break;
                }
            }
        }
        node.active = false;
        parent.addChild(node);
        return node;
    }

    initWinFlagAnims(): void {
        let len = this._sprBetAreaWinFlags.length;
        // win旗子动画
        for (let i = 0; i < len; i++) {
            let winNode = this._sprBetAreaWinFlags[i].node;
            // let winAnim: cc.Node = this.initAni(this.node, this.win_flag_prefab);
            let winAnim: cc.Node = this.initAni(this.node, pf.addressableAssetManager.getAsset(macros.Assets.WIN_FLAG));
            let winAction: cc.Animation = winAnim.getComponent(cc.Animation);
            winAnim.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ANIM_NODE;
            winAnim.setPosition(
                winAnim.parent.convertToNodeSpaceAR(
                    winNode.parent.convertToWorldSpaceAR(cc.v2(0 + winNode.x, -180 + winNode.y))
                )
            );

            this._winFlagAnims.push(winAnim);
            this._winFlagActions.push(winAction);
        }
    }

    showWinFlagAnim(areaIdx: number): void {
        let betOption = this.getBetOptionByAreaIdx(areaIdx);
        this._winFlagAnims[areaIdx].active = true;
        // win flag animation prefab used in cowboy has only 1 animation: win_flag
        // win flag animation prefab used in humanboy and poker master has 3 animation: win_flag, animation0, animation1
        // since gotoFrameAndPlay only plays default clip, specify animation name to play to get the same result
        // even if default clip is changed somehow
        // this.gotoFrameAndPlay(this._winFlagActions[areaIdx], 0, 145, true);
        this._winFlagActions[areaIdx].play('win_flag');
    }

    hideWinFlagAnim(areaIdx: number): void {
        // this._winFlagAnims[areaIdx].getParent().removeChildByName("special_win_anim");
        if (areaIdx >= pf.StringUtil.getArrayLength(this._winFlagAnims)) return;
        this._winFlagAnims[areaIdx].active = false;
        this.gotoFrameZeroAndPause(this._winFlagActions[areaIdx]);
    }

    showSpecialCardTypeAnim(stayLastFrame?: boolean, lastDuration?: number): void {
        const checkedIsStayLastFrame = stayLastFrame === true ? true : false;
        const checkedLastDuration = lastDuration === undefined ? 0 : lastDuration;
        this.clearSpecialCardTypeAnim();
        if (!this.isResultSpecialCardType()) return;

        let specialBetOption = -1;

        // 优先判断：金刚/同花顺/皇家
        let matchArr = this._cowboyRoom.roundInfo.matchOptions;
        let matchLen = matchArr.length;
        for (let i = 0; i < matchLen; i++) {
            let areaIdx = this.getAreaIdxByBetOption(matchArr[i]);
            let betOption = this.getBetOptionByAreaIdx(areaIdx);

            if (betOption === network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4) {
                specialBetOption = betOption;
                break;
            }
        }
        if (specialBetOption < 0) {
            let matchArr = this._cowboyRoom.roundInfo.matchOptions;
            let matchLen = matchArr.length;
            for (let i = 0; i < matchLen; i++) {
                let areaIdx = this.getAreaIdxByBetOption(matchArr[i]);
                let betOption = this.getBetOptionByAreaIdx(areaIdx);

                if (betOption === network.BetZoneOption.HOLE_A) {
                    specialBetOption = betOption;
                    break;
                }
            }
        }

        if (specialBetOption < 0) return;

        // 胜利牌型
        // 0 平 1 牛仔胜 -1 小牛胜
        let winLevel = 0;
        if (this._cowboyRoom.roundInfo.roundResult.result === 0) {
            winLevel = this._cowboyRoom.roundInfo.roundResult.redLevel;
        } else if (this._cowboyRoom.roundInfo.roundResult.result === 1) {
            winLevel = this._cowboyRoom.roundInfo.roundResult.redLevel;
        } else if (this._cowboyRoom.roundInfo.roundResult.result === -1) {
            winLevel = this._cowboyRoom.roundInfo.roundResult.blueLevel;
        }

        let specialCardType = '';
        let specialCardOdd = '';
        if (specialBetOption === network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4) {
            if (winLevel === 8) {
                // 金刚
                specialCardType = 'special_jingang';
                specialCardOdd = 'special_card_odd_248';
            } else if (winLevel === 9) {
                // 同花顺
                specialCardType = 'special_tonghuashun';
                specialCardOdd = 'special_card_odd_248';
            } else if (winLevel === 10) {
                // 皇家同花顺
                specialCardType = 'special_huangtong';
                specialCardOdd = 'special_card_odd_248';
            } else {
                console.log('showSpecialCardTypeAnim, show special cardtype anim error1');
                return;
            }
        } else if (specialBetOption === network.BetZoneOption.HOLE_A) {
            // 对A
            specialCardType = 'special_duia';
            specialCardOdd = 'special_card_odd_100';
        } else {
            console.log('showSpecialCardTypeAnim, show special cardtype anim error2');
            return;
        }

        let winAnim: cc.Node = this._nodeAnim.getChildByName('special_card_type_anim');
        let winAction: cc.Animation = null;
        if (!winAnim) {
            // 创建动画
            winAnim = this.initAni(this._nodeAnim, this.special_card_type_prefab);
            winAction = winAnim.getComponent(cc.Animation);
            winAnim.name = 'special_card_type_anim';

            // this._winFlagAnims.push(winAnim);
            // this._winFlagActions.push(winAction);
        }
        winAnim.active = true;
        let atlas: cc.SpriteAtlas = pf.addressableAssetManager.getAsset(macros.Assets.SPECIAL_CARD_TYPE_ATLAS);
        // let atlas: cc.SpriteAtlas =
        //     pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN
        //         ? this.special_card_type_PLIST
        //         : this.en_animation_PLIST;
        // cb.loadSpriteTextureByPlist(
        //     atlas,
        //     winAnim.getChildByName('special_card_type').getComponent(cc.Sprite),
        //     specialCardType
        // );
        winAnim.getChildByName('special_card_type').getComponent(cc.Sprite).spriteFrame =
            atlas.getSpriteFrame(specialCardType);
        // cb.loadSpriteTextureByPlist(
        //     atlas,
        //     winAnim.getChildByName('special_card_odd').getComponent(cc.Sprite),
        //     specialCardOdd
        // );
        winAnim.getChildByName('special_card_odd').getComponent(cc.Sprite).spriteFrame =
            atlas.getSpriteFrame(specialCardOdd);

        if (checkedIsStayLastFrame) {
            this.gotoFrameAndPlay(winAction, 480, 480, false);
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(checkedLastDuration),
                    cc.callFunc(() => {
                        winAnim.active = false;
                        this.showBetWinFlagsAndFlyCoinsAnim();
                    })
                )
            );
        } else {
            this._playSoundEffect(macros.Audio.SPECIAL_CARD_TYPE_BIG);
            this.gotoFrameAndPlay(winAction, 0, 480, false);
            winAction.on('finished', (event: cc.Event): void => {
                winAction.off('finished');
                winAnim.active = false;
                this.showBetWinFlagsAndFlyCoinsAnim();
            });
        }
    }

    isResultSpecialCardType(): boolean {
        let arr = this._cowboyRoom.roundInfo.matchOptions;
        let len = pf.StringUtil.getArrayLength(arr);
        for (let i = 0; i < len; i++) {
            let areaIdx = this.getAreaIdxByBetOption(arr[i]);
            let betOption = this.getBetOptionByAreaIdx(areaIdx);
            if (
                betOption === network.BetZoneOption.HOLE_A ||
                betOption === network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4
            ) {
                return true;
            }
        }
        return false;
    }

    clearSpecialCardTypeAnim(): void {
        let special_card_type_anim = this._nodeAnim.getChildByName('special_card_type_anim');
        if (pf.UIUtil.isValidNode(special_card_type_anim)) {
            special_card_type_anim.removeFromParent(true);
            special_card_type_anim.destroy();
        }
    }

    initTrendChangeAnim(): void {
        // let frameVector: cc.SpriteFrame[] = [];
        // let frameVector: [cc.SpriteFrame] = [null];
        // for (let i = 0; i < 8; i++) {
        //     let frameName = cv.StringTools.formatC("cowboy_trend_%d", i);
        //     let spriteFrame = this.cowboy_trend_anim_PLIST.getSpriteFrame(frameName);
        //     frameVector.push(spriteFrame);
        // }
        let atlas: cc.SpriteAtlas = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_TREND_ANIM_ATLAS);
        this.trend_anim = cc.AnimationClip.createWithSpriteFrames(atlas.getSpriteFrames(), 10);
        this.trend_anim.wrapMode = cc.WrapMode.Loop;
    }

    showTrendChangeAnim(): void {
        this._btnZouShi.node.active = false;
        let trend_anim = this._topBg.node.getChildByName('trend_anim');
        if (pf.UIUtil.isValidNode(trend_anim)) {
            trend_anim.removeFromParent(true);
            trend_anim.destroy();
        }
        let sprTrend = new cc.Node().addComponent(cc.Sprite);
        // cb.loadSpriteTextureByPlist(this.cowboy_trend_anim_PLIST, sprTrend, 'cowboy_trend_0');
        let atlas: cc.SpriteAtlas = pf.addressableAssetManager.getAsset(macros.Assets.COWBOY_TREND_ANIM_ATLAS);
        sprTrend.spriteFrame = atlas.getSpriteFrame('cowboy_trend_0');
        sprTrend.node.name = 'trend_anim';
        sprTrend.node.setPosition(this._btnZouShi.node.x - 1, this._btnZouShi.node.y + 3);
        this._topBg.node.addChild(sprTrend.node);
        let ani = sprTrend.node.addComponent(cc.Animation);
        // sprTrend.node.addChild(ani.node);
        ani.addClip(this.trend_anim, 'trend_anim');
        ani.play('trend_anim');
        // sprTrend.node.runAction(cc.repeatForever(ani.play()));
    }

    hideTrendChangeAnim(): void {
        this._btnZouShi.node.active = true;
        let trend_anim = this._topBg.node.getChildByName('trend_anim');
        if (pf.UIUtil.isValidNode(trend_anim)) {
            trend_anim.removeFromParent(true);
            trend_anim.destroy();
        }
    }

    showRecordDotBezierAnim(): void {
        let lightRecordDot: cc.Sprite = new cc.Node().addComponent(cc.Sprite);
        let sprRecordDot: cc.Sprite = new cc.Node().addComponent(cc.Sprite);
        let bornPoint = cc.v2(0, 0);
        let ctrlPoint1 = cc.v2(0, 0);
        let ctrlPoint2 = cc.v2(0, 0);

        // 0 平 1 牛仔胜 - 1 小牛胜
        if (this._cowboyRoom.roundInfo.roundResult.result === 1) {
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, lightRecordDot, 'record_red_fire_light');
            lightRecordDot.spriteFrame = this._atlasDZNZ.getSpriteFrame('record_red_fire_light');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, sprRecordDot, 'record_red_fire');
            sprRecordDot.spriteFrame = this._atlasDZNZ.getSpriteFrame('record_red_fire');
            let areaIdx = this.getAreaIdxByBetOption(network.BetZoneOption.RED_WIN);
            bornPoint = this._betAreas[areaIdx]
                .getParent()
                .convertToWorldSpaceAR(this._betAreas[areaIdx].getPosition(), bornPoint);
            bornPoint = this._nodeAnim.convertToNodeSpaceAR(bornPoint);
            ctrlPoint1 = cc.v2(300 + bornPoint.x, 80 + bornPoint.y);
            ctrlPoint2 = cc.v2(600 + bornPoint.x, 160 + bornPoint.y);
        } else if (this._cowboyRoom.roundInfo.roundResult.result === -1) {
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, lightRecordDot, 'record_blue_fire_light');
            lightRecordDot.spriteFrame = this._atlasDZNZ.getSpriteFrame('record_blue_fire_light');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, sprRecordDot, 'record_blue_fire');
            sprRecordDot.spriteFrame = this._atlasDZNZ.getSpriteFrame('record_blue_fire');
            let areaIdx = this.getAreaIdxByBetOption(network.BetZoneOption.BLUE_WIN);
            bornPoint = this._betAreas[areaIdx]
                .getParent()
                .convertToWorldSpaceAR(this._betAreas[areaIdx].getPosition());
            bornPoint = this._nodeAnim.convertToNodeSpaceAR(bornPoint);
            ctrlPoint1 = cc.v2(-80 + bornPoint.x, 80 + bornPoint.y);
            ctrlPoint2 = cc.v2(-120 + bornPoint.x, 160 + bornPoint.y);
        } else if (this._cowboyRoom.roundInfo.roundResult.result === 0) {
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, lightRecordDot, 'record_draw_fire_light');
            lightRecordDot.spriteFrame = this._atlasDZNZ.getSpriteFrame('record_draw_fire_light');
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, sprRecordDot, 'record_draw_fire');
            sprRecordDot.spriteFrame = this._atlasDZNZ.getSpriteFrame('record_draw_fire');
            let areaIdx = this.getAreaIdxByBetOption(network.BetZoneOption.EQUAL);
            bornPoint = this._betAreas[areaIdx]
                .getParent()
                .convertToWorldSpaceAR(this._betAreas[areaIdx].getPosition());
            bornPoint = this._nodeAnim.convertToNodeSpaceAR(bornPoint);
            ctrlPoint1 = cc.v2(80 + bornPoint.x, 80 + bornPoint.y);
            ctrlPoint2 = cc.v2(160 + bornPoint.x, 160 + bornPoint.y);
        }
        if (!sprRecordDot) {
            return;
        }

        // 光效
        if (!this._lastRecordDotWorldPos) {
            const panelRecord = this._topBg.node.getChildByName('panelRecord');
            this._lastRecordDotWorldPos = panelRecord.convertToWorldSpaceAR(
                this._recordDots[this._recordNum - 1].node.getPosition()
            );
        }
        let endPos = this._nodeAnim.convertToNodeSpaceAR(this._lastRecordDotWorldPos);
        this._nodeAnim.addChild(lightRecordDot.node);
        lightRecordDot.node.active = false;
        lightRecordDot.node.setPosition(endPos);

        this._nodeAnim.addChild(sprRecordDot.node);
        sprRecordDot.node.setPosition(bornPoint);
        let bezierCfg = [ctrlPoint1, ctrlPoint2, endPos];

        sprRecordDot.node.scale = 0;
        sprRecordDot.node.opacity = 0;
        sprRecordDot.node.runAction(cc.scaleTo(0.3, 1.0));
        sprRecordDot.node.runAction(cc.fadeIn(0.3));

        this.showTrendChangeAnim();
        let len = pf.StringUtil.getArrayLength(this._recordDots);
        sprRecordDot.node.runAction(
            cc.sequence(
                cc.delayTime(0.32),
                cc.bezierTo(0.5, bezierCfg),
                cc.callFunc(() => {
                    // 0.5.5
                    if (pf.UIUtil.isValidNode(sprRecordDot)) {
                        sprRecordDot.node.removeFromParent(true);
                        sprRecordDot.node.destroy();
                    }
                    if (len > 0) {
                        this._recordDots[len - 1].node.active = true;
                    }

                    lightRecordDot.node.active = true;
                    lightRecordDot.node.runAction(
                        cc.sequence(cc.fadeOut(0.18), cc.fadeIn(0.18), cc.fadeOut(0.18), cc.destroySelf())
                    );
                })
            )
        );

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.5),
                cc.callFunc(() => {
                    if (len > 0) {
                        this._recordDots[len - 1].node.active = false;
                    }
                    this.showHistoryMoveAnim();
                })
            )
        );

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(2.0),
                cc.callFunc(() => {
                    this.hideTrendChangeAnim();
                })
            )
        );
    }

    // 显示玩家胜利头像框光环动画
    showWinPlayerLightAnim(uid: number): void {
        let playerHeads: cc.Node[] = this.getPlayerHeadNodesByUid(uid);
        if (playerHeads.length === 0) {
            this.updatePlayerWinCount(uid, true);
            return;
        }

        for (const head of playerHeads) {
            // 自己不显示光环
            if (head === this._selfHeadBg) {
                continue;
            }

            let winPlayerLightAnim: cc.Node = head.getChildByName('win_player_light');
            let headIMG = head.getChildByName(this._HEAD_IMG_TAG);
            let pos = headIMG ? headIMG.position : cc.v2(0, 15);
            if (!winPlayerLightAnim) {
                winPlayerLightAnim = this.initAni(
                    head,
                    pf.addressableAssetManager.getAsset(macros.Assets.WIN_PLAYER_LIGHT)
                );
                winPlayerLightAnim.name = 'win_player_light';
                winPlayerLightAnim.setPosition(pos);
                winPlayerLightAnim.zIndex = 10;
            }

            let winPlayerLightAction: cc.Animation = winPlayerLightAnim.getComponent(cc.Animation);

            // winPlayerLightAnim.runAction(winPlayerLightAction);
            // winPlayerLightAction.play();
            winPlayerLightAnim.active = true;
            this.gotoFrameAndPlay(winPlayerLightAction, 0, 20, true);
        }

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.5),
                cc.callFunc(() => {
                    this.updatePlayerWinCount(uid, true);
                })
            )
        );
    }

    hideAllWinPlayerLightAnim(): void {
        let len = this._otherPlayerHeads.length;
        for (let i = 0; i < len; i++) {
            let node = this._otherPlayerHeads[i].bg.node.getChildByName('win_player_light');
            if (pf.UIUtil.isValidNode(node)) {
                node.removeFromParent(true);
                node.destroy();
            }
        }
    }

    // 显示玩家连胜动画
    updatePlayerWinCount(uid: number, bAnim?: boolean): void {
        const checkedIsPlayAnim = bAnim === true ? true : false;
        let playerHeads: cc.Node[] = this.getPlayerHeadNodesByUid(uid);
        if (playerHeads.length === 0) {
            return;
        }

        for (const head of playerHeads) {
            // 富豪No1 和 神算子 不显示连胜
            if (this._otherPlayerHeads[0].bg.node === head || this._otherPlayerHeads[1].bg.node === head) {
                continue;
            }

            let win_player_win_count = this.node.getChildByName('win_player_win_count_' + head.uuid);
            if (pf.UIUtil.isValidNode(win_player_win_count)) {
                win_player_win_count.removeFromParent(true);
                win_player_win_count.destroy();
            }
            let keepWinCount = this._cowboyRoom.getPlayerKeepWinCountByUid(uid);
            if (keepWinCount >= 3) {
                keepWinCount = keepWinCount > 10 ? 11 : keepWinCount;
                let sprWinCount = new cc.Node().addComponent(cc.Sprite);
                // cb.loadSpriteTextureByPlist(
                //     this._atlasDZNZ,
                //     sprWinCount,
                //     pf.StringUtil.formatC('win_count_%d', keepWinCount)
                // );
                sprWinCount.spriteFrame = this.language_PLIST.getSpriteFrame(
                    pf.StringUtil.formatC('win_count_%d', keepWinCount)
                );
                sprWinCount.node.name = 'win_player_win_count_' + head.uuid;
                let offsetY = head === this._selfHeadBg ? 40 : 70;
                let tmpPos = head.convertToWorldSpaceAR(cc.v2(0, offsetY));
                tmpPos = this.node.convertToNodeSpaceAR(tmpPos);
                sprWinCount.node.setPosition(tmpPos);
                this.node.addChild(sprWinCount.node, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_IMG_WIN_COUNT);

                if (checkedIsPlayAnim) {
                    // animation
                    let targetPos = sprWinCount.node.getPosition();
                    let bornPos = targetPos;
                    let headMidWorldPos = head.getParent().convertToWorldSpaceAR(head.getPosition());
                    headMidWorldPos = head.convertToNodeSpaceAR(headMidWorldPos);
                    if (headMidWorldPos.x < pf.system.view.width / 2) {
                        bornPos = cc.v2(-200 + bornPos.x, 0 + bornPos.y);
                    } else {
                        bornPos = cc.v2(200 + bornPos.x, 0 + bornPos.y);
                    }
                    sprWinCount.node.setPosition(bornPos);
                    sprWinCount.node.runAction(
                        cc.moveTo(0.18, targetPos).easing(cc.easeBackOut()).easing(cc.easeSineOut())
                    );
                }
            }
        }
    }

    // 更新所有玩家连胜状态
    updateAllPlayerWinCount(): void {
        this.updatePlayerWinCount(this._authService.currentUser.userId);

        // 这里按照服务器发的gamePlayers顺序放
        for (let i = 0; i < this._otherPlayerHeads.length; i++) {
            let otherPlayersInfo = this._cowboyRoom.otherPlayers;
            if (i < otherPlayersInfo.length) {
                let info = otherPlayersInfo[i];
                this.updatePlayerWinCount(info.uid);
            }
        }
    }

    // 隐藏所有timeline动画
    hideAllTimelineAnims(): void {
        this.gotoFrameZeroAndPause(this._roundStartAction);
        this._roundStartAnim.active = false;

        this.gotoFrameZeroAndPause(this._fightBeginAction);
        this._fightBeginAnim.active = false;

        this.gotoFrameZeroAndPause(this._fightEndAction);
        this._fightEndAnim.active = false;

        this.gotoFrameZeroAndPause(this._waitForNextRoundAction);
        this._waitForNextRoundAnim.active = false;

        let len = this._winFlagAnims.length;
        for (let i = 0; i < len; i++) {
            this.gotoFrameZeroAndPause(this._winFlagActions[i]);
            this._winFlagAnims[i].active = false;
        }
    }

    gotoFrameZeroAndPause(ani: cc.Animation) {
        // 未处理
        ani.play();
        ani.stop();
    }

    // 更新玩家金币，在金币飞到自己动画结束之前(减去当前局赢的)
    updatePlayerCoinBeforeSettle(): void {
        // 自己
        // let it = cb.getCowboyRoom().playerSettles.get(cv.dataHandler.getUserData().u32Uid);
        let it = this._cowboyRoom.roundInfo.playerSettles.get(this._authService.currentUser.userId);
        if (it) {
            this._textCoin.string = cr.CommonUtil.getShortOwnCoinString(it.curCoin - it.totalWinAmount);
        }

        // 其他玩家
        for (const head of this._otherPlayerHeads) {
            if (head.bg.node.active && head.uid > 0) {
                // let it = cb.getCowboyRoom().playerSettles.get(player.uid);
                let it = this._cowboyRoom.roundInfo.playerSettles.get(head.uid);
                if (it) {
                    head.textCoin.string = cr.CommonUtil.getShortOwnCoinString(it.curCoin - it.totalWinAmount);
                }
            }
        }
    }

    // 更新牌
    updateAllCardsBeforeSettle(): void {
        this.setAllCardsVisible(true);

        // 更新所有背面牌
        this.updateAllCardsBack();

        /* let publicCardNum = cb.getCowboyRoom().publicCards.length;
        for (let i = 0; i < 1; i++)
        {
            if (i < publicCardNum)
            {
                this._publicCards[i].SetContent((cb.getCowboyRoom().publicCards[i].number), (cb.getCowboyRoom().publicCards[i].suit));
                this._publicCards[i].SetFace(true);
            }
        } */
    }

    // 更新所有牌，除了公共牌
    updateAllCardsExceptPublicBeforeSettle(): void {
        // update cards
        this.setAllCardsVisible(true);

        // 更新所有背面牌
        this.updateAllCardsBack();

        // 更新所有正面牌
        if (
            this._cowboyRoom.roundInfo.redHandCards.length === 2 &&
            this._cowboyRoom.roundInfo.blueHandCards.length === 2
        ) {
            for (let i = 0; i < 2; i++) {
                this._redHandCards[i].SetContent(
                    this._cowboyRoom.roundInfo.redHandCards[i].number,
                    this._cowboyRoom.roundInfo.redHandCards[i].suit
                );
                this._blueHandCards[i].SetContent(
                    this._cowboyRoom.roundInfo.blueHandCards[i].number,
                    this._cowboyRoom.roundInfo.blueHandCards[i].suit
                );
                this._redHandCards[i].SetFace(true);
                this._blueHandCards[i].SetFace(true);
            }
        }

        /* let publicCardNum = cb.getCowboyRoom().publicCards.length;
        for (let i = 0; i < 1; i++)
        {
            if (i < publicCardNum)
            {
                this._publicCards[i].SetContent((cb.getCowboyRoom().publicCards[i].number), (cb.getCowboyRoom().publicCards[i].suit));
                this._publicCards[i].SetFace(true);
            }
        } */
    }

    // 清除没有中的区域金币
    clearLoseBetCoins(): void {
        let len = this._betCoinContents.length;
        let arr = this._cowboyRoom.roundInfo.matchOptions;
        let matchLen = this._cowboyRoom.roundInfo.matchOptions.length;
        for (let i = 0; i < len; i++) {
            let betOption = this.getBetOptionByAreaIdx(i);
            let isOptionWin = false;
            for (let i = 0; i < matchLen; i++) {
                if (betOption === arr[i]) {
                    isOptionWin = true;
                    break;
                }
            }

            if (!isOptionWin) {
                this._betCoinContents[i].destroyAllChildren();
                this._betCoinContents[i].removeAllChildren(true);
                // console.log('clearLoseBetCoins-.清理areaIndex = ' + i);
                this.hideAreaCoin(i, false);
            }
        }
    }

    // 开局动画
    showRoundStartAnim(): void {
        this._roundStartAnim.active = true;
        this.gotoFrameAndPlay(this._roundStartAction, 0, 90, false);
        this._roundStartAction.on(
            'finished',
            (event: cc.Event): void => {
                this._roundStartAction.off('finished');
                this._roundStartAnim.active = false;
                this.sendCardsAnim();
            },
            this
        );
    }

    // 发牌动画
    sendCardsAnim(): void {
        this.setAllCardsVisible(false);
        this.updateAllCardsBack();

        let handCards: PokerCardControl[] = [];
        let handCardsTargetPos: cc.Vec2[] = [];
        let redOffset = cc.v2(30, -30);
        let blueOffset = cc.v2(-30, -30);
        let publicOffset = cc.v2(-30, -30);
        for (let i = 0; i < 2; i++) {
            this._redHandCards[i].node.setPosition(
                this._oriRedHandCardsPos[i].x + redOffset.x,
                this._oriRedHandCardsPos[i].y + redOffset.y
            );
            this._blueHandCards[i].node.setPosition(
                this._oriBlueHandCardsPos[i].x + blueOffset.x,
                this._oriBlueHandCardsPos[i].y + blueOffset.y
            );
        }
        for (let i = 0; i < 5; i++) {
            this._publicCards[i].node.setPosition(
                this._oriPublicCardsPos[i].x + publicOffset.x,
                this._oriPublicCardsPos[i].y + publicOffset.y
            );
            this._publicCards[i].SetFace(false);
        }
        handCards.push(this._redHandCards[0]);
        handCards.push(this._blueHandCards[0]);
        handCards.push(this._redHandCards[1]);
        handCards.push(this._blueHandCards[1]);
        handCardsTargetPos.push(this._oriRedHandCardsPos[0]);
        handCardsTargetPos.push(this._oriBlueHandCardsPos[0]);
        handCardsTargetPos.push(this._oriRedHandCardsPos[1]);
        handCardsTargetPos.push(this._oriBlueHandCardsPos[1]);
        // 发手牌动画
        let duration = 0.14;
        let easeRate = 1.0;
        let len = handCards.length;
        for (let i = 0; i < len; i++) {
            let moveAction = cc.moveTo(duration, handCardsTargetPos[i]).easing(cc.easeInOut(easeRate));
            let showAction = cc.callFunc(() => {
                handCards[i].node.active = true;
                handCards[i].SetFace(false);
            });
            let moveComplete = cc.callFunc(() => {
                if (i === len - 1) {
                    // 发公共牌动画
                    for (let j = 0; j < 5; j++) {
                        let publicMoveAction = cc
                            .moveTo(duration, this._oriPublicCardsPos[j])
                            .easing(cc.easeInOut(easeRate));
                        let publicShowAction = cc.callFunc(() => {
                            this._publicCards[j].node.active = true;
                            this._publicCards[j].SetFace(false);
                            this._playSoundEffect(macros.Audio.FAPAI);
                        });
                        let publicMoveComplete = cc.callFunc(() => {
                            if (j === 4) {
                                // 开战动画
                                this._nodeAnim.runAction(
                                    cc.sequence(
                                        cc.delayTime(0.2),
                                        cc.callFunc(() => {
                                            this.showFightBeginAnim();
                                        })
                                    )
                                );
                            }
                        });
                        this.scheduleOnce(() => {
                            this._publicCards[j].node.active = true;
                            this._publicCards[j].node.runAction(
                                cc.sequence(publicShowAction, publicMoveAction, publicMoveComplete)
                            );
                        }, j * duration);
                    }
                }
            });
            this.scheduleOnce(() => {
                handCards[i].node.active = true;
                handCards[i].node.runAction(cc.sequence(showAction, moveAction, moveComplete));
            }, i * duration);

            this.scheduleOnce(
                () => {
                    this._playSoundEffect(macros.Audio.FAPAI);
                },
                0.04 + i * duration
            );
        }
    }

    // 翻手牌动画
    showHandCardsAnim(): void {
        // 显示所有牌
        this.setAllCardsVisible(true);

        // 翻手牌
        if (
            this._cowboyRoom.roundInfo.redHandCards.length === 2 &&
            this._cowboyRoom.roundInfo.blueHandCards.length === 2
        ) {
            for (let i = 0; i < 2; i++) {
                this._redHandCards[i].SetContent(
                    this._cowboyRoom.roundInfo.redHandCards[i].number,
                    this._cowboyRoom.roundInfo.redHandCards[i].suit
                );
                // this._redHandCards[i].node.runAction(cc.sequence(cc.rotateTo(0.3, 0, 180), cc.callFunc(()=>{
                //     this._redHandCards[i].node.setRotation(0);
                this._redHandCards[i].SetFace(false); // })));

                this._redHandCards[i].Turn(true);

                this._blueHandCards[i].SetContent(
                    this._cowboyRoom.roundInfo.blueHandCards[i].number,
                    this._cowboyRoom.roundInfo.blueHandCards[i].suit
                );
                // this._blueHandCards[i].node.runAction(cc.sequence(cc.delayTime(0.5), cc.callFunc(() => {//, cc.rotateTo(0.3, 0, 180)
                // this._blueHandCards[i].node.setRotation(0);
                this._blueHandCards[i].SetFace(false);
                this._blueHandCards[i].Turn(true, 0.5);
                // })));
                // this._blueHandCards[i].Turn(true, 0.5);

                if (i === 0) {
                    this._playSoundEffect(macros.Audio.KAIPAI);
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(0.5),
                            cc.callFunc(() => {
                                this._playSoundEffect(macros.Audio.KAIPAI);
                            })
                        )
                    );
                }
            }
        }

        // 翻公共牌
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._showHandCardsDuration),
                cc.callFunc(() => {
                    this.showPublicCardsAnim();
                })
            )
        );
    }

    // 翻公共牌动画
    showPublicCardsAnim(): void {
        let publicCardNum = this._cowboyRoom.roundInfo.publicCards.length;
        for (let i = 0; i < 5; i++) {
            if (i < publicCardNum) {
                let flopOverFunc = cc.callFunc(() => {
                    if (i === 4) {
                        this._nodeAnim.runAction(
                            cc.sequence(
                                cc.delayTime(0.3),
                                cc.callFunc(() => {
                                    this.hideGameTips();
                                    this.showCardTypeAnim();
                                    this.updateWinCards();
                                    this.showCowboyLoseAnim();
                                    // showHistoryMoveAnim();
                                })
                            )
                        );
                        this._playSoundEffect(macros.Audio.KAIPAI);
                    }
                });
                this._publicCards[i].SetFace(false);
                this._publicCards[i].node.runAction(
                    cc.sequence(
                        cc.moveTo(0.3, this._oriPublicCardsPos[0]),
                        cc.callFunc(() => {
                            this._publicCards[i].SetContent(
                                this._cowboyRoom.roundInfo.publicCards[i].number,
                                this._cowboyRoom.roundInfo.publicCards[i].suit
                            );
                            this._publicCards[i].Turn(true);
                        }),
                        cc.moveTo(0.2, this._oriPublicCardsPos[i]),
                        flopOverFunc
                    )
                );
            }
        }

        // 隐藏没有中的区域金币动画
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._showPublicCardsDuration),
                cc.callFunc(() => {
                    this.showHideLoseBetCoinsAnim();
                })
            )
        );
    }

    // 翻牌型动画
    showCardTypeAnim(): void {
        this.updateCardType();

        this._redCardType.node.scale = 0;
        this._blueCardType.node.scale = 0;
        this._redCardTypeBg.node.scale = 0;
        this._blueCardTypeBg.node.scale = 0;
        let scaleAction = cc.scaleTo(0.18, 1.0).easing(cc.easeInOut(1.0));
        this._redCardType.node.runAction(scaleAction);
        this._blueCardType.node.runAction(scaleAction.clone());
        this._redCardTypeBg.node.runAction(scaleAction.clone());
        this._blueCardTypeBg.node.runAction(scaleAction.clone());

        this._cowboyRoom.showTheNewestTrend = true;
    }

    // 出战动画
    showFightBeginAnim(): void {
        this._playSoundEffect(macros.Audio.BEGIN_BET);
        this._fightBeginAnim.active = true;
        this.gotoFrameAndPlay(this._fightBeginAction, 0, 60, false);
        this._fightBeginAction.on(
            'finished',
            (event: cc.Event): void => {
                this._fightBeginAction.off('finished');
                this._fightBeginAnim.active = false;
            },
            this
        );
    }

    // 开战动画
    showFightEndAnim(): void {
        // 开战动画.翻牌动画.显示win标记，金币收回动画.等待下一局动画
        this._fightEndAnim.active = true;
        this.gotoFrameAndPlay(this._fightEndAction, 0, 60, false);

        this.playPointAni();
        this._fightEndAction.on('finished', (event: cc.Event): void => {
            this._fightEndAction.off('finished');
            this._fightEndAnim.active = false;
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.3),
                    cc.callFunc(() => {
                        // 翻牌动画
                        this.showHandCardsAnim();
                    })
                )
            );
        });
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.4),
                cc.callFunc(() => {
                    // this.playCowboyEffect(s_chuzhan_kaizhan);
                    this._playSoundEffect(macros.Audio.END_BET);
                })
            )
        );
    }

    // 下注倒计时开始动画
    showBetCoutDownBeginAnim(): void {
        // console.log('[3in1] CowboyControl::showBetCoutDownBeginAnim');
        this.updateBetCoutDown();

        // 动画
        this._betCountDown.stopAllActions();
        this._betCountDown.setPosition(this._betCountDown.x, pf.system.view.height);
        this._betCountDown.runAction(cc.moveTo(0.5, this._oriBetCountDownBgPos).easing(cc.easeBackOut()));
    }

    // 下注倒计时结束动画
    showBetCoutDownEndAnim(): void {
        this._updateBetButtonState();
        this.showOpenCardTips();

        // 动画
        this._betCountDown.stopAllActions();
        this._betCountDown.setPosition(this._oriBetCountDownBgPos);
        let move = cc.moveTo(0.5, cc.v2(this._oriBetCountDownBgPos.x, pf.system.view.height)).easing(cc.easeBackIn());
        let pkCall = cc.callFunc(() => {
            this.hideBetCountDown();
        });
        this._betCountDown.runAction(cc.sequence(move, pkCall));

        // 开战动画
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._betCountDownEndDuration),
                cc.callFunc(() => {
                    this.showFightEndAnim();
                })
            )
        );
    }

    // 显示隐藏没有中的区域金币动画
    showHideLoseBetCoinsAnim(): void {
        let arr = this._cowboyRoom.roundInfo.matchOptions;
        let len = pf.StringUtil.getArrayLength(arr);
        let betLen = pf.StringUtil.getArrayLength(this._betCoinContents);
        for (let i = 0; i < betLen; i++) {
            let betOption = this.getBetOptionByAreaIdx(i);
            let isOptionWin = false;
            for (let i = 0; i < len; i++) {
                if (betOption === arr[i]) {
                    isOptionWin = true;
                    break;
                }
            }

            if (!isOptionWin) {
                // console.log('清理areaIndex = ' + i);
                this.hideAreaCoin(i);
            }
        }

        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(this._hideLoseBetCoinsDuration),
                cc.callFunc(() => {
                    // 特殊牌型动画
                    if (this.isResultSpecialCardType()) {
                        this.showSpecialCardTypeAnim();
                    } else {
                        // 显示win标记/金币回收动画
                        this.showBetWinFlagsAndFlyCoinsAnim();
                    }
                })
            )
        );
    }

    hideAreaCoin(areaIdx: number, isRunAction?: boolean) {
        const checkedIsRunAction = isRunAction === false ? false : true;
        let areaCoin = this._coinNodeByArea[areaIdx];
        let coinLen = pf.StringUtil.getArrayLength(areaCoin);
        for (let i = coinLen - 1; i >= 0; i--) {
            let node = areaCoin[i];
            // node.runAction(cc.sequence(cc.fadeOut(0.5), cc.hide()));
            if (checkedIsRunAction) {
                node.runAction(cc.fadeOut(0.5));
            } else {
                node.opacity = 0;
            }
            // node.active = false;
        }
    }

    handleCoin() {
        // let coinLen = this._coinNode.childrenCount;
        // this._circleCoinArr = [];
        // this._squareCoinArr = [];
        let len = this._coinNode.childrenCount;
        let arr = this._coinNode.children;
        for (let i = len - 1; i >= 0; i--) {
            arr[i].active = true;
            arr[i].stopAllActions();
            this.nodePutToPool(arr[i]);
        }
        let areaLen = this._coinNodeByArea.length;
        for (let i = 0; i < areaLen; i++) {
            this._coinNodeByArea[i] = [];
        }
    }

    // 显示win标记，金币收回动画
    showBetWinFlagsAndFlyCoinsAnim(): void {
        // 显示win标记/隐藏区域数字和金币
        let matchArr = this._cowboyRoom.roundInfo.matchOptions;
        let matchLen = pf.StringUtil.getArrayLength(matchArr);

        for (let i = 0; i < matchLen; i++) {
            let areaIdx = this.getAreaIdxByBetOption(matchArr[i]);
            this.clearBetArea(areaIdx);
            this.showWinFlagAnim(areaIdx);
        }

        // 路单曲线动画
        this._nodeAnim.runAction(
            cc.sequence(
                cc.delayTime(0.5),
                cc.callFunc((): void => {
                    this._showAllWayOutAnim();
                    this.showRecordDotBezierAnim();
                })
            )
        );

        // 动画:在哪些选项赢了(增加除主界面8个人输赢外其它玩家列表的输赢)
        let tmpSettles = this._cowboyRoom.roundInfo.playerSettles;
        tmpSettles.set(0xffffffff, this._cowboyRoom.roundInfo.otherPlayersSettle);
        tmpSettles.forEach((value: PlayerSettle, key: number): void => {
            let uid = key;
            let playerHeads: cc.Node[] = this.getPlayerCoinNodesByUid(uid);

            // 桌面没有该玩家
            if (playerHeads.length === 0) {
                console.log('playerSettles uid: %d not in gameplayers, use player list button', uid);
                playerHeads.push(this._btnPlayerList.node);
            }

            for (let i = 0; i < playerHeads.length; i++) {
                // 自己是富豪/神算子， 只回收一次金币到自己头像
                if (uid === this._authService.currentUser.userId && i > 0) {
                    continue;
                }

                let headImg = playerHeads[i];
                let worldPos = headImg.parent.convertToWorldSpaceAR(headImg.getPosition());
                // console.log('世界坐标系---------.' + worldPos);
                let headMidWorldPos = headImg.parent.convertToWorldSpaceAR(headImg.getPosition());
                headMidWorldPos = this._nodeAnim.convertToNodeSpaceAR(headMidWorldPos);
                let zoneSettleDetails = value.settle;
                for (const detail of zoneSettleDetails) {
                    let zoneSettleDetail = detail;
                    if (zoneSettleDetail.winAmount > 0) {
                        let option = zoneSettleDetail.option;
                        let areaIdx = this.getAreaIdxByBetOption(option);

                        // 自己赢显示win标记,隐藏区域数字
                        let coinContent = this._betCoinContents[areaIdx];
                        let sz = coinContent.getContentSize();
                        let betDetails = this.getBetDetailAmounts(zoneSettleDetail.winAmount);
                        let len = betDetails.length;
                        for (let k = 0; k < len; k++) {
                            let flyCoin: cc.Sprite = this.createFlyCoin(areaIdx, betDetails[k], true);
                            let coinFlyBorn = coinContent.convertToWorldSpaceAR(
                                cc.v2(
                                    this.SERangeRandomf(sz.width * 0.3, sz.width * 0.7) - sz.width * 0.5,
                                    this.SERangeRandomf(sz.height * 0.2, sz.height * 0.7) - sz.height * 0.5
                                )
                            );
                            coinFlyBorn = this._nodeAnim.convertToNodeSpaceAR(coinFlyBorn);
                            this._nodeAnim.addChild(flyCoin.node);
                            flyCoin.node.setPosition(coinFlyBorn);
                            // flyCoin.node.active = false;

                            // 延迟一会(win动画结束)开始飞金币
                            this.scheduleOnce((): void => {
                                flyCoin.node.active = true;
                                let delay: cc.ActionInterval = cc.delayTime(0.2 + k * 0.025);
                                let moveTo: cc.ActionInterval = cc.moveTo(0.6, headMidWorldPos).easing(cc.easeOut(0.8));
                                flyCoin.node.runAction(
                                    cc.sequence(
                                        delay,
                                        moveTo,
                                        cc.callFunc((): void => {
                                            this.nodePutToPool(flyCoin.node);
                                        })
                                    )
                                );
                            }, 0.7);
                        }
                    }
                }

                // 总共赢的
                let totalWinAmount = value.totalWinAmount;
                if (totalWinAmount >= 100) {
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(0.3),
                            cc.callFunc(() => {
                                let winToastBg = new cc.Node().addComponent(cc.Sprite);
                                // cb.loadSpriteTextureByPlist(this._atlasDZNZ, winToastBg, 'win_coin_bg');
                                winToastBg.spriteFrame = this._atlasDZNZ.getSpriteFrame('win_coin_bg');
                                winToastBg.node.setPosition(headMidWorldPos);
                                console.log(
                                    '世界坐标系---------.' + worldPos + ', ' + (pf.system.view.width / 2).toString()
                                );
                                console.log(headImg !== this._selfCoin.node);
                                if (headImg === this._btnPlayerList.node) {
                                    winToastBg.node.setAnchorPoint(cc.v2(1, 0.5));
                                } else {
                                    if (worldPos.x < pf.system.view.width / 2) {
                                        if (headImg !== this._selfCoin.node) {
                                            winToastBg.node.setAnchorPoint(cc.v2(0, 0.5));
                                        }
                                    } else {
                                        winToastBg.node.setAnchorPoint(cc.v2(1, 0.5));
                                        // winToastBg.node.setPosition(headMidWorldPos.x-winToastBg.node.width, 0);
                                    }
                                }

                                this._nodeAnim.addChild(winToastBg.node);

                                // std.string formatCoin = this.getShortOwnCoinString(totalWinAmount);
                                // TextBMFont* textWinToast = cocos2d.ui.TextBMFont.create("+" + formatCoin, "cowboy/fnt/win_num.fnt");
                                let textWinToast = new cc.Node().addComponent(cc.Label);
                                textWinToast.font = this.win_num_FNT;
                                textWinToast.fontSize = 10;
                                textWinToast.string =
                                    '+' +
                                    pf.StringUtil.numberToString(pf.StringUtil.clientGoldByServer(totalWinAmount));
                                winToastBg.node.addChild(textWinToast.node);
                                textWinToast.node.scale = 1.4;
                                if (headImg === this._btnPlayerList.node) {
                                    textWinToast.node.setPosition(-winToastBg.node.width / 2, 0);
                                } else {
                                    if (worldPos.x > pf.system.view.width / 2) {
                                        textWinToast.node.setPosition(-winToastBg.node.width / 2, 0);
                                    } else if (headImg !== this._selfCoin.node) {
                                        textWinToast.node.setPosition(winToastBg.node.width / 2, 0);
                                    }
                                }

                                winToastBg.node.runAction(
                                    cc.sequence(cc.delayTime(1.3), cc.moveBy(1.5, cc.v2(0, 50)), cc.destroySelf())
                                );
                                winToastBg.node.runAction(
                                    cc.sequence(cc.delayTime(1.4), cc.fadeOut(1.5).easing(cc.easeInOut(1.0)))
                                );
                                textWinToast.node.runAction(
                                    cc.sequence(cc.delayTime(1.4), cc.fadeOut(1.5).easing(cc.easeInOut(1.0)))
                                );
                                this._nodeAnim.runAction(
                                    cc.sequence(
                                        cc.delayTime(0.3),
                                        cc.callFunc(() => {
                                            this.updateRoundEndPlayerCoin();
                                        })
                                    )
                                );

                                this._playSoundEffect(macros.Audio.GET_WIN_COIN);
                            })
                        )
                    );

                    // 赢的玩家头像光环
                    this._nodeAnim.runAction(
                        cc.sequence(
                            cc.delayTime(0.7),
                            cc.callFunc(() => {
                                this.showWinPlayerLightAnim(uid);
                            })
                        )
                    );
                } else {
                    // 更新玩家连胜状态
                    this.updatePlayerWinCount(uid, true);
                }
            }
        });

        // 维护状态:非0代表系统即将维护
        if (this._cowboyRoom.roundInfo.stopWorld !== 0) {
            let bTrue = this._cowboyRoom.roundInfo.idleRoomId > 0;
            if (!bTrue) {
                this.showCowboyToast(pf.languageManager.getString('Cowboy_server_will_stop_text'));
            }
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(this._betWinFlagsAndFlyCoinsDuration),
                    cc.callFunc(() => {
                        if (bTrue) {
                            this.showSwitchTable();
                        } else {
                            this._backToCowboyListScene();
                        }
                    })
                )
            );
        } else {
            // 下一局即将开始
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(this._betWinFlagsAndFlyCoinsDuration),
                    cc.callFunc(() => {
                        // cv.MessageCenter.send('on_cowboy_willstart_notify');
                        this.showNextRoundTips();
                    })
                )
            );
        }
    }

    // 单局结束时更新玩家金币
    updateRoundEndPlayerCoin(): void {
        // 自己
        let it = this._cowboyRoom.roundInfo.playerSettles.get(this._authService.currentUser.userId);
        if (it) {
            this._textCoin.string = cr.CommonUtil.getShortOwnCoinString(it.curCoin);
        }

        // 其他玩家
        for (const head of this._otherPlayerHeads) {
            if (head.bg.node.active && head.uid > 0) {
                let it = this._cowboyRoom.roundInfo.playerSettles.get(head.uid);
                if (it) {
                    head.textCoin.string = cr.CommonUtil.getShortOwnCoinString(it.curCoin);
                }
            }
        }
    }

    getBetChipIdx(coin: number): number {
        let ret = -1;
        let amountLevels = this._cowboyRoom.roomParams.amountLevel;
        for (let i = 0; i < amountLevels.length; i++) {
            if (coin === amountLevels[i]) {
                return i;
            }
        }
        return ret;
    }

    getBetDetailAmounts(gold: number): number[] {
        let vAmountLevels: number[] = this._cowboyRoom.betSettings.betCoinOptions;
        return MiniGameCommonDef.disinteBetAmounts(gold, vAmountLevels);
    }

    getAllBetDetailAmounts(option: number): number[] {
        // let it = cb.getCowboyRoom().allZoneBet.get(option);
        // if (it) {
        //     return it;
        // } else {
        //     let vec: number[] = [];
        //     return vec;
        // }
        const betZone = this._cowboyRoom.betZones.get(option);
        if (betZone) {
            return betZone.optionInfo.amounts;
        } else {
            return [];
        }
    }

    nodePutToPool(node: cc.Node): void {
        if (!node) return;

        // console.log('#########################nodePutToPool#############################' + this._circlePool.size());
        // let name = node.name;
        // if (name == 'btnBet_0') {
        //     this._circlePool.put(node);
        // } else if (name == 'btnBet_3') {
        //     this._squarePool.put(node);
        // }
        this._coinPool.put(node);
    }

    getRandPos(sz: cc.Size, flyCoin: cc.Node): cc.Vec2 {
        let width_offset = flyCoin.width * 0.3;
        let height_offset = flyCoin.height * 0.3;
        return cc.v2(
            this.SERangeRandomf(0 + width_offset, sz.width - width_offset) - sz.width * 0.5,
            this.SERangeRandomf(0 + height_offset, sz.height - height_offset) - sz.height * 0.5
        );
    }

    createFlyCoin(areaIdx: number, amount: number, isWin?: boolean): cc.Sprite {
        const checkedIsWin = isWin === true ? true : false;
        // let isCircleCoin = this.isCircleCoin(coin);
        let node: cc.Node = null;

        let len = pf.StringUtil.getArrayLength(this._coinNodeByArea[areaIdx]);
        if (len >= this._areaCoinMax[areaIdx]) {
            let removeNode = this._coinNodeByArea[areaIdx][0];
            this.nodePutToPool(removeNode);
            this._coinNodeByArea[areaIdx].splice(0, 1);
        }

        // if (isCircleCoin) {
        //     if (this._circlePool.size() > 0) {
        //         node = this._circlePool.get();
        //     } else {
        //         node = cc.instantiate(this.btnBet_0_prefab);
        //     }
        // } else {
        //     if (this._squarePool.size() > 0) {
        //         node = this._squarePool.get();
        //     } else {
        //         node = cc.instantiate(this.btnBet_3_prefab);
        //     }
        // }
        if (this._coinPool.size() > 0) {
            node = this._coinPool.get();
        } else {
            node = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.BET_COIN));
            node.setScale(this._fFlyCoinScaleRate);
        }
        let coin: HumanboyBetCoinControl = node.getComponent(HumanboyBetCoinControl);
        coin.setShape(this._getBetCoinShapeByAmount(amount));
        coin.setTxtNum(pf.StringUtil.serverGoldToShowNumber(amount));
        coin.btn.enabled = false;
        if (!checkedIsWin) {
            this._coinNode.addChild(node);

            this._coinNodeByArea[areaIdx].push(node);
        }
        node.active = true;
        node.opacity = 255;

        // this.setCoinText(node.getChildByName('textBet'), pf.StringUtil.clientGoldByServer(amount), true);
        return node.getComponent(cc.Sprite);
    }

    setCoinText(node: cc.Node, num: number, isYellow: boolean): void {
        let str = pf.StringUtil.numberToShowString(num);
        let len = pf.StringUtil.getArrayLength(str);
        node.setContentSize(30 * len, 48);
        // cv.resMgr.adaptWidget(node);
        pf.UIUtil.adaptWidget(node);
        node.destroyAllChildren();
        node.removeAllChildren(true);
        for (let i = 0; i < len; i++) {
            let tempNode = new cc.Node();
            tempNode.setContentSize(30, 48);
            node.addChild(tempNode);
            let spr = new cc.Node().addComponent(cc.Sprite);
            spr.spriteFrame = this._atlasDZNZ.getSpriteFrame(
                (isYellow ? 'coin_yellow_' : 'coin_gray_') + str.charAt(i)
            );
            tempNode.addChild(spr.node);
        }
    }

    // isCircleCoin(gold: number): boolean {
    //     let llRealGold = pf.StringUtil.clientGoldByServer(gold);
    //     // let fileName = llRealGold < cb.getCowboyRoom().llCoinUICritical ? "bet_coin_clicked" : "bet_block_clicked";
    //     return llRealGold < this._cowboyRoom.llCoinUICritical;
    // }

    // 下注动画，金币飞到池中动画
    showBetInAnim(oneBet: PlayerOneBet, ignoreEffect?: boolean): void {
        const checkedIsIgnoreEffect = ignoreEffect === true ? true : false;
        // let oneBet = cb.getCowboyRoom().curPlayerBet;
        this.updatePlayerCoin(oneBet.uid);
        this.updateBetArea(oneBet.betOption);

        // 下注动画
        let areaIdx = this.getAreaIdxByBetOption(oneBet.betOption);
        let playerHeads = this.getPlayerCoinNodesByUid(oneBet.uid);
        if (playerHeads.length === 0) {
            console.log('this.showBetInAnim, cannot find valid headBg, use btnPlayerList, oneBet.uid: %d', oneBet.uid);
            playerHeads.push(this._btnPlayerList.node);
        }

        // 自己是富豪/神算子时，下注的筹码只显示一个
        // let isCircleCoin = this.isCircleCoin(oneBet.betAmount);
        let coinContent = this._betCoinContents[areaIdx];
        let sz = coinContent.getContentSize();

        for (let i = 0; i < playerHeads.length; i++) {
            let fromHead = playerHeads[i];
            let coinFlyWorldBorn = fromHead.getParent().convertToWorldSpaceAR(fromHead.getPosition());

            // 发射摇头动画

            // let headImg = fromHead.getChildByName(this._HEAD_IMG_TAG);
            // let circleHeadNode = CircleSprite.getHeadNode(headImg);

            if (
                fromHead !== this._selfHeadBg &&
                fromHead !== this._btnPlayerList.node &&
                // circleHeadNode &&
                cc.director.getActionManager().getNumberOfRunningActionsInTarget(fromHead) <= 0
            ) {
                let ac: cc.Action = null;
                if (coinFlyWorldBorn.x < pf.system.view.width / 2) {
                    ac = cc.sequence(
                        cc.moveBy(0.1, cc.v2(-30, 0)),
                        cc.moveBy(0.1, cc.v2(30, 0)).easing(cc.easeInOut(1.0))
                    );
                } else {
                    ac = cc.sequence(
                        cc.moveBy(0.1, cc.v2(30, 0)),
                        cc.moveBy(0.1, cc.v2(-30, 0)).easing(cc.easeInOut(1.0))
                    );
                }

                fromHead.runAction(ac);
            }

            // 富豪和神算子是自己的情况，只下一个金币和播放一次音效
            if (oneBet.uid === this._authService.currentUser.userId && i > 0) continue;

            // 下注音效
            if (!checkedIsIgnoreEffect) {
                // if (isCircleCoin) {
                //     this._playSoundEffect(macros.Audio.BET);
                // } else {
                //     this._playSoundEffect(macros.Audio.BET_MANY);
                // }
                let llRealGold: number = pf.StringUtil.clientGoldByServer(oneBet.betAmount);
                let sound: string =
                    llRealGold < this._cowboyRoom.llCoinUICritical ? macros.Audio.BET : macros.Audio.BET_MANY;

                if (sound !== this._lastSoundName || Date.now() - this._lastSoundTime > 200) {
                    this._lastSoundTime = Date.now();
                    this._lastSoundName = sound;
                    this._playSoundEffect(sound);
                }
            }

            let coinFlyBorn = this._coinNode.convertToNodeSpaceAR(coinFlyWorldBorn);
            let flyCoin: cc.Sprite = this.createFlyCoin(areaIdx, oneBet.betAmount);

            let coinFlyTargetPos: cc.Vec2 = this.getRandPos(sz, flyCoin.node);
            coinFlyTargetPos = this._coinNode.convertToNodeSpaceAR(coinContent.convertToWorldSpaceAR(coinFlyTargetPos));

            flyCoin.node.setPosition(coinFlyBorn);
            // flyCoin.node.active = false;
            // this.scheduleOnce(function () {
            // flyCoin.node.active = true;
            if (i === 0) {
                flyCoin.node.runAction(
                    cc.sequence(cc.moveTo(0.3, coinFlyTargetPos), cc.rotateBy(0.15, 180), cc.rotateBy(0.15, 180))
                );
            } else {
                flyCoin.node.runAction(
                    cc.sequence(
                        cc.moveTo(0.3, coinFlyTargetPos),
                        cc.callFunc(() => {
                            flyCoin.node.active = false;
                        })
                    )
                );
            }
            // }.bind(this), 0.13);
        }
    }

    SERangeRandomf(min: number, max: number): number {
        return min + Math.random() * (max - min);
    }

    // 等待下一局动画
    showWaitForNextRoundInAnim(): void {
        this._waitForNextRoundAnim.active = true;
        this.gotoFrameAndPlay(this._waitForNextRoundAction, 0, 30, false);
    }

    showWaitForNextRoundOutAnim(): void {
        if (this._waitForNextRoundAnim && this._waitForNextRoundAnim.active) {
            this._waitForNextRoundAnim.runAction(
                cc.sequence(
                    cc.moveTo(0.3, cc.v2(pf.system.view.width * 1.25, 0)),
                    cc.callFunc(() => {
                        this._waitForNextRoundAnim.active = false;
                        this._waitForNextRoundAnim.setPosition(cc.v2(0, 0));
                    })
                )
            );
        }
    }

    gotoFrameAndPlay(ani: cc.Animation, startIndex: number, endIndex: number, loop: boolean) {
        ani.play();
    }
    // ===============animation end===============

    initBetArea(): void {
        let bet_content_bg_ipx = this._gameContent.getChildByName('bet_content_bg_ipx');
        let bet_content_bg = this._gameContent.getChildByName('bet_content_bg');
        let bet_content_bg_ipad = this._gameContent.getChildByName('bet_content_bg_ipad');

        const canvas = this.getComponent(cc.Canvas);

        const fitScale = canvas.fitWidth
            ? pf.system.view.width / canvas.designResolution.width
            : pf.system.view.height / canvas.designResolution.height;

        this.eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NORMAL;
        if (pf.system.view.isNarrowScreen()) {
            let fTotalWidth = 0;
            fTotalWidth += 2 * pf.system.view.iphoneXOffset;
            fTotalWidth += bet_content_bg_ipx.width;
            // right and left player panel?
            fTotalWidth += 157 - 25;
            fTotalWidth += 157 - 25;
            if (fTotalWidth * fitScale <= pf.system.view.width) {
                this.eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW;
            }
        } else if (pf.system.view.isWideScreen() && pf.system.view.height / pf.system.view.width > 0.71) {
            let fTotalHeight = 0;
            fTotalHeight += bet_content_bg_ipad.height;
            fTotalHeight += this._bottomPanel.height;
            fTotalHeight += this._cardPanel.height;
            if (fTotalHeight * fitScale <= pf.system.view.height) {
                this.eGameboyScreenType = MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD;
            }
        }

        // let bTrueFullScreen = fTotalWidth <= pf.system.view.width;

        // if (pf.system.view.isWideScreen()) {
        //     if (cc.sys.os === cc.sys.OS_ANDROID) {
        //         let size = cc.winSize;
        //         if (size.width / size.height <= 1920 / 1439) {
        //             this._isIpad = true;
        //         }
        //     } else {
        //         this._isIpad = true;
        //     }
        // }

        // if (bTrueFullScreen && pf.system.view.isFullScreen()) {
        if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            // iphoneX
            // this._isViewX = true;
            this._betContentBg = bet_content_bg_ipx.getComponent(cc.Sprite);
            if (pf.UIUtil.isValidNode(bet_content_bg)) {
                bet_content_bg.removeFromParent(true);
                bet_content_bg.destroy();
            }
            if (pf.UIUtil.isValidNode(bet_content_bg_ipad)) {
                bet_content_bg_ipad.removeFromParent(true);
                bet_content_bg_ipad.destroy();
            }
            // } else if (this._isIpad) {
        } else if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
            // ipad
            this._betContentBg = bet_content_bg_ipad.getComponent(cc.Sprite);
            if (pf.UIUtil.isValidNode(bet_content_bg)) {
                bet_content_bg.removeFromParent(true);
                bet_content_bg.destroy();
            }
            if (pf.UIUtil.isValidNode(bet_content_bg_ipx)) {
                bet_content_bg_ipx.removeFromParent(true);
                bet_content_bg_ipx.destroy();
            }
        } else {
            // 普通分辨率
            this._betContentBg = bet_content_bg.getComponent(cc.Sprite);
            if (pf.UIUtil.isValidNode(bet_content_bg_ipx)) {
                bet_content_bg_ipx.removeFromParent(true);
                bet_content_bg_ipx.destroy();
            }
            if (pf.UIUtil.isValidNode(bet_content_bg_ipad)) {
                bet_content_bg_ipad.removeFromParent(true);
                bet_content_bg_ipad.destroy();
            }
        }

        if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
            // let _bgName = bTrueFullScreen
            //     ? macros.Dynamic_Assets.COWBOY_TABLE_NARROW_SPRITE
            //     : macros.Dynamic_Assets.COWBOY_TABLE_SPRITE;
            // if (this._isIpad) {
            //     _bgName = macros.Dynamic_Assets.COWBOY_TABLE_BROAD_SPRITE;
            // }
            let _bgName = macros.Dynamic_Assets.COWBOY_TABLE_SPRITE;
            if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
                _bgName = macros.Dynamic_Assets.COWBOY_TABLE_NARROW_SPRITE;
            } else if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
                _bgName = macros.Dynamic_Assets.COWBOY_TABLE_BROAD_SPRITE;
            }
            pf.addressableAssetManager.loadAsset(_bgName).then((asset: cc.SpriteFrame) => {
                this._betContentBg.node.getComponent(cc.Sprite).spriteFrame = asset;
            });
        }

        this._betContentBg.node.active = true;
        this._coinNodeByArea = [];

        this.initBetOptionArea();

        this.initBetAreaContent();
    }

    protected initBetOptionArea(): void {
        // 下注区域映射
        this._mapBetOptionArea.set(network.BetZoneOption.RED_WIN, 0);
        this._mapBetOptionArea.set(network.BetZoneOption.EQUAL, 1);
        this._mapBetOptionArea.set(network.BetZoneOption.BLUE_WIN, 2);

        this._mapBetOptionArea.set(network.BetZoneOption.HOLE_3_TONG_SAME_SHUN, 3);
        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_NONE_1DUI, 4);
        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_2DUI, 5);
        this._mapBetOptionArea.set(network.BetZoneOption.HOLE_SAME, 6);
        this._mapBetOptionArea.set(network.BetZoneOption.HOLE_A, 7);

        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_3_SHUN_TONG_HUA, 8);
        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_3_2, 9);
        this._mapBetOptionArea.set(network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4, 10);
    }

    protected initBetAreaContent() {
        // 按区域索引升序
        let vAreaIdx: number[] = [];
        this._mapBetOptionArea.forEach((value: number, key: number) => {
            vAreaIdx.push(value);
        });

        vAreaIdx.sort((a: number, b: number): number => {
            return a > b ? 1 : -1;
        });

        // 对应区域赔率数组
        let vAreaIdxLen = vAreaIdx.length;

        for (let i = 0; i < vAreaIdxLen; ++i) {
            let text_self_bet_num = this._betContentBg.node.getChildByName('text_self_bet_num_' + i);
            let text_total_bet_num = this._betContentBg.node.getChildByName('text_total_bet_num_' + i);
            this._textSelfBetNum.push(text_self_bet_num.getComponent(cc.Label));
            this._textTotalBetNum.push(text_total_bet_num.getComponent(cc.Label));
            this._oriTextSelfBetNumPos.push(text_self_bet_num.getPosition());
            this._oriTextTotalBetNumPos.push(text_total_bet_num.getPosition());

            let fnt_odd = this._betContentBg.node.getChildByName('fnt_odd_' + i).getComponent(cc.Label);
            fnt_odd.string = '';
            this._textBetAreaOdds.push(fnt_odd);

            let iAreaIdx = vAreaIdx[i];
            let betArea = this._betContentBg.node.getChildByName(pf.StringUtil.formatC('bet_area_%d', iAreaIdx));
            let coin_content = betArea.getChildByName('coin_content');

            betArea.on(cc.Node.EventType.TOUCH_END, (): void => {
                this.betAreaClicked(iAreaIdx);
            });

            this._betAreas.push(betArea);
            this._betCoinContents.push(coin_content);
            this._coinNodeByArea.push([]);

            let winFlag = betArea.getChildByName('win_flag');
            this._sprBetAreaWinFlags.push(winFlag.getComponent(cc.Sprite));

            // 初始化路子信息
            this.initWayOutInfoByAreaIdx(iAreaIdx);
        }
    }

    betAreaClicked(areaIdx: number): void {
        console.log('betAreaClicked, areaIdx: %d', areaIdx);

        if (this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            // 可以下注
            if (this._curBetButtonIdx < 0) {
                // showCowboyToast(cv.config.getStringData("Cowboy_select_bet_button_first_text"));
                return;
            } else {
                // if (!cv.C2CNotify.isAllowBet()) return;
                // cv.cowboyNet.RequestBet(this.getBetOptionByAreaIdx(areaIdx), this.getCurBetLevel());
                this._cowboyRoom.bet(this.getBetOptionByAreaIdx(areaIdx), this.getCurBetLevel());
            }
        } else {
            console.log(
                'betAreaClicked, cannot bet, curState: %d, left bet time: %d',
                this._cowboyRoom.gameState.roundState,
                this._leftTime
            );
            // showCowboyToast(cv.config.getStringData("Cowboy_cannot_bet_now_text"));
        }
    }

    updateBetArea(option: number): void {
        let areaIdx = this.getAreaIdxByBetOption(option);

        // 自己的下注
        // let it = cb.getCowboyRoom().selfZoneBet.get(option);
        // if (it) {
        //     this.updateSelfBetAreaCoin(areaIdx, it);
        // }

        // 总共的下注
        // let it2 = cb.getCowboyRoom().totalZoneBet.get(option);
        // if (it2) {
        //     this.updateTotalBetAreaCoin(areaIdx, it2);
        // }
        const betZone = this._cowboyRoom.betZones.get(option);
        if (betZone) {
            this.updateSelfBetAreaCoin(areaIdx, betZone.optionInfo.selfBet);
            this.updateTotalBetAreaCoin(areaIdx, betZone.optionInfo.totalBet);
        }
    }

    updateSelfBetAreaCoin(areaIdx: number, coin: number): void {
        let text = this._textSelfBetNum[areaIdx];
        if (!text) return;
        text.node.setPosition(this._oriTextSelfBetNumPos[areaIdx]);
        text.node.color = coin >= 0 ? new cc.Color(232, 201, 147) : cc.Color.WHITE;
        if (coin >= 100) {
            text.string = this.getBetAreaCoinString(coin);
        } else {
            text.string = '';
        }
    }

    updateTotalBetAreaCoin(areaIdx: number, coin: number): void {
        let text = this._textTotalBetNum[areaIdx];
        if (!text) return;
        text.node.setPosition(this._oriTextTotalBetNumPos[areaIdx]);

        if (coin >= 100) {
            text.string = this.getBetAreaCoinString(coin);
        } else {
            text.string = '';
        }
    }

    updateAllBetAreas(): void {
        // let tempMap = cb.getCowboyRoom().selfZoneBet;
        // // 自己下注详情
        // tempMap.forEach(
        //     function (key: number, value: number, i: number) {
        //         this.updateSelfBetAreaCoin(this.getAreaIdxByBetOption(key), value);
        //     }.bind(this)
        // );

        // // 总的下注详情
        // cb.getCowboyRoom().totalZoneBet.forEach(
        //     function (key: number, value: number, i: number) {
        //         let areaIdx = this.getAreaIdxByBetOption(key);
        //         let coin = value;
        //         this.updateTotalBetAreaCoin(areaIdx, coin);

        //         // 随机位置生成金币
        //         if (coin > 0) {
        //             let coinContent = this._betCoinContents[areaIdx];
        //             coinContent.destroyAllChildren();
        //             coinContent.removeAllChildren(true);
        //             let sz = coinContent.getContentSize();

        //             //let betDetails = getBetDetailAmounts(coin);
        //             let betDetails = this.getAllBetDetailAmounts(key);
        //             for (let j = 0; j < betDetails.length; j++) {
        //                 let flyCoin: cc.Sprite = this.createFlyCoin(areaIdx, betDetails[j]);
        //                 let coinFlyBorn: cc.Vec2 = this.getRandPos(sz, flyCoin.node);
        //                 coinFlyBorn = this._coinNode.convertToNodeSpaceAR(
        //                     coinContent.convertToWorldSpaceAR(coinFlyBorn)
        //                 );

        //                 flyCoin.node.setPosition(coinFlyBorn);
        //             }
        //         }
        //     }.bind(this)
        // );
        this._cowboyRoom.betZones.forEach((betZone) => {
            const areaIdx = this.getAreaIdxByBetOption(betZone.option);

            // 自己下注详情
            this.updateSelfBetAreaCoin(areaIdx, betZone.optionInfo.selfBet);

            // 总的下注详情
            this.updateTotalBetAreaCoin(areaIdx, betZone.optionInfo.totalBet);
            if (betZone.optionInfo.totalBet > 0) {
                let coinContent = this._betCoinContents[areaIdx];
                coinContent.destroyAllChildren();
                coinContent.removeAllChildren(true);
                let sz = coinContent.getContentSize();

                // let betDetails = getBetDetailAmounts(coin);
                let betDetails = this.getAllBetDetailAmounts(betZone.option);
                for (const detail of betDetails) {
                    let flyCoin: cc.Sprite = this.createFlyCoin(areaIdx, detail);
                    let coinFlyBorn: cc.Vec2 = this.getRandPos(sz, flyCoin.node);
                    coinFlyBorn = this._coinNode.convertToNodeSpaceAR(coinContent.convertToWorldSpaceAR(coinFlyBorn));

                    flyCoin.node.setPosition(coinFlyBorn);
                }
            }
        });
    }

    initBetCountDown(): void {
        this._betCountDown = this._gameContent.getChildByName('bet_count_down_bg');
        const betCountDownBg = this._betCountDown.getComponent(cc.Sprite);
        if (pf.languageManager.currentLanguage !== pf.LANGUAGE_GROUPS.zh_CN) {
            // cb.loadSpriteTextureByPlist(this.language_PLIST, this._betCountDownBg, 'bet_count_down');
            betCountDownBg.spriteFrame = this.language_PLIST.getSpriteFrame('bet_count_down');
        }
        this._textCountDown = this._betCountDown.getChildByName('text_count_down').getComponent(cc.Label);
        this._oriBetCountDownBgPos = this._betCountDown.getPosition();
    }

    hideBetCountDown(): void {
        this._betCountDown.stopAllActions();
        this._betCountDown.active = false;
        this._betCountDown.setPosition(this._oriBetCountDownBgPos);
        this.unschedule(this.updateBetTimer);
    }

    updateBetCoutDown(): void {
        this.hideBetCountDown();
        if (this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            // 可以下注
            this._betCountDown.active = true;
            this._textCountDown.string = pf.StringUtil.formatC('%lld', this._leftTime);
            this.schedule(this.updateBetTimer, 1.0);
        }
    }

    updateBetTimer(f32Delta: number): void {
        if (this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            this._betCountDown.active = true;
            this._textCountDown.string = pf.StringUtil.formatC('%lld', this._leftTime);
            this._playSoundEffect(macros.Audio.TIME_TICK);
        } else {
            this._textCountDown.string = '0';
        }
    }

    hideGameTips(): void {
        this._gameTipsBg.active = false;
        this.unschedule(this.updateNextRoundTipsTimer);
    }

    showNextRoundTips(): void {
        if (this._cowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND && this._leftTime > 0) {
            this.clearRound();
            this._gameTipsBg.active = true;
            // this._gameTipsBg.active = false;
            this._textGameTips.string = pf.StringUtil.formatC(
                pf.languageManager.getString('Cowboy_game_tips_wait_next_round_text'),
                this._leftTime
            );
            this.unschedule(this.updateNextRoundTipsTimer);
            this.schedule(this.updateNextRoundTipsTimer, 1.0);

            if (this._waitForNextRoundAnim.active && this._leftTime <= this._waitForNextRoundOutTheshould) {
                this.showWaitForNextRoundOutAnim();
            }
        }
    }

    updateNextRoundTipsTimer(f32Delta: number): void {
        if (this._cowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND && this._leftTime > 0) {
            this._textGameTips.string = pf.StringUtil.formatC(
                pf.languageManager.getString('Cowboy_game_tips_wait_next_round_text'),
                this._leftTime
            );

            if (this._waitForNextRoundAnim.active && this._leftTime <= this._waitForNextRoundOutTheshould) {
                this.showWaitForNextRoundOutAnim();
            }
        } else {
            this.hideGameTips();
        }
    }

    showSendCardTips(): void {
        if (this._cowboyRoom.gameState.roundState === network.RoundState.NEW_ROUND) {
            // 暂时不要提示
            // this._gameTipsBg.node.active = (true);
            this._gameTipsBg.active = false;
            this._textGameTips.string = pf.languageManager.getString('Cowboy_game_tips_send_card_text');
        }
    }

    showOpenCardTips(): void {
        if (
            this._cowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND &&
            this._leftTime > this._betWinFlagsAndFlyCoinsDuration + this._showNextRoundDuration
        ) {
            // 暂时不要提示
            // this._gameTipsBg..node.active = (true);
            this._gameTipsBg.active = false;
            this._textGameTips.string = pf.languageManager.getString('Cowboy_game_tips_open_card_text');
        }
    }

    initPlayersInfo(): void {
        // 自己
        this.self_panel = this._bottomPanel.getChildByName('self_panel');
        this._textNickName = this.self_panel.getChildByName('text_nickname').getComponent(cc.Label);
        this._textCoin = this.self_panel.getChildByName('text_coin').getComponent(cc.Label);
        this._selfHeadBg = this.self_panel.getChildByName('img_head_box');
        this._selfCoin = this.self_panel.getChildByName('own_coin').getComponent(cc.Sprite);
        this.selfAvatar = this.self_panel.getChildByName('Avatar').getComponent(AvatarControl);

        // 其他玩家
        this._leftPlayerPanel = this.node.getChildByName('leftPlayerPanel');
        this._rightPlayerPanel = this.node.getChildByName('rightPlayerPanel');

        // let bIpad = this._isIpad;

        this.setLeftAndRightList();

        for (let i = 0; i < 5; i++) {
            {
                let player = new OtherPlayerHead();
                let playerBg = this._leftPlayerPanel
                    .getChildByName(pf.StringUtil.formatC('player_%d', i))
                    .getComponent(cc.Sprite);
                player.bg = playerBg;
                player.textCoin = this._leftPlayerPanel.getChildByName('text_coin_' + i).getComponent(cc.Label);
                const avatar = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.AVATAR));
                avatar.name = this._HEAD_IMG_TAG;
                playerBg.node.addChild(avatar);
                avatar.setPosition(avatar.position.x, avatar.position.y + macros.AVATAR_LOCAL_OFFSET);
                player.avatarControl = avatar.getComponent(AvatarControl);

                if (i === 0) {
                    player.nbFlag = playerBg.node.getChildByName('nb_flag');
                    player.nbFlag.zIndex = 11;
                    player.nbFlag.getChildByName('nb_flag_desc').getComponent(cc.Label).string = pf.StringUtil.formatC(
                        pf.languageManager.getString('Cowboy_fuhao_no_text'),
                        1
                    );
                }

                // if (!bIpad && i == 4) {
                if (this.eGameboyScreenType !== MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD && i === 4) {
                    playerBg.node.active = false;
                    player.textCoin.node.active = false;
                } else {
                    this._otherPlayerHeads.push(player);
                }
            }
            {
                let player = new OtherPlayerHead();
                let playerBg = this._rightPlayerPanel
                    .getChildByName(pf.StringUtil.formatC('player_%d', i))
                    .getComponent(cc.Sprite);
                player.bg = playerBg;
                player.textCoin = this._rightPlayerPanel.getChildByName('text_coin_' + i).getComponent(cc.Label);
                const avatar = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.AVATAR));
                avatar.name = this._HEAD_IMG_TAG;
                playerBg.node.addChild(avatar);
                avatar.setPosition(avatar.position.x, avatar.position.y + macros.AVATAR_LOCAL_OFFSET);
                player.avatarControl = avatar.getComponent(AvatarControl);

                if (i === 0) {
                    player.nbFlag = playerBg.node.getChildByName('nb_flag');
                    player.nbFlag.zIndex = 11;
                    player.nbFlag.getChildByName('nb_flag_desc').getComponent(cc.Label).string =
                        pf.languageManager.getString('Cowboy_shensuanzi_text');
                }

                // if (!bIpad && i == 4) {
                if (this.eGameboyScreenType !== MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD && i === 4) {
                    playerBg.node.active = false;
                    player.textCoin.node.active = false;
                } else {
                    this._otherPlayerHeads.push(player);
                }
            }
        }
    }

    // iPad/iPhoneX等宽窄屏适配
    adaptiveScreen(): void {
        // if (this._openIphoneXAdapter && pf.system.view.isFullScreen()) {
        if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            let tmp_x = pf.system.view.iphoneXOffset - 25;
            // this._leftPlayerPanel.setPosition(this._leftPlayerPanel.x + tmp_x, this._leftPlayerPanel.y);
            // this._rightPlayerPanel.setPosition(this._rightPlayerPanel.x - tmp_x, this._rightPlayerPanel.y);
            this._btnMenu.node.setPosition(this._leftPlayerPanel.x, this._btnMenu.node.y);
            this._btnPlayerList.node.setPosition(this._rightPlayerPanel.x, this._btnPlayerList.node.y);
            let newHeadPos = this._btnMenu.node.parent.convertToWorldSpaceAR(this._btnMenu.node.position);
            newHeadPos = this._selfHeadBg.parent.convertToNodeSpaceAR(newHeadPos);
            this.self_panel.setPosition(this.self_panel.x + newHeadPos.x - this._selfHeadBg.x, this.self_panel.y);
            // } else if (this._isIpad) {
        } else if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
            let heroOffsetY = 166;
            let cardOffsetY = 170;
            let headOffsetY = 60;
            let temp_x = 10;
            // let img = this._heroBoy;
            if (
                this._heroBoy.parent.convertToWorldSpaceAR(this._heroBoy.position).y + this._heroBoy.height * 0.5 >
                this._topBg.node.parent.convertToWorldSpaceAR(this._topBg.node.position).y +
                    this._topBg.node.height * 0.5
            )
                return;

            // this._leftPlayerPanel.setPosition(this._leftPlayerPanel.x - temp_x, this._leftPlayerPanel.y + headOffsetY);
            // this._rightPlayerPanel.setPosition(this._rightPlayerPanel.x + temp_x, this._rightPlayerPanel.y + headOffsetY);

            this._heroBoy.setPosition(this._heroBoy.x, heroOffsetY + this._heroBoy.y);
            this._heroCow.setPosition(this._heroCow.x, heroOffsetY + this._heroCow.y);

            this._cardPanel.setPosition(this._cardPanel.x, cardOffsetY + this._cardPanel.y);
            this._betCountDown.setPosition(this._betCountDown.x, cardOffsetY + this._betCountDown.y);

            this._oriBetCountDownBgPos = this._betCountDown.getPosition();
            this._btnMenu.node.getComponent(cc.Widget).top = 68;
            pf.UIUtil.adaptWidget(this._btnMenu.node);
            this._btnMenu.node.setPosition(this._leftPlayerPanel.x, this._btnMenu.node.y);
        } else {
            this._btnMenu.node.setPosition(this._leftPlayerPanel.x, this._btnMenu.node.y);
        }
    }

    adaptiveBetBtnPanel(): void {
        // 若为空, 则填充按钮数组
        if (this._vBottomBetBtns.length === 0) {
            // 下注按钮
            let betButtons_len = pf.StringUtil.getArrayLength(this._betButtons);
            for (let i = 0; i < betButtons_len; ++i) {
                this._vBottomBetBtns.push(
                    new MiniGameCommonDef.GameNodeScale(this._betButtons[i].node, this.fBetBtnSrcScaleRate)
                );
            }

            // 续投按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btnBetAuto.node, this._btnBetAuto.node.scale)
            );

            // 清屏按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btnBetClean.node, this._btnBetClean.node.scale)
            );

            // 红包节按钮
            this._vBottomBetBtns.push(
                new MiniGameCommonDef.GameNodeScale(this._btn_redpacket_festival, this._btn_redpacket_festival.scale)
            );
        }

        let w = this._btnPlayerList.node.x - this._btnPlayerList.node.getContentSize().width / 2;
        w -= this.self_panel.x + this.self_panel.getContentSize().width / 2;
        this._panel_betbtn.setContentSize(cc.size(w, this._panel_betbtn.getContentSize().height));
        this._panel_betbtn.setPosition(
            this.self_panel.x + w / 2 + this.self_panel.getContentSize().width / 2,
            this._panel_betbtn.y
        );

        let iTotal_w = 0; // 所有可见子节点宽度和
        let iSpacing_x = 0; // 子节点之间的间距
        let iChildrenCount = 0; // 可见的子节点个数

        let vBottomBetBtns_len = pf.StringUtil.getArrayLength(this._vBottomBetBtns);
        for (let i = 0; i < vBottomBetBtns_len; ++i) {
            let node = this._vBottomBetBtns[i].node;
            let fScale = this._vBottomBetBtns[i].scale;
            if (node.active) {
                ++iChildrenCount;
                iTotal_w += node.getContentSize().width * fScale;
            }
        }

        iSpacing_x = (this._panel_betbtn.getContentSize().width - iTotal_w) / (iChildrenCount + 1);

        let iLast_w = -this._panel_betbtn.width * 0.5;
        for (let i = 0; i < vBottomBetBtns_len; ++i) {
            let node = this._vBottomBetBtns[i].node;
            let fScale = this._vBottomBetBtns[i].scale;
            if (node.active) {
                let szNode = node.getContentSize();
                let x = iLast_w + iSpacing_x + (szNode.width * fScale) / 2;
                let pos = this._panel_betbtn.convertToWorldSpaceAR(cc.v2(x, 0));
                pos = node.getParent().convertToNodeSpaceAR(pos);
                node.setPosition(pos.x, node.y);
                iLast_w = pos.x + (szNode.width * fScale) / 2;
            }
        }

        // 适配红包节入口节点提示层
        if (this._btn_redpacket_festival_layer) {
            let wpos: cc.Vec2 = this._btn_redpacket_festival.convertToWorldSpaceAR(cc.Vec2.ZERO);
            this._btn_redpacket_festival_layer.setPosition(
                this._btn_redpacket_festival_layer.parent.convertToNodeSpaceAR(wpos)
            );
        }

        // 适配高级续投提示语位置
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoTipsPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
        }
    }

    getPlayerCoinNodesByUid(uid: number): cc.Node[] {
        let ret: cc.Node[] = [];
        if (uid === this._authService.currentUser.userId) {
            ret.push(this._selfCoin.node);
        }

        let len = this._otherPlayerHeads.length;
        for (let i = 0; i < len; i++) {
            if (this._otherPlayerHeads[i].uid === uid) {
                ret.push(this._otherPlayerHeads[i].bg.node);
            }
        }
        return ret;
    }

    getPlayerHeadNodesByUid(uid: number): cc.Node[] {
        let ret: cc.Node[] = [];
        if (uid === this._authService.currentUser.userId) {
            ret.push(this._selfHeadBg);
        }

        for (const head of this._otherPlayerHeads) {
            if (head.uid === uid) {
                ret.push(head.bg.node);
            }
        }
        return ret;
    }

    updatePlayerCoin(uid: number): void {
        if (uid === this._authService.currentUser.userId) {
            this._textCoin.string = cr.CommonUtil.getShortOwnCoinString(this._cowboyRoom.selfPlayer.curCoin);
        }

        for (const head of this._otherPlayerHeads) {
            if (head.uid === uid) {
                // 神算子/富豪是自己的情況
                if (uid === this._authService.currentUser.userId) {
                    head.textCoin.string = cr.CommonUtil.getShortOwnCoinString(this._cowboyRoom.selfPlayer.curCoin);
                } else {
                    let player = this._cowboyRoom.getOtherPlayerByUid(uid);
                    if (player) {
                        head.textCoin.string = cr.CommonUtil.getShortOwnCoinString(player.curCoin);
                        // player.reset();
                    }
                }
            }
        }
    }

    // getShortOwnCoinString(coin: number): string {
    //     let formatCoin = pf.StringUtil.clientGoldByServer(coin);
    //     if (pf.StringUtil.numberToShowNumber(formatCoin) < 10000) {
    //         return pf.StringUtil.numberToString(formatCoin);
    //     } else {
    //         return (
    //             pf.StringUtil.numberToString(pf.StringUtil.clientGoldByServer(coin / 10000)) +
    //             pf.languageManager.getString('Cowboy_coin_short_text')
    //         );
    //     }
    // }

    // 下注区域金币：超过10万显示xxW
    getBetAreaCoinString(coin: number): string {
        let formatCoin = pf.StringUtil.clientGoldByServer(coin);
        if (pf.StringUtil.numberToShowNumber(formatCoin) < 100000) {
            return pf.StringUtil.numberToString(formatCoin);
        } else {
            return (
                pf.StringUtil.formatC('%lld', pf.StringUtil.numberToShowNumber(formatCoin) / 10000) +
                pf.languageManager.getString('Cowboy_coin_short_text')
            );
        }
    }

    updateSelfCoin(): void {
        this._textCoin.string = cr.CommonUtil.getShortOwnCoinString(this._cowboyRoom.selfPlayer.curCoin);
    }

    updateOtherCoin(): void {
        let len = this._otherPlayerHeads.length;
        let otherPlayersInfo = this._cowboyRoom.otherPlayers;
        let infoLen = otherPlayersInfo.length;
        for (let i = 0; i < len; i++) {
            if (i < infoLen) {
                let info = otherPlayersInfo[i];
                this._otherPlayerHeads[i].textCoin.string = cr.CommonUtil.getShortOwnCoinString(info.curCoin);
            }
        }
    }

    updateSelfInfo(): void {
        // 未处理
        // this._textNickName.string = cb.getCowboyRoom().selfPlayer.name;
        pf.StringUtil.setShrinkString(this._textNickName.node, this._cowboyRoom.selfPlayer.name, true);

        let llCurCoin: number = this._cowboyRoom.selfPlayer.curCoin;
        this._textCoin.string = cr.CommonUtil.getShortOwnCoinString(llCurCoin);

        // 头像
        //    let this._selfHeadBg.getChildByName(_HEAD_IMG_TAG);
        //     let head = CowboyHeadSprite. create("self_head_default", "head_mask");
        //     head.setTag(_HEAD_IMG_TAG);
        //     head.setAnchorPoint(Vec2. ANCHOR_MIDDLE);
        //     head.setPosition(this._selfHeadBg.getContentSize() / 2);
        // let headUrl = cb.getCowboyRoom().selfPlayer.head;
        // head.UpdateSpriteFromUrl(headUrl);
        // this._selfHeadBg.addChild(head, 9);
        // CircleSprite.setCircleSprite(this._selfHeadBg, headUrl);
        this.selfAvatar.loadHeadImage(this._cowboyRoom.selfPlayer.head, this._cowboyRoom.selfPlayer.plat);
        this._cowboyRoom.setPlayerBeforeSettlementGold(this._authService.currentUser.userId, llCurCoin);
    }

    updateOtherPlayersInfo(): void {
        // 未处理
        // 这里按照服务器发的gamePlayers顺序放
        let len = this._otherPlayerHeads.length;
        let otherPlayersInfo = this._cowboyRoom.otherPlayers;
        let infoLen = otherPlayersInfo.length;
        for (let i = 0; i < len; i++) {
            if (i < infoLen) {
                // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._otherPlayerHeads[i].bg, 'player_bg');
                this._otherPlayerHeads[i].bg.spriteFrame = this._atlasDZNZ.getSpriteFrame('player_bg');
                if (this._otherPlayerHeads[i].nbFlag) {
                    this._otherPlayerHeads[i].nbFlag.active = true;
                }

                let info = otherPlayersInfo[i];
                this._otherPlayerHeads[i].uid = info.uid;
                this._otherPlayerHeads[i].textCoin.string = cr.CommonUtil.getShortOwnCoinString(info.curCoin);

                // 头像更新
                // let headBg = this._otherPlayerHeads[i].bg;
                // let head = headBg.node.getChildByName(this._HEAD_IMG_TAG);
                // if (!head) {
                //     head = new cc.Node();
                //     head.addComponent(cc.Sprite);
                //     cv.resMgr.setSpriteFrame(head, 'zh_CN/game/cowboy/head/display_base');

                //     // let headSprite = head.addComponent(cc.Sprite);
                //     // if (info.uid == cv.dataHandler.getUserData().u32Uid) {
                //     //     cb.loadSpriteTextureByPlist(this._atlasDZNZ, headSprite, "self_head_default_2");
                //     // }
                //     // else {
                //     //     cb.loadSpriteTextureByPlist(this._atlasDZNZ, headSprite, "other_head_default");
                //     // }
                //     head.name = this._HEAD_IMG_TAG;
                //     head.setAnchorPoint(cc.v2(0.5, 0.5));
                //     // head.setPosition(headBg.getContentSize() / 2);
                //     head.setPosition(cc.v2(0, 15)); //head.getPosition() +
                //     headBg.node.addChild(head, 9);
                //     head.getComponent(cc.Sprite).sizeMode = cc.Sprite.SizeMode.RAW;
                //     head.getComponent(cc.Sprite).trim = true;

                //     head.active = false;
                // }
                // let headUrl = info.head;
                // CircleSprite.setCircleSprite(
                //     head,
                //     headUrl,
                //     info.plat,
                //     undefined,
                //     Head_Mode.IRREGULAR,
                //     true,
                //     true,
                //     false
                // );
                this._otherPlayerHeads[i].avatarControl.node.active = true;
                const headPath = cr.CommonUtil.getHeadPath(info, this._authService.currentUser.userId);
                this._otherPlayerHeads[i].avatarControl.loadHeadImage(headPath, info.plat);
            } else {
                let win_player_win_count = this.node.getChildByName(
                    'win_player_win_count_' + this._otherPlayerHeads[i].bg.node.uuid
                );
                if (pf.UIUtil.isValidNode(win_player_win_count)) {
                    win_player_win_count.removeFromParent(true);
                    win_player_win_count.destroy();
                }

                // let headBg = this._otherPlayerHeads[i].bg;
                // let head = headBg.node.getChildByName(this._HEAD_IMG_TAG);
                // if (head) {
                //     head.active = false;
                //     let circleHeadNode = CircleSprite.getHeadNode(head);
                //     if (circleHeadNode) {
                //         circleHeadNode.parent.active = false;
                //     }
                // }
                this._otherPlayerHeads[i].avatarControl.node.active = false;
                this._otherPlayerHeads[i].uid = 0;
                // this._otherPlayerHeads[i].bg.removeChildByTag(_HEAD_IMG_TAG);

                // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._otherPlayerHeads[i].bg, 'player_bg_2');
                this._otherPlayerHeads[i].bg.spriteFrame = this._atlasDZNZ.getSpriteFrame('player_bg_2');
                this._otherPlayerHeads[i].textCoin.string = '';
                if (this._otherPlayerHeads[i].nbFlag) {
                    this._otherPlayerHeads[i].nbFlag.active = false;
                }
            }
        }
    }

    initGuide(): void {
        let storeGuideKey = 'cowboy_has_show_guide_2';
        if (pf.localStorage.getItem(storeGuideKey) !== 'true') {
            let panelRecord = this._topBg.node.getChildByName('panelRecord');
            for (let i = 0; i < this._recordNum; i++) {
                this._recordDotsTemp.push(
                    panelRecord.getChildByName(pf.StringUtil.formatC('recordDot%d', i)).getComponent(cc.Sprite)
                );
            }
            if (!this._humanboyGuide) {
                this._humanboyGuide = cc.instantiate(
                    pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_GUIDE)
                );
                this.node.addChild(this._humanboyGuide, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_GUIDE);
            }
            let guideLayer = this._humanboyGuide.getComponent(MiniGameGuideControl);
            guideLayer.setDescString(pf.languageManager.getString('Cowboy_ludan_guide_text'));
            guideLayer.show(
                this._topBg.node,
                () => {
                    let hasShowGuide = 'true';
                    pf.localStorage.setItem(storeGuideKey, hasShowGuide);
                    // cv.cowboyNet.RequestTrend();
                    this._cowboyRoom.queryTrend();
                    this._cowboyChart.active = true;
                    // cv.MessageCenter.send('on_display_page1');
                    this._cowboyChartControl.onPage1();
                    this._playSoundEffect(macros.Audio.PRESS);
                    pf.StringUtil.clearArray(this._recordDotsTemp);
                },
                true
            );
        }
    }

    initChart() {
        this._cowboyChart = cc.instantiate(this.cowboyChart);
        this._cowboyChart.setAnchorPoint(0.5, 0.5);
        this._cowboyChart.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
        this.node.addChild(this._cowboyChart);
        this._cowboyChart.active = false;
        this._cowboyChartControl = this._cowboyChart.getComponent(CowboyChartControl);
        // cv.cowboyNet.RequestTrend();
        this._cowboyRoom.queryTrend();
    }

    // any reference?
    // onGuideBgClick(event: cc.Event) {
    //     event.stopPropagation();
    // }

    // any reference?
    // onGuideTopBgClick(event: cc.Event) {
    //     let storeGuideKey = 'cowboy_has_show_guide_2';
    //     let hasShowGuide = 'true';
    //     // cv.tools.SaveStringByCCFile(storeGuideKey, hasShowGuide);
    //     pf.localStorage.setItem(storeGuideKey, hasShowGuide);
    //     //this._topBgTemp.node.parent.active = false;
    //     // cv.cowboyNet.RequestTrend();
    //     this._cowboyRoom.queryTrend();
    //     this._playSoundEffect(macros.Audio.PRESS);
    //     //this._topBgTemp.node.removeFromParent();
    //     this._recordDotsTemp = [];
    // }

    openShop(sender: any): void {
        if (pf.app.clientType === pf.client.ClientType.CowboyWeb) return;
        // if (!cc.sys.isBrowser) {
        //     cv.viewAdaptive.isselfchange = true;
        //     cv.viewAdaptive.cowboyroomid = cb.getCowboyRoom().u32RoomId;
        //     this._backToMainScene();
        // } else {
        //     cv.SHOP.RechargeClick();
        // }

        if (this._platform === 'pkw') {
            if (pf.system.isBrowser) {
                cr.commonResourceAgent.commonDialog.showMsg(
                    pf.languageManager.getString('UIOpenNewWindow'),
                    [pf.languageManager.getString('TipsPanel_sure_button')],
                    () => cr.commonResourceAgent.shop?.open()
                );
            } else {
                const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                context.isSelfRecharge = true;
                // this.exitGame();
                this.tryLeaveRoom();
            }
        } else if (this._platform === 'wpk') {
            cr.commonResourceAgent.commonDialog.showMsg(
                pf.languageManager.getString('PokerMaster_dialog_recharge'),
                [
                    pf.languageManager.getString('TipsPanel_sure_button'),
                    pf.languageManager.getString('TipsPanel_cancel_button')
                ],
                () => {
                    const context = pf.app.getGameContext<pf.services.MiniGameContext>();
                    context.isSelfRecharge = true;
                    context.exitCallback(pf.client.ExitType.Standard);
                },
                () => {
                    // do nothing here
                }
            );
        }
    }

    initButtonEvents(): void {
        // 菜单按钮
        this._btnMenu = this.node.getChildByName('btnMenu').getComponent(cc.Button);
        this._btnMenu.node.on('click', (event: cc.Event): void => {
            // cv.AudioMgr.playButtonSound('button_click');
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);

            if (!this._humanboyMenu) {
                // this._humanboyMenu = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_MENU));
                if (this._platform === 'pkw') {
                    this._humanboyMenu = cc.instantiate(
                        pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_MENU)
                    );
                } else if (this._platform === 'wpk') {
                    this._humanboyMenu = cc.instantiate(
                        pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_MENU_WITHOUT_EXCHANGE)
                    );
                }
                let menuLayer = this._humanboyMenu.getComponent(MiniGameMenuControl);
                this.node.addChild(this._humanboyMenu, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL);

                // 菜单 - 兑换
                menuLayer.getBtnExchange().node.on('click', (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    if (this._walletService.getWallet().usdt <= 0) {
                        cr.commonResourceAgent.toastMessage.showMsg(
                            pf.languageManager.getString('USDTView_ex_coin_error_0_usdt')
                        );
                        return;
                    }

                    if (!this._cowboyExchange) {
                        // this._cowboyExchange = cc
                        //     .instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_EXCHANGE))
                        //     .getComponent(MiniGameExchangeControl);
                        // this.node.addChild(
                        //     this._cowboyExchange.node,
                        //     COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                        // );
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.MINI_GAME_EXCHANGE)
                            .then((asset: cc.Prefab) => {
                                this._cowboyExchange = cc.instantiate(asset).getComponent(MiniGameExchangeControl);
                                this.node.addChild(
                                    this._cowboyExchange.node,
                                    COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                                );
                            });
                    } else {
                        this._cowboyExchange.openView();
                    }
                });

                // 菜单 - 规则
                menuLayer.getBtnRule().node.on('click', (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    if (this._cowboyRule === null) {
                        // this._cowboyRule = cc.instantiate(
                        //     pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_RULE)
                        // );
                        // this._cowboyRule.setAnchorPoint(cc.v2(0.5, 0.5));
                        // this._cowboyRule.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                        // this.node.addChild(this._cowboyRule);
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.MINI_GAME_RULE)
                            .then((asset: cc.Prefab) => {
                                this._cowboyRule = cc.instantiate(asset);
                                this._cowboyRule.setAnchorPoint(cc.v2(0.5, 0.5));
                                this._cowboyRule.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                                this.node.addChild(this._cowboyRule);
                                this._cowboyRule.getComponent(MiniGameRuleControl).openView('');
                            });
                    } else {
                        this._cowboyRule.getComponent(MiniGameRuleControl).openView('');
                    }
                });

                // 菜单 - 音效设置
                menuLayer.getBtnSoundSetting().node.on('click', (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);
                    if (this._cowboySetting === null) {
                        // this._cowboySetting = cc.instantiate(
                        //     pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_AUDIO_SETTING)
                        // );
                        // this._cowboySetting.setAnchorPoint(cc.v2(0.5, 0.5));
                        // this._cowboySetting.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                        // this.node.addChild(this._cowboySetting);
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.MINI_GAME_AUDIO_SETTING)
                            .then((asset: cc.Prefab) => {
                                this._cowboySetting = cc.instantiate(asset);
                                this._cowboySetting.setAnchorPoint(cc.v2(0.5, 0.5));
                                this._cowboySetting.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                                this.node.addChild(this._cowboySetting);
                            });
                    } else {
                        this._cowboySetting.getComponent(MiniGameAudioSettingControl).initSwitch();
                        this._cowboySetting.active = true;
                    }
                });

                // 菜单 - 高级设置
                menuLayer.getBtnAdvancedSetting().node.on('click', (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    if (!this._humanboyAdvancedSetting) {
                        // this._humanboyAdvancedSetting = cc.instantiate(
                        //     pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_ADVANCED_SETTING)
                        // );
                        // this.node.addChild(
                        //     this._humanboyAdvancedSetting,
                        //     COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                        // );
                        pf.addressableAssetManager
                            .loadAsset(macros.Dynamic_Assets.MINI_GAME_ADVANCED_SETTING)
                            .then((asset: cc.Prefab) => {
                                this._humanboyAdvancedSetting = cc.instantiate(asset);
                                this.node.addChild(
                                    this._humanboyAdvancedSetting,
                                    COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL
                                );
                                this._humanboyAdvancedSetting.getComponent(MiniGameAdvancedSettingControl).show();
                            });
                    } else {
                        this._humanboyAdvancedSetting.getComponent(MiniGameAdvancedSettingControl).show();
                    }
                });

                // 菜单 - 退出
                menuLayer.getBtnExit().node.on('click', (event: cc.Event): void => {
                    this._playSoundEffect(macros.Audio.PRESS);
                    menuLayer.hide(false);

                    // let iUsedAutoBetCount = this._cowboyRoom.betSettings.usedAutoBetCount;
                    let iSelectAutoBetCount = this._cowboyRoom.betSettings.selectAutoBetCount;
                    if (iSelectAutoBetCount > 0) {
                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_DIALOG)
                        );
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST);

                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const stringContent = pf.StringUtil.formatC(
                            pf.languageManager.getString('Cowboy_auto_bet_exit_tips'),
                            this._cowboyRoom.betSettings.usedAutoBetCount,
                            this._cowboyRoom.betSettings.selectAutoBetCount
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_exit_game');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_resume_game');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            // cv.cowboyNet.RequestLeaveRoom();
                            // this.exitGame();
                            this.tryLeaveRoom();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            miniGameDialog?.close();
                        };
                        const stringCenter = pf.languageManager.getString('MiniGame_AddAutoBet_Text');
                        const cbCenterBtn = (dialog: MiniGameDialog) => {
                            this.showAutoAddBetList(dialog);
                        };
                        const _onUpdateContent = (dialog: IMiniGameDialog) => {
                            if (legacyDialog) {
                                legacyDialog.txt_content.string = pf.StringUtil.calculateAutoWrapString(
                                    legacyDialog.txt_content.node,
                                    pf.StringUtil.formatC(
                                        pf.languageManager.getString('Cowboy_auto_bet_exit_tips'),
                                        this._cowboyRoom.betSettings.usedAutoBetCount,
                                        this._cowboyRoom.betSettings.selectAutoBetCount
                                    )
                                );
                            }
                            if (this._cowboyRoom.betSettings.reachLimitBet) {
                                miniGameDialog?.blockCenterButton();
                            }
                        };

                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: this._cowboyRoom.betSettings.reachLimitBet,
                            legacyDialog,
                            isShowBtnCenter: true,
                            stringCenterButton: stringCenter,
                            cbCenterBtn,
                            onUpdateContent: _onUpdateContent
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                    } else {
                        if (this._cowboyExit === null) {
                            // this._cowboyExit = cc.instantiate(
                            //     pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_EXIT)
                            // );
                            // this._cowboyExit.setAnchorPoint(cc.v2(0.5, 0.5));
                            // this._cowboyExit.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                            // this.node.addChild(this._cowboyExit);
                            pf.addressableAssetManager
                                .loadAsset(macros.Dynamic_Assets.MINI_GAME_EXIT)
                                .then((asset: cc.Prefab) => {
                                    this._cowboyExit = cc.instantiate(asset);
                                    this._cowboyExit.setAnchorPoint(cc.v2(0.5, 0.5));
                                    this._cowboyExit.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_MENU_PANEL;
                                    this.node.addChild(this._cowboyExit);
                                });
                        } else {
                            this._cowboyExit.active = true;
                        }
                        this._playSoundEffect(macros.Audio.PRESS);
                    }
                });
            }

            this._humanboyMenu.getComponent(MiniGameMenuControl).show(true);
            this._humanboyMenu
                .getComponent(MiniGameMenuControl)
                .setMenuPosition(cc.v2(this._btnMenu.node.x, this._btnMenu.node.y - this._btnMenu.node.height / 2));
        });

        // 玩家列表
        this._btnPlayerList = this._bottomPanel.getChildByName('btnPlayerList').getComponent(cc.Button);
        this._btnPlayerList.node.on('click', (event: cc.Event): void => {
            this._playSoundEffect(macros.Audio.PRESS);
            // cv.cowboyNet.RequestPlayerList();
            this._cowboyRoom.getPlayerList().then((resp) => {
                this.OnPlayerListUpdate(resp.players, resp.playerNum);
            });
        });

        // 商店
        this.self_panel = this._bottomPanel.getChildByName('self_panel');
        let btn_shop_valid = this.self_panel.getChildByName('btn_shop_valid');
        btn_shop_valid.on(cc.Node.EventType.TOUCH_END, (event: cc.Event): void => {
            if (pf.app.clientType === pf.client.ClientType.CowboyWeb) {
                // document.location.href = "ccjs://recharge";
            } else {
                this.openShop(null);
            }
        });
    }

    onGoldViewShop() {
        if (pf.app.clientType === pf.client.ClientType.CowboyWeb) {
            // document.location.href = "ccjs://recharge";
        } else {
            this.openShop(null);
        }
    }

    initBetButtons(): void {
        this._betButtons = [];
        this._betButtonTexts = [];
        this._betButtonMasks = [];

        for (let betBtnIdx = 0; betBtnIdx < this._betButtonNum; betBtnIdx++) {
            let btnBet = this._panel_betbtn.getChildByName('btn_bet_' + betBtnIdx).getComponent(cc.Button);
            // std.string betBtnPng = betBtnIdx <= 2 ? "bet_coin_normal" : "bet_block_normal";
            // btnBet.loadTextures(betBtnPng, betBtnPng, "", cocos2d.ui.Widget.TextureResType.PLIST);
            this._betButtons.push(btnBet);
            this._betButtonTexts.push(btnBet.node.getChildByName('textBet'));
            this._betButtonMasks.push(btnBet.node.getChildByName('imgMask').getComponent(cc.Sprite));

            btnBet.node.on(
                'click',
                (event: cc.Event): void => {
                    console.log('GameCowboyScene btnBet %d clicked', betBtnIdx);
                    this.betButtonSelected(betBtnIdx);
                    this._playSoundEffect(macros.Audio.PRESS);
                },
                this
            );
        }

        // 初始化高级续投面板
        if (!this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.HUMANBOY_ADVANCED_AUTO)
            );
            this.node.addChild(this._humanboyAdvancedAuto, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_SELECT);
        }

        // 续投按钮
        this._btnBetAuto = this._panel_betbtn.getChildByName('btn_bet_auto').getComponent(cc.Button);
        this._btnBetAuto.node.on('click', (event: cc.Event) => {
            this._playSoundEffect(macros.Audio.PRESS);

            switch (this._eAutoBtnStyle) {
                // 常规续投点击
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                    // cv.cowboyNet.RequestAutoBet();
                    this._cowboyRoom.autoBet().then(() => {
                        this.OnAutoBetSucc();
                    });
                    break;

                // 高级续投已激活(再次点击 弹出高级续投选项面板)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                    {
                        // if (cb.getCowboyRoom().curState == RoundState.BET) {
                        const miniGameAdvanceAuto =
                            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl);
                        const advanceAuto = new ConcreteAdvancedAuto(miniGameAdvanceAuto);
                        advanceAuto.adaptSelectPanelPos(this._btnBetAuto.node);
                        advanceAuto.showSelectPanel(true);
                        // }
                    }
                    break;

                // 高级续投中(再次点击取消)
                case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                    {
                        // let iUsedAutoBetCount = this._cowboyRoom.betSettings.usedAutoBetCount;
                        // let iSelectAutoBetCount = this._cowboyRoom.betSettings.selectAutoBetCount;
                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_DIALOG)
                        );
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST);

                        const legacyDialog = dialogNode.getComponent(HumanboyDialogControl);
                        const stringContent = pf.StringUtil.formatC(
                            pf.languageManager.getString('Cowboy_auto_bet_stop_tips'),
                            this._cowboyRoom.betSettings.usedAutoBetCount,
                            this._cowboyRoom.betSettings.selectAutoBetCount
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_stop_auto_bet');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_resume_auto_bet');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            // cv.cowboyNet.ReqCancelAdvanceAutoBet();
                            this._cowboyRoom.cancelAdavnceAutoBet();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            miniGameDialog?.close();
                        };
                        const stringCenter = pf.languageManager.getString('MiniGame_AddAutoBet_Text');
                        const cbCenterBtn = (dialog: MiniGameDialog) => {
                            this.showAutoAddBetList(dialog);
                        };
                        const _onUpdateContent = (dialog: IMiniGameDialog) => {
                            if (legacyDialog) {
                                legacyDialog.txt_content.string = pf.StringUtil.calculateAutoWrapString(
                                    legacyDialog.txt_content.node,
                                    pf.StringUtil.formatC(
                                        pf.languageManager.getString('Cowboy_auto_bet_stop_tips'),
                                        this._cowboyRoom.betSettings.usedAutoBetCount,
                                        this._cowboyRoom.betSettings.selectAutoBetCount
                                    )
                                );
                            }
                            if (this._cowboyRoom.betSettings.reachLimitBet) {
                                miniGameDialog?.blockCenterButton();
                            }
                        };
                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: this._cowboyRoom.betSettings.reachLimitBet,
                            legacyDialog,
                            isShowBtnCenter: true,
                            stringCenterButton: stringCenter,
                            cbCenterBtn,
                            onUpdateContent: _onUpdateContent
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                    }
                    break;

                default:
                    break;
            }
        });

        // 清屏按钮
        this._btnBetClean = this._panel_betbtn.getChildByName('btn_bet_clean').getComponent(cc.Button);
        // cv.resMgr.loadButtonTextureByPlist(
        //     this.language_PLIST,
        //     this._btnBetClean.node,
        //     'clean_screen_normal',
        //     'clean_screen_normal',
        //     'clean_screen_normal',
        //     'clean_screen_gray'
        // );
        this._btnBetClean.normalSprite = this.language_PLIST.getSpriteFrame('clean_screen_normal');
        this._btnBetClean.pressedSprite = this.language_PLIST.getSpriteFrame('clean_screen_normal');
        this._btnBetClean.hoverSprite = this.language_PLIST.getSpriteFrame('clean_screen_normal');
        this._btnBetClean.disabledSprite = this.language_PLIST.getSpriteFrame('clean_screen_gray');
        this._btnBetClean.node.on('click', (event: cc.Event) => {
            // cv.AudioMgr.playButtonSound('button_click');
            pf.audioManager.playSoundEffect(macros.Audio.BUTTON_CLICK);
            this.clearAllBetAreaCoins();
        });
    }

    protected advanceAutoAddBet: cc.Node = null;
    protected showAutoAddBetList(dialog: MiniGameDialog) {
        if (!this.advanceAutoAddBet) {
            this.advanceAutoAddBet = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_ADVANCED_AUTO)
            );
            this.node.addChild(this.advanceAutoAddBet, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ADVANCE_AUTO_ADD_SELECT);
        }
        const miniGameAdvanceAuto = this.advanceAutoAddBet.getComponent(MiniGameAdvancedAuto);
        const advanceAuto = new ConcreteAdvancedAuto(miniGameAdvanceAuto);
        advanceAuto.adaptSelectPanelPos(dialog.btn_center.node);
        advanceAuto.showSelectPanel(true);
        advanceAuto.setCountUpdateCallback(() => {
            dialog.updateCenterButton();
        });
    }

    /**
     * 初始化红包等相关按钮入口
     */
    protected initRedPackage(): void {
        // 红包节按钮
        this._btn_redpacket_festival = this._panel_betbtn.getChildByName('btn_redpacket_festival');
        this._btn_redpacket_festival.getComponent(cc.Sprite).spriteFrame = null;
        this._btn_redpacket_festival.active = false;

        // 红包节按钮提示层
        this._btn_redpacket_festival_layer = cc.instantiate(this._btn_redpacket_festival);
        this.node.addChild(this._btn_redpacket_festival_layer, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_RED_PACKAGE);

        let wpos: cc.Vec2 = cc.Vec2.ZERO;
        this._btn_redpacket_festival.convertToWorldSpaceAR(cc.Vec2.ZERO, wpos);
        this._btn_redpacket_festival_layer.setPosition(
            this._btn_redpacket_festival_layer.parent.convertToNodeSpaceAR(wpos)
        );

        // 初始执行一次
        this.showLuckButton(pf.client.RedPacketLotteryMode.Classical);
    }

    clearAllBetAreaCoins(): void {
        let betAreas_len = pf.StringUtil.getArrayLength(this._betAreas);
        for (let i = 0; i < betAreas_len; i++) {
            this.hideAreaCoin(i, false);
        }
    }
    resetAllBetButtons(): void {
        let len = this._betButtons.length;
        for (let i = 0; i < len; i++) {
            this._betButtons[i].node.setScale(this.fBetBtnSrcScaleRate, this.fBetBtnSrcScaleRate);
            this._betButtonTexts[i].active = true;
            this._betButtonMasks[i].node.active = false;
            this._betButtons[i].enabled = true;
        }
        this._curBetButtonIdx = -1;
    }

    betButtonSelected(betBtnIdx: number, ignoreCheckCoin?: boolean): void {
        // 未完
        const checkedIsIgnoreCheckCoin = ignoreCheckCoin === true ? true : false;
        this.resetAllBetButtons();
        if (!checkedIsIgnoreCheckCoin) {
            this._updateBetButtonState();
        }

        if (betBtnIdx < 0 || betBtnIdx > this._betButtonNum - 1) {
            return;
        }

        this._curBetButtonIdx = betBtnIdx;
        this._betButtons[betBtnIdx].node.setScale(this._fBetBtnTarScaleRate, this._fBetBtnTarScaleRate);
        // let clickedPng = this._curBetButtonIdx <= 2 ? "bet_coin_clicked" : "bet_block_clicked";
        // cb.loadButtonTextureByPlist(this._atlasDZNZ, this._betButtons[betBtnIdx].node, clickedPng, clickedPng, clickedPng, clickedPng);
        // this._betButtonTexts[betBtnIdx].node.scale = (1.0);
        // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._betButtonMasks[betBtnIdx], this._curBetButtonIdx <= 2 ? "bet_coin_disable_mask_big" : "bet_block_disable_mask_big");
    }

    updateBetButtonText(): void {
        return;
        // let amountlevel = cb.getCowboyRoom().pkRoomParam.amountLevel;
        // for (let i = 0; i < amountlevel.length; i++) {
        //     if (i < this._betButtonNum) {
        //         //this._betButtonTexts[i].string = (this.getShortOwnCoinString(amountlevel[i]));
        //         this._betButtonTexts[i].string = (cv.StringTools.numberToString(cv.StringTools.clientGoldByServer(amountlevel[i])));
        //     }
        //     else {
        //         console.log("error!! updateBetButtonText amountlevel must be %d, size: %d", this._betButtonNum, amountlevel.length);
        //     }
        // }
    }

    _updateBetOddsDetail(): void {
        let details = this._cowboyRoom.roomParams.oddsDetails;
        for (const detail of details) {
            let areaIdx = this.getAreaIdxByBetOption(detail.option);
            if (this._textBetAreaOdds[areaIdx]) {
                this._textBetAreaOdds[areaIdx].string =
                    pf.StringUtil.clientGoldByServer(detail.odds) + pf.languageManager.getString('Cowboy_odds_text');
            }
        }
    }

    _updateBetButtonState(): void {
        // 检测下注按钮禁用与否
        let vBetCoinOption = this._cowboyRoom.betSettings.betCoinOptions; // 房间下注级别
        let curCoin = this._cowboyRoom.selfPlayer.curCoin; // 当前自身携带金币
        let vBetCoinOption_len = pf.StringUtil.getArrayLength(vBetCoinOption);
        for (let i = 0; i < vBetCoinOption_len; ++i) {
            // 钱是否够按钮上的金额
            let llAmountLevel = pf.StringUtil.clientGoldByServer(vBetCoinOption[i]);
            if (curCoin >= vBetCoinOption[i]) {
                this._betButtons[i].interactable = true;
                this._betButtons[i].enabled = true;
                this.setCoinText(this._betButtonTexts[i], llAmountLevel, true);
                // cv.resMgr.getAsyncFontByName("zh_CN/game/cowboy/fnt/bet_btn_num_yellow", (font: cc.Font): void => {
                //     this._betButtonTexts[i].font = font;
                // });
            } else {
                this._betButtons[i].interactable = false;
                this._betButtons[i].enabled = false;
                this.setCoinText(this._betButtonTexts[i], llAmountLevel, false);
                // cv.resMgr.getAsyncFontByName("zh_CN/game/cowboy/fnt/bet_btn_num_gray", (font: cc.Font): void => {
                //     this._betButtonTexts[i].font = font;
                // });                //_resetBetButton(i, false);
            }
        }

        // 检测下注按钮可触摸与否
        let bEffective: boolean =
            this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0;
        let betButtons_len: number = pf.StringUtil.getArrayLength(this._betButtons);
        for (let i = 0; i < betButtons_len; ++i) {
            this._betButtonMasks[i].node.active = !bEffective;
            this._betButtonMasks[i].enabled = true;
            this._betButtons[i].enabled = bEffective;
        }

        // 更新续投按钮状态
        this._updateAutoBetBtnStatus();

        // 更新清屏按钮状态
        this._updateCleanBtnStatus();
    }

    updatBetButtonByCurCoin(): void {
        return;
        // 可以下注的时候才判断
        // if (cb.getCowboyRoom().curState == cb.Enum.RoundState.BET && this._leftTime > 0) {
        //     let amountlevel = cb.getCowboyRoom().pkRoomParam.amountLevel;
        //     let curCoin = cb.getCowboyRoom().selfPlayer.curCoin;
        //     let len = amountlevel.length;

        //     let disablePng: string = "";
        //     let curStatePng: string = "";

        //     for (let i = 0; i < len; i++) {
        //         if (i < this._betButtonNum) {
        //             // 钱不够按钮上的金额
        //             if (curCoin < amountlevel[i]) {

        //                 if (i == this._curBetButtonIdx) {
        //                     disablePng = i <= 2 ? "bet_coin_disabled_big" : "bet_block_disabled_big";
        //                 }
        //                 else {
        //                     disablePng = i <= 2 ? "bet_coin_disabled" : "bet_block_disabled";
        //                 }

        //                 cb.loadButtonTextureByPlist(this._atlasDZNZ, this._betButtons[i].node, disablePng, disablePng, disablePng, disablePng);
        //                 this._betButtons[i].enabled = (false);
        //                 this._betButtons[i].interactable = false;
        //                 this._betButtonTexts[i].node.color = (cc.Color.BLACK);
        //                 this._betButtonTexts[i].node.opacity = (102);
        //             }
        //             else {
        //                 if (i == this._curBetButtonIdx) {
        //                     curStatePng = i <= 2 ? "bet_coin_clicked" : "bet_block_clicked";
        //                 }
        //                 else {
        //                     curStatePng = i <= 2 ? "bet_coin_normal" : "bet_block_normal";
        //                 }

        //                 cb.loadButtonTextureByPlist(this._atlasDZNZ, this._betButtons[i].node, curStatePng, curStatePng, curStatePng, curStatePng);
        //                 this._betButtons[i].enabled = (true);
        //                 this._betButtons[i].interactable = true;
        //                 this._betButtonTexts[i].node.color = (cc.Color.WHITE);
        //                 this._betButtonTexts[i].node.opacity = (255);
        //             }
        //         }
        //         else {
        //             console.log("error!! this.updatBetButtonByCurCoin amountlevel must be: %d, size: %d", this._betButtonNum, amountlevel.length);
        //         }
        //     }
        // }
    }

    enableAutoBetButton(enabled: boolean): void {
        this._btnBetAuto.enabled = enabled;
        this._btnBetAuto.interactable = enabled;
    }

    _updateAutoBetBtnStatus(): void {
        switch (this._eAutoBtnStyle) {
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL: {
                if (this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
                    // 当前一局下过注
                    if (this._cowboyRoom.roundInfo.hasBetInCurRound) {
                        this.enableAutoBetButton(false);
                    } else {
                        this.enableAutoBetButton(this._cowboyRoom.betSettings.canAutoBet);
                    }
                } else {
                    this.enableAutoBetButton(false);
                }
                break;
            }
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE: {
                // 当前一局下过注
                if (this._cowboyRoom.roundInfo.hasBetInCurRound) {
                    this.enableAutoBetButton(true);
                } else {
                    this.enableAutoBetButton(this._cowboyRoom.betSettings.canAutoBet);
                }
                break;
            }
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING: {
                this.enableAutoBetButton(true);
                break;
            }
            default:
                break;
        }
    }

    _updateCleanBtnStatus(): void {
        let bEnable = false;
        if (this._cowboyRoom.gameState.roundState === network.RoundState.BET && this._leftTime > 0) {
            bEnable = true;
        }

        this._btnBetClean.interactable = bEnable;
    }

    _updateBetAmountLevel(): void {
        let vBetCoinOption = this._cowboyRoom.betSettings.betCoinOptions;
        let vBetCoinOption_len = pf.StringUtil.getArrayLength(vBetCoinOption);
        for (let i = 0; i < vBetCoinOption_len; ++i) {
            if (i < this._betButtonNum) {
                let llAmountLevel = pf.StringUtil.clientGoldByServer(vBetCoinOption[i]);
                if (llAmountLevel < this._cowboyRoom.llCoinUICritical) {
                    // cb.loadButtonTextureByPlist(
                    //     this._atlasDZNZ,
                    //     this._betButtons[i].node,
                    //     'bet_coin_clicked',
                    //     'bet_coin_clicked',
                    //     'bet_coin_clicked',
                    //     'bet_coin_disabled_big'
                    // );
                    this._betButtons[i].normalSprite = this._atlasDZNZ.getSpriteFrame('bet_coin_clicked');
                    this._betButtons[i].pressedSprite = this._atlasDZNZ.getSpriteFrame('bet_coin_clicked');
                    this._betButtons[i].hoverSprite = this._atlasDZNZ.getSpriteFrame('bet_coin_clicked');
                    this._betButtons[i].disabledSprite = this._atlasDZNZ.getSpriteFrame('bet_coin_disabled_big');
                    // this._betButtons[i].loadTextureNormal("bet_coin_clicked.png", TextureResType.PLIST);
                    // this._betButtons[i].loadTexturePressed("bet_coin_clicked.png", TextureResType.PLIST);
                    // this._betButtons[i].loadTextureDisabled("bet_coin_disabled_big.png", TextureResType.PLIST);
                    // cb.loadSpriteTextureByPlist(
                    //     this._atlasDZNZ,
                    //     this._betButtonMasks[i],
                    //     'bet_coin_disable_mask_big'
                    // );
                    this._betButtonMasks[i].spriteFrame = this._atlasDZNZ.getSpriteFrame('bet_coin_disable_mask_big');
                    this._betButtonMasks[i].node.scale = 1.05;
                    // this._betButtonMasks[i].loadTexture("bet_coin_disable_mask_big.png", TextureResType.PLIST);
                } else {
                    // cb.loadButtonTextureByPlist(
                    //     this._atlasDZNZ,
                    //     this._betButtons[i].node,
                    //     'bet_block_clicked',
                    //     'bet_block_clicked',
                    //     'bet_block_clicked',
                    //     'bet_block_disabled_big'
                    // );
                    this._betButtons[i].normalSprite = this._atlasDZNZ.getSpriteFrame('bet_block_clicked');
                    this._betButtons[i].pressedSprite = this._atlasDZNZ.getSpriteFrame('bet_block_clicked');
                    this._betButtons[i].hoverSprite = this._atlasDZNZ.getSpriteFrame('bet_block_clicked');
                    this._betButtons[i].disabledSprite = this._atlasDZNZ.getSpriteFrame('bet_block_disabled_big');
                    // _betButtons[i].loadTextureNormal("bet_block_clicked.png", TextureResType.PLIST);
                    // _betButtons[i].loadTexturePressed("bet_block_clicked.png", TextureResType.PLIST);
                    // _betButtons[i].loadTextureDisabled("bet_block_disabled_big.png", TextureResType.PLIST);
                    // cb.loadSpriteTextureByPlist(
                    //     this._atlasDZNZ,
                    //     this._betButtonMasks[i],
                    //     'bet_block_disable_mask_big'
                    // );
                    this._betButtonMasks[i].spriteFrame = this._atlasDZNZ.getSpriteFrame('bet_block_disable_mask_big');
                    this._betButtonMasks[i].node.scale = 1.0;
                    // _betButtonMasks[i].loadTexture("bet_block_disable_mask_big.png", TextureResType.PLIST);
                }

                // _betButtons[i].ignoreContentAdaptWithSize(true);
                // _betButtonMasks[i].ignoreContentAdaptWithSize(true);
                // this._betButtonMasks[i].node.setPosition(this._betButtonMasks[i].node.getParent().width / 2, this._betButtonMasks[i].node.getParent().height / 2);
                this.setCoinText(this._betButtonTexts[i], llAmountLevel, true);
                // this._betButtonTexts[i].string = (cv.StringTools.numberToString(llAmountLevel));
                // this._betButtonTexts[i].node.setPosition(this._betButtonTexts[i].node.getParent().width / 2, this._betButtonTexts[i].node.getParent().height / 2);
            } else {
                console.log(
                    'error!! HumanboyMainView._updateBetAmountLevel vBetCoinOption must be %d, size: %d',
                    this._betButtonNum,
                    vBetCoinOption_len
                );
            }
        }

        switch (this._cowboyRoom.betSettings.autoBetLevel) {
            case pf.client.session.AutoBetLevel.Level_Normal:
                this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL);
                break;

            case pf.client.session.AutoBetLevel.Level_Advance:
                if (this._cowboyRoom.betSettings.selectAutoBetCount > 0) {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING);
                } else {
                    this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE);
                }
                break;

            default:
                break;
        }

        this.adaptiveBetBtnPanel();
    }

    protected _setAutoBetBtnStytle(eAutoBtnStyle: MiniGameCommonDef.eGameboyAutoBtnStyle): void {
        // 隐藏高级续投子面板
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).hideAdvanceAutoTips();
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).hideAdvanceAutoCount();
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).hideSelectPanel(false);
        }

        this._eAutoBtnStyle = eAutoBtnStyle;
        switch (this._eAutoBtnStyle) {
            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NONE:
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_NORMAL:
                // cb.loadButtonTextureByPlist(
                //     this.language_PLIST,
                //     this._btnBetAuto.node,
                //     'autobet_normal',
                //     'autobet_normal',
                //     'autobet_normal',
                //     'autobet_gray'
                // );
                this._btnBetAuto.normalSprite = this.language_PLIST.getSpriteFrame('autobet_normal');
                this._btnBetAuto.pressedSprite = this.language_PLIST.getSpriteFrame('autobet_normal');
                this._btnBetAuto.hoverSprite = this.language_PLIST.getSpriteFrame('autobet_normal');
                this._btnBetAuto.disabledSprite = this.language_PLIST.getSpriteFrame('autobet_gray');
                // _btnBetAuto.loadTextureNormal("autobet_normal.png", TextureResType.PLIST);
                // _btnBetAuto.loadTexturePressed("autobet_normal.png", TextureResType.PLIST);
                // _btnBetAuto.loadTextureDisabled("autobet_gray.png", TextureResType.PLIST);
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE:
                // cb.loadButtonTextureByPlist(
                //     this.language_PLIST,
                //     this._btnBetAuto.node,
                //     'autobet_block_normal',
                //     'autobet_block_normal',
                //     'autobet_block_normal',
                //     'autobet_block_gray'
                // );
                this._btnBetAuto.normalSprite = this.language_PLIST.getSpriteFrame('autobet_block_normal');
                this._btnBetAuto.pressedSprite = this.language_PLIST.getSpriteFrame('autobet_block_normal');
                this._btnBetAuto.hoverSprite = this.language_PLIST.getSpriteFrame('autobet_block_normal');
                this._btnBetAuto.disabledSprite = this.language_PLIST.getSpriteFrame('autobet_block_gray');
                // _btnBetAuto.loadTextureNormal("autobet_block_normal.png", TextureResType.PLIST);
                // _btnBetAuto.loadTexturePressed("autobet_block_normal.png", TextureResType.PLIST);
                // _btnBetAuto.loadTextureDisabled("autobet_block_gray.png", TextureResType.PLIST);
                break;

            case MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING:
                // cb.loadButtonTextureByPlist(
                //     this.language_PLIST,
                //     this._btnBetAuto.node,
                //     'autobet_block_using',
                //     'autobet_block_using',
                //     'autobet_block_using',
                //     'autobet_block_gray'
                // );
                this._btnBetAuto.normalSprite = this.language_PLIST.getSpriteFrame('autobet_block_using');
                this._btnBetAuto.pressedSprite = this.language_PLIST.getSpriteFrame('autobet_block_using');
                this._btnBetAuto.hoverSprite = this.language_PLIST.getSpriteFrame('autobet_block_using');
                this._btnBetAuto.disabledSprite = this.language_PLIST.getSpriteFrame('autobet_block_gray');
                // _btnBetAuto.loadTextureNormal("autobet_block_using.png", TextureResType.PLIST);
                // _btnBetAuto.loadTexturePressed("autobet_block_using.png", TextureResType.PLIST);
                // _btnBetAuto.loadTextureDisabled("autobet_block_gray.png", TextureResType.PLIST);

                if (this._humanboyAdvancedAuto) {
                    this._humanboyAdvancedAuto
                        .getComponent(HumanboyAdvancedAutoControl)
                        .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
                    this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).showAdvanceAutoCount();
                }
                break;

            default:
                break;
        }

        let imgBetAuto: cc.Sprite = this._btnBetAuto.getComponent(cc.Sprite);
        imgBetAuto.type = cc.Sprite.Type.SIMPLE;
        imgBetAuto.sizeMode = cc.Sprite.SizeMode.RAW;
    }

    _getAutoBetBtnStytle(): MiniGameCommonDef.eGameboyAutoBtnStyle {
        return this._eAutoBtnStyle;
    }

    _checkAdvanceAutoReq(): void {
        if (
            this._cowboyRoom.gameState.roundState === network.RoundState.BET &&
            this._getAutoBetBtnStytle() === MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING
        ) {
            if (this._humanboyAdvancedAuto) {
                this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).hideAdvanceAutoTips();
            }

            if (this._cowboyRoom.betSettings.usedAutoBetCount < this._cowboyRoom.betSettings.selectAutoBetCount) {
                // cv.cowboyNet.reqAdvanceAutoBet();
                this._cowboyRoom
                    .advanceAutoBet()
                    .then(() => {
                        this._onMsgAdvanceAutobet(network.ErrorCode.OK);
                    })
                    .catch((err: pf.ServerError) => {
                        this._onMsgAdvanceAutobet(err.errorCode);
                    });
            }
        }
    }

    clearBetArea(areaIdx: number): void {
        if (pf.StringUtil.getArrayLength(this._sprBetAreaWinFlags) > areaIdx) {
            this._sprBetAreaWinFlags[areaIdx].node.active = false;
        }

        this.hideWinFlagAnim(areaIdx);
        this._betCoinContents[areaIdx].destroyAllChildren();
        this._betCoinContents[areaIdx].removeAllChildren(true);
        // console.log('clearBetArea-.清理areaIndex = ' + areaIdx);
        this.hideAreaCoin(areaIdx, false);
        this.updateSelfBetAreaCoin(areaIdx, 0);
        this.updateTotalBetAreaCoin(areaIdx, 0);
    }

    clearAllBetArea(): void {
        let len = this._betAreas.length;
        for (let i = 0; i < len; i++) {
            this.clearBetArea(i);
        }
    }

    getAreaIdxByBetOption(betOption: number): number {
        return this._mapBetOptionArea.get(betOption);
    }

    getBetOptionByAreaIdx(areaIdx: number): number {
        let betOption = -1;
        this._mapBetOptionArea.forEach((value: number, key: number) => {
            if (value === areaIdx) {
                betOption = key;
            }
        });

        if (betOption < 0) {
            console.log('error!! getOptionByAreaIdx -1');
        }
        return betOption;
    }

    getCurBetLevel(): number {
        if (this._curBetButtonIdx < 0) {
            return 0;
        }

        let amountlevel = this._cowboyRoom.betSettings.betCoinOptions;
        return amountlevel[this._curBetButtonIdx];
    }

    initHistoryDots(): void {
        this._topBg = this.node.getChildByName('top_bg').getComponent(cc.Sprite);
        this._topBg.node.active = true;
        this._btnZouShi = this._topBg.node.getChildByName('btnZouShi').getComponent(cc.Button);
        this._btnZouShi.enabled = false;
        this._topBg.node.on(
            cc.Node.EventType.TOUCH_END,
            (event: cc.Event): void => {
                // cv.cowboyNet.RequestTrend();
                this._cowboyRoom.queryTrend();
                this._cowboyChart.active = true;
                // cv.MessageCenter.send('on_display_page1');
                this._cowboyChartControl.onPage1();
                this._playSoundEffect(macros.Audio.PRESS);
            },
            this
        );
        let panelRecord = this._topBg.node.getChildByName('panelRecord');
        for (let i = 0; i < this._recordNum; i++) {
            this._recordDots.push(
                panelRecord.getChildByName(pf.StringUtil.formatC('recordDot%d', i)).getComponent(cc.Sprite)
            );
            let pos: cc.Vec2 = cc.v2(this._recordDots[i].node.getPosition());
            this._oriRecordDotsPos.push(pos);
        }
        if (panelRecord.getComponent(cc.Mask).enabled) {
            panelRecord.getComponent(cc.Mask).enabled = false;
        }

        // 走势
        this._topBg.node.on(
            'click',
            (event: cc.Event): void => {
                // cv.cowboyNet.RequestTrend();
                this._cowboyRoom.queryTrend();
                this._playSoundEffect(macros.Audio.PRESS);
            },
            this
        );
    }

    updateDotState(): void {
        this.hideHistoryMoveAnim();

        let historySize = this._cowboyRoom.historyResults.length;

        if (historySize === 2) {
            // historySize == 1 ||
            let recordDotIdx = this._recordNum - 1;
            this._recordDots[recordDotIdx].node.active = true;
        }

        if (historySize > 2) {
            for (let i = 0; i < this._recordNum; i++) {
                // 逆序取
                let historyIdx = historySize - i - 1;
                let recordDotIdx = this._recordNum - i - 1;
                this._recordDots[recordDotIdx].node.active = true;

                if (historyIdx <= 0) {
                    this._recordDots[recordDotIdx].node.active = false;
                } else {
                    this._recordDots[recordDotIdx].node.active = true;
                }
            }
        }
    }

    updateHistoryResults(): void {
        this.hideHistoryMoveAnim();

        let historySize = this._cowboyRoom.historyResults.length;
        for (let i = 0; i < this._recordNum; i++) {
            // 逆序取
            let historyIdx = historySize - i - 1;
            let recordDotIdx = this._recordNum - i - 1;
            this._recordDots[recordDotIdx].node.active = true;
            if (this._recordDotsTemp.length > 0) {
                this._recordDotsTemp[recordDotIdx].node.active = true;
            }
            if (historyIdx < 0) {
                // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._recordDots[recordDotIdx], 'record_draw');
                this._recordDots[recordDotIdx].spriteFrame = this._atlasDZNZ.getSpriteFrame('record_draw');
                this._recordDots[recordDotIdx].node.active = false;

                if (this._recordDotsTemp.length > 0) {
                    // cb.loadSpriteTextureByPlist(
                    //     this._atlasDZNZ,
                    //     this._recordDotsTemp[recordDotIdx],
                    //     'record_draw'
                    // );
                    this._recordDotsTemp[recordDotIdx].spriteFrame = this._atlasDZNZ.getSpriteFrame('record_draw');
                    this._recordDotsTemp[recordDotIdx].node.active = false;
                }
            } else {
                let betOption = this._cowboyRoom.historyResults[historyIdx];
                let frameName = 'record_draw';
                if (betOption === network.BetZoneOption.RED_WIN) {
                    frameName = 'record_red';
                } else if (betOption === network.BetZoneOption.BLUE_WIN) {
                    frameName = 'record_blue';
                }
                // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._recordDots[recordDotIdx], frameName);
                this._recordDots[recordDotIdx].spriteFrame = this._atlasDZNZ.getSpriteFrame(frameName);
                this._recordDots[recordDotIdx].node.active = true;

                if (this._recordDotsTemp.length > 0) {
                    // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._recordDotsTemp[recordDotIdx], frameName);
                    this._recordDotsTemp[recordDotIdx].spriteFrame = this._atlasDZNZ.getSpriteFrame(frameName);
                    this._recordDotsTemp[recordDotIdx].node.active = true;
                }
            }
        }
    }

    updateHistoryResultsPrevious(): void {
        let last = this._cowboyRoom.removeCurrentHistoryResult();
        if (last !== -1) {
            this.updateHistoryResults();
            this._cowboyRoom.addCurrentHistoryResult(last);
        }
    }

    showHistoryMoveAnim(): void {
        if (this._cowboyRoom.historyResults.length > 0) {
            // 设置最新胜负标记
            // let betOption = cb.getCowboyRoom().historyResults[cb.getCowboyRoom().historyResults.length - 1];
            // let frameName = "record_draw";
            // if (betOption == cb.Enum.BetZoneOption.RED_WIN) {
            //     frameName = "record_red";
            // }
            // else if (betOption == cb.Enum.BetZoneOption.BLUE_WIN) {
            //     frameName = "record_blue";
            // }
            let len = pf.StringUtil.getArrayLength(this._recordDots);
            // console.log("----. len = " + len)
            // cb.loadSpriteTextureByPlist(this._atlasDZNZ, this._recordDots[len - 1], frameName);

            let panelRecord = this._topBg.node.getChildByName('panelRecord');
            let tempMsk = panelRecord.getComponent(cc.Mask);
            if (!tempMsk) {
                let msk: any = panelRecord.addComponent(cc.Mask);
                // if (msk) {
                //     msk._createGraphics();
                // }
                msk.type = cc.Mask.Type.RECT;
            } else if (!tempMsk.enabled) {
                tempMsk.enabled = true;
            }
            // this._recordDots[len - 1].node.active = true;
            // 位移动画
            let moveOffset = cc.v2(
                this._oriRecordDotsPos[0].x - this._oriRecordDotsPos[1].x,
                this._oriRecordDotsPos[0].y - this._oriRecordDotsPos[1].y
            );

            this.updateDotState();
            let historySize = this._cowboyRoom.historyResults.length;

            if (historySize === 1) {
                this._nodeAnim.runAction(
                    cc.sequence(
                        cc.delayTime(0.3),
                        cc.callFunc(() => {
                            this.updateHistoryResults();
                        })
                    )
                );
            } else {
                for (let i = 0; i < len; i++) {
                    // console.log('----2222222----. len = ' + len + ', i = ' + i);
                    if (this._recordDots[i].node.active === true) {
                        let historySize = this._cowboyRoom.historyResults.length;

                        //  {
                        this._recordDots[i].node.runAction(
                            cc.sequence(
                                cc.moveBy(0.3, moveOffset),
                                cc.callFunc(() => {
                                    let lenX = pf.StringUtil.getArrayLength(this._recordDots);
                                    // if (historySize < len)
                                    // {
                                    //     this._recordDots[i].node.active = false;
                                    // }
                                    // console.log('--------. len = ' + len + ', i = ' + i);
                                    if (i === len - 1) {
                                        this.updateHistoryResults();
                                    }
                                })
                            )
                        );
                        // }
                    }
                }
            }

            // cv.MessageCenter.send('cowboy_start_history_move_anim');
            this._cowboyChartControl.updateResult();

            // let isOpen = cb.getCowboyRoom().isOpen;
            // if (isOpen) {
            //     cv.cowboyNet.RequestTrend();
            // }
        }
    }

    hideHistoryMoveAnim(): void {
        let len = pf.StringUtil.getArrayLength(this._recordDots);
        // this._recordDots[len - 1].node.active = false;

        for (let i = 0; i < len; i++) {
            this._recordDots[i].node.stopAllActions();
            this._recordDots[i].node.setPosition(this._oriRecordDotsPos[i]);
        }

        let panelRecord = this._topBg.node.getChildByName('panelRecord');
        if (panelRecord.getComponent(cc.Mask).enabled) {
            panelRecord.getComponent(cc.Mask).enabled = false;
        }
    }

    OnTrendUpdate(trend: domain.RoomTrend): void {
        if (this._cowboyChartControl !== null && this._cowboyRoom.showTheNewestTrend === false) {
            this._cowboyChartControl.resetblink();
            return;
        }

        this._cowboyChartControl.setData(trend);
    }

    protected OnPlayerListUpdate(gamePlayers: pf.services.GamePlayer[], playerNum: number): void {
        if (this._cowboyList === null) {
            // this._cowboyList = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_PLAYER_LIST));
            // this._cowboyList.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST;
            // this.node.addChild(this._cowboyList);

            // this._cowboyList.getComponent(MiniGamePlayerListControl).setCowboyData(gamePlayers, playerNum);
            // this._cowboyList.getComponent(MiniGamePlayerListControl).displayCell(0);
            pf.addressableAssetManager
                .loadAsset(macros.Dynamic_Assets.MINI_GAME_PLAYER_LIST)
                .then((asset: cc.Prefab) => {
                    this._cowboyList = cc.instantiate(asset);
                    this._cowboyList.zIndex = COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST;
                    this.node.addChild(this._cowboyList);

                    this._cowboyList.getComponent(MiniGamePlayerListControl).setCowboyData(gamePlayers, playerNum);
                    this._cowboyList.getComponent(MiniGamePlayerListControl).displayCell(0);
                });
        } else {
            this._cowboyList.active = true;
            this._cowboyList.getComponent(MiniGamePlayerListControl).setCowboyData(gamePlayers, playerNum);
            this._cowboyList.getComponent(MiniGamePlayerListControl).displayCell(-1);
        }
    }

    OnGameDataSynNotify(): void {
        this._bSwitchTable = false;
        this._cowboyRoom.showTheNewestTrend = true;
        this._vCoinOptimizationDeque.clear();
        this.clearRound(); // 清除场景动态信息

        // 更新场景静态信息
        this.resetLeftTimer();
        this._updateBetAmountLevel();
        this.updatBetButtonByCurCoin();
        this._updateBetButtonState();
        this._updateBetOddsDetail();
        this.updateSelfInfo();
        this.updateOtherPlayersInfo();

        this.updateHistoryResults();
        this.updateAllPlayerWinCount();

        this._getRebateEventStatus();

        // 根据不同的游戏状态恢复游戏场景
        if (this._cowboyRoom.gameState.roundState === network.RoundState.GAME_PENDING) {
            // 房间新建的，准备开局
            // do nothing
        } else if (this._cowboyRoom.gameState.roundState === network.RoundState.NEW_ROUND) {
            // 新的一局
            this.updateCards();
            this._updateAllWayOut();
        } else if (this._cowboyRoom.gameState.roundState === network.RoundState.BET) {
            // 下注
            this.updateCards();
            this._updateAllWayOut();
            this.updateAllBetAreas();
            this.updateBetCoutDown();

            // 下注剩余时间大于4s，显示出战动画
            if (this._leftTime > 4) {
                this.showFightBeginAnim();
            }

            // 检测是否正在使用高级续投
            if (this._cowboyRoom.betSettings.canAdvanceAutoBet) {
                this._checkAdvanceAutoReq();
            }
        } else if (this._cowboyRoom.gameState.roundState === network.RoundState.WAIT_NEXT_ROUND) {
            // 处于结束期间并即将开启新的一局
            let isSpecial = this.isResultSpecialCardType();
            let _specialDuration = isSpecial ? this._specialCardTypeDuration : 0; // 特殊牌型动画时间
            if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration +
                    this._showPublicCardsDuration +
                    this._showHandCardsDuration +
                    this._fightEndDuration +
                    this._betCountDownEndDuration
            ) {
                this._cowboyRoom.showTheNewestTrend = false;
                this.updateHistoryResultsPrevious();
                this.showWaitForNextRoundInAnim();
                this.updateAllCardsBeforeSettle();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.showBetCoutDownEndAnim();
                console.log('OnGameDataSynNotify, enter this.showBetCoutDownEndAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration +
                    this._showPublicCardsDuration +
                    this._showHandCardsDuration +
                    this._fightEndDuration
            ) {
                this._cowboyRoom.showTheNewestTrend = false;
                this.updateHistoryResultsPrevious();
                this.showWaitForNextRoundInAnim();
                this.updateAllCardsBeforeSettle();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.showFightEndAnim();
                console.log('OnGameDataSynNotify, enter showFightEndAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration +
                    this._showPublicCardsDuration +
                    this._showHandCardsDuration
            ) {
                this._cowboyRoom.showTheNewestTrend = false;
                this.updateHistoryResultsPrevious();
                this.showWaitForNextRoundInAnim();
                this.updateAllCardsBeforeSettle();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.showHandCardsAnim();
                console.log('OnGameDataSynNotify, enter showHandCardsAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration +
                    this._showPublicCardsDuration
            ) {
                this._cowboyRoom.showTheNewestTrend = false;
                this.updateHistoryResultsPrevious();
                this.showWaitForNextRoundInAnim();
                this.updateAllCardsExceptPublicBeforeSettle();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.showPublicCardsAnim();
                console.log('OnGameDataSynNotify, enter showPublicCardsAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration +
                    this._betWinFlagsAndFlyCoinsDuration +
                    _specialDuration +
                    this._hideLoseBetCoinsDuration
            ) {
                this._cowboyRoom.showTheNewestTrend = true;
                this.updateHistoryResultsPrevious();
                this.showWaitForNextRoundInAnim();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.updateCards();
                this.updateCardType();
                this.updateWinCards();
                this.showHideLoseBetCoinsAnim();
                this.showCowboyLoseAnim();
                console.log('OnGameDataSynNotify, enter showHideLoseBetCoinsAnim');
            } else if (
                this._leftTime >
                this._showNextRoundDuration + this._betWinFlagsAndFlyCoinsDuration + _specialDuration
            ) {
                // added
                this._cowboyRoom.showTheNewestTrend = true;
                this.updateHistoryResultsPrevious();
                this.showWaitForNextRoundInAnim();
                this.updatePlayerCoinBeforeSettle();
                this.updateAllBetAreas();
                this.updateCards();
                this.updateCardType();
                this.updateWinCards();
                this.clearLoseBetCoins();
                this.showCowboyLoseAnim();
                if (isSpecial) {
                    this.showSpecialCardTypeAnim();
                } else {
                    this.showBetWinFlagsAndFlyCoinsAnim();
                }
                console.log('OnGameDataSynNotify, enter showSpecialCardTypeAnim/showBetWinFlagsAndFlyCoinsAnim');
            } else if (this._leftTime > this._showNextRoundDuration + this._betWinFlagsAndFlyCoinsDuration) {
                this._cowboyRoom.showTheNewestTrend = true;
                this.showWaitForNextRoundInAnim();
                this.updateAllBetAreas();
                this.updateCards();
                this.updateCardType();
                this.updateWinCards();
                this.clearLoseBetCoins();
                this.showCowboyLoseAnim();
                if (isSpecial) {
                    this.showSpecialCardTypeAnim(
                        true,
                        this._leftTime - this._showNextRoundDuration - this._betWinFlagsAndFlyCoinsDuration
                    );
                } else {
                    this.showBetWinFlagsAndFlyCoinsAnim();
                }
                console.log(
                    'OnGameDataSynNotify, enter showSpecialCardTypeAnim/showBetWinFlagsAndFlyCoinsAnim, left time'
                );
            } else if (this._leftTime > this._showNextRoundDuration) {
                this._cowboyRoom.showTheNewestTrend = true;
                this.showWaitForNextRoundInAnim();
                this.updateAllBetAreas();
                this.updateCards();
                this.updateCardType();
                this.updateWinCards();
                this.showNextRoundTips();
                console.log('OnGameDataSynNotify, enter showSpecialCardTypeAnim left time');
            } else {
                this._cowboyRoom.showTheNewestTrend = true;
                if (this._leftTime > 2) {
                    this.showWaitForNextRoundInAnim();
                }

                this.updateAllBetAreas();
                this.updateCards();
                this.updateCardType();
                this.updateWinCards();
                this.updateWinFlags(); // 显示win标记
                this.showNextRoundTips();
                console.log('OnGameDataSynNotify, enter showNextRoundTips');
            }

            // 显示路子
            if (this._leftTime > this._showNextRoundDuration + this._betWinFlagsAndFlyCoinsDuration) {
                this._updateAllWayOut(1);
            } else if (this._leftTime > this._showNextRoundDuration) {
                this._updateAllWayOut(1);
                this._showAllWayOutAnim();
            } else {
                this._updateAllWayOut();
            }
        }
    }

    // 一局结束
    OnGameRoundEndNotify(): void {
        this._cowboyRoom.showTheNewestTrend = false;
        this.resetLeftTimer();

        // 下注倒计时结束动画	 . 开战动画	.	翻牌动画 . 显示win标记，金币收回动画	. 等待下一局动画
        this.showBetCoutDownEndAnim();

        // 隐藏高级续投选择面板
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).hideSelectPanel(false);
        }
    }

    // 新的一局
    OnDealNotify(): void {
        this._cowboyRoom.showTheNewestTrend = true;
        this.clearRound();
        this.resetLeftTimer();
        this.updatBetButtonByCurCoin();
        this._updateBetButtonState();
        // this.updateSelfCoin();
        this.updateOtherPlayersInfo();
        this.showCowboyNormalAnim();
        this.showSendCardTips();
        this.updateAllPlayerWinCount();
        this.handleCoin();
        this._updateAllWayOut();

        // 开局动画	.	发牌动画/翻牌动画		. 出战动画
        this.showRoundStartAnim();
    }

    // 开始下注
    OnStartBetNotify(): void {
        // console.log('[3in1] start bet notify');
        this._cowboyRoom.showTheNewestTrend = true;
        this.resetLeftTimer();
        this.updatBetButtonByCurCoin();
        this._updateBetButtonState();
        this.hideGameTips();
        // 下注倒计时开始动画
        this.showBetCoutDownBeginAnim();
        // 检测是否正在使用高级续投
        this._checkAdvanceAutoReq();
    }

    OnBetNotify(bet: PlayerOneBet): void {
        this._updateAutoBetBtnStatus();
        // this.showBetInAnim();

        // 自己筹码变化后判断一下下注筹码状态
        if (bet.uid === this._authService.currentUser.userId) {
            this._updateBetButtonState();
        }

        let tempData = new PlayerOneBet();
        tempData.betAmount = bet.betAmount;
        tempData.betOption = bet.betOption;
        tempData.uid = bet.uid;
        this._vCoinOptimizationDeque.push_back(tempData);
    }

    OnAutoBetNotify(bets: PlayerOneBet[]): void {
        // // this.showBetInAnim(true);
        // let oneBet = cb.getCowboyRoom().curPlayerBet;
        // let tempData = new PlayerOneBet();
        // tempData.betAmount = oneBet.betAmount;
        // tempData.betOption = oneBet.betOption;
        // tempData.uid = oneBet.uid;
        // this._vCoinOptimizationDeque.push_back(tempData);
        bets.forEach((bet) => {
            let tempData = new PlayerOneBet();
            tempData.betAmount = bet.betAmount;
            tempData.betOption = bet.betOption;
            tempData.uid = bet.uid;
            this._vCoinOptimizationDeque.push_back(tempData);
        });

        let betSize = pf.StringUtil.getArrayLength(bets);
        if (betSize > 1) {
            this._playSoundEffect(macros.Audio.BET_MANY);
        } else {
            this._playSoundEffect(macros.Audio.BET);
        }
        this._updateBetButtonState();
    }

    // OnAutoBetNotifyHandleOver(pSender: number): void {
    //     let betSize = pSender;
    //     if (betSize > 1) {
    //         this._playSoundEffect(macros.Audio.BET_MANY);
    //     } else {
    //         this._playSoundEffect(macros.Audio.BET);
    //     }
    //     this._updateBetButtonState();
    // }

    // OnLeaveRoomSucc(pSender: any): void {
    //     this.cleanData();
    //     this._backToCowboyListScene();
    // }

    OnAutoBetSucc(): void {
        this._updateAutoBetBtnStatus();
    }

    _onMsgBetAmountLevelChange(sender: any): void {
        this._updateBetAmountLevel();
        this._updateBetButtonState();
    }

    _onMsgAdvanceAutobetSet(sender: any): void {
        this._setAutoBetBtnStytle(MiniGameCommonDef.eGameboyAutoBtnStyle.GAB_STYLE_ADVANCE_USING);

        // 如果本局没有下注,且已勾选续投局数,则本局就生效一次
        if (!this._cowboyRoom.roundInfo.hasBetInCurRound && this._cowboyRoom.betSettings.canAutoBet) {
            this._checkAdvanceAutoReq();
        }
    }

    _onMsgAdvanceAutobet(code: number): void {
        switch (code) {
            case network.ErrorCode.OK:
                break;

            // 高级续投超出限红
            case network.ErrorCode.AUTO_BET_EXCEED_LIMIT:
                if (this._humanboyAdvancedAuto) {
                    this._humanboyAdvancedAuto
                        .getComponent(HumanboyAdvancedAutoControl)
                        .adaptAdvanceAutoTipsPos(this._btnBetAuto.node);
                    this._humanboyAdvancedAuto
                        .getComponent(HumanboyAdvancedAutoControl)
                        .showAdvanceAutoTips(
                            pf.languageManager.getString(pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', code))
                        );
                }
                break;

            // 高级续投金额不足
            case network.ErrorCode.AUTO_BET_NO_MONEY:
                if (pf.app.clientType === pf.client.ClientType.CowboyWeb) {
                    cr.commonResourceAgent.commonDialog.showMsg(
                        pf.languageManager.getString(pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', code)),
                        [
                            pf.languageManager.getString('TipsPanel_sure_button'),
                            pf.languageManager.getString('TipsPanel_cancel_button')
                        ],
                        null
                    );
                } else {
                    let strNodeName = 'cowboy_dialog_recharge';
                    let dialogNode = this.node.getChildByName(strNodeName);
                    if (!dialogNode) {
                        let dialogNode = cc.instantiate(
                            pf.addressableAssetManager.getAsset(macros.Assets.MINI_GAME_DIALOG)
                        );
                        const miniGameDialog: IMiniGameDialog = dialogNode.getComponent(MiniGameDialog);
                        this.node.addChild(dialogNode, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_TOAST);

                        const stringContent = pf.languageManager.getString(
                            pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', code)
                        );
                        const stringLeftBtn = pf.languageManager.getString('CowBoy_btn_desc_auto_cancel');
                        const stringRightBtn = pf.languageManager.getString('CowBoy_btn_desc_auto_recharge');
                        const cbLeftBtn = (dialog: IMiniGameDialog) => {
                            // cv.cowboyNet.ReqCancelAdvanceAutoBet();
                            this._cowboyRoom.cancelAdavnceAutoBet();
                        };
                        const cbRightBtn = (dialog: IMiniGameDialog) => {
                            this.openShop(null);
                        };

                        const miniGameDialogConfig: IMiniGameDialogConfig = {
                            miniDialog: miniGameDialog,
                            stringContent,
                            stringLeftBtn,
                            stringRightBtn,
                            cbLeftBtn,
                            cbRightBtn,
                            isReachedMax: false,
                            legacyDialog: dialogNode.getComponent(HumanboyDialogControl),
                            isShowBtnCenter: false,
                            themeType: ThemeSystemType.TwoButton_NoMoney_Style
                        };

                        ConcreteMiniGameDialog.showDialog(miniGameDialogConfig);
                        dialogNode.name = strNodeName;
                    }
                }
                break;

            default:
                // cv.MessageCenter.send('on_cowboy_server_error', code);
                this.OnServerError(code);
                break;
        }

        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).showAdvanceAutoCount();
        }
    }

    _onMsgAdvanceAutobetCancel(sender: any): void {
        this._updateBetAmountLevel();
        this._updateBetButtonState();
    }

    OnRoomParamChangeNotify(): void {
        this._updateBetAmountLevel();
        this._updateBetButtonState();
    }

    _onMsgAdvanceAutobetLimitReached(addedCount: number): void {
        if (addedCount) {
            cr.commonResourceAgent.toastMessage.showMsg(
                pf.StringUtil.formatC(pf.languageManager.getString('MiniGame_btn_desc_auto_bet_reached'), addedCount)
            );
        }
    }

    OnServerError(pSender: number): void {
        let i32Error = pSender;

        let acBuffer: string = pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', i32Error);
        cc.log('[3in1] %s', acBuffer);

        if (i32Error === network.ErrorCode.BET_TOO_SMALL) {
            let errText = pf.StringUtil.formatC('%s', pf.languageManager.getString(acBuffer));
            let formatCoin = pf.StringUtil.clientGoldByServer(this._cowboyRoom.roomParams.smallBet);
            this.showCowboyToast(pf.StringUtil.formatC(errText, pf.StringUtil.numberToString(formatCoin)));
        } else if (i32Error === network.ErrorCode.NO_BET) {
            // 忽略提示:已停止下注
        } else if (i32Error === network.ErrorCode.CAN_NOT_LEAVE_IN_BETTING) {
            cr.commonResourceAgent.toastMessage.showMsg(pf.languageManager.getString(acBuffer));

            // possible scenario: push recharge button while there are still unsettled bets
            // isSelfRecharge needs to be reset or exiting game will lead to recharge flow
            const context = pf.app.getGameContext<pf.services.MiniGameContext>();
            context.isSelfRecharge = false;
        } else {
            const errorDesc = pf.languageManager.getString(acBuffer);
            if (errorDesc) {
                this.showCowboyToast(pf.StringUtil.formatC('%s', errorDesc));
            } else {
                this.showCowboyToast(acBuffer);
            }
        }
    }

    OnKickNotify(pSender: network.IKickNotify): void {
        let kickType = pSender.kickType;
        if (pSender.idle_roomid > 0) {
            if (!this._bSwitchTable) {
                this._cowboyRoom.roundInfo.idleRoomId = pSender.idle_roomid;
                this.showSwitchTable();
            }
            return;
        }
        if (kickType === network.Kick.IDLE_LONG_TIME) {
            this._backToMainScene(
                pf.StringUtil.formatC('%s', pf.languageManager.getString('Cowboy_server_kick_long_time_text'))
            );
        } else if (kickType === network.Kick.Stop_World) {
            this._backToMainScene(
                pf.StringUtil.formatC('%s', pf.languageManager.getString('Cowboy_server_kick_stop_world_text'))
            );
        }
    }

    OnSoundSwitchNotify(): void {
        // if (cv.tools.isPlayMusic()) {
        //     this.playCowboyBGM();
        // } else {
        //     this.stopCowboyBGM();
        // }
        pf.audioManager.enableMusic = pf.localStorage.getItem(macros.AudioSettingKeys.MUSIC) !== 'false';
        pf.audioManager.enalbeSoundEffect = pf.localStorage.getItem(macros.AudioSettingKeys.SOUND_EFFECT) !== 'false';

        pf.audioManager.playMusic(macros.Audio.BGM);
    }

    // OnSelfInfo(): void {
    //     //     if (_minitary == null) {
    //     //         Size winSize = Director. getInstance().getWinSize();
    //     //         _minitary = static_cast < CowboyMilitary *> (CowboyMilitary. createLayer());
    //     //         //this._chart.setPosition(Vec2(winSize.width / 2, winSize.height / 2));
    //     //         addChild(_minitary);
    //     //     }
    //     //     else {
    //     //         _minitary..node.active = (true);
    //     //         //_minitary.setData();
    //     // }
    // }

    // OnJoinRoomFailed(pSender: number): void {
    //     let i32Error = pSender;
    //     let acBuffer = pf.StringUtil.formatC('Cowboy_ServerErrorCode%d', i32Error);

    //     this._backToMainScene(pf.StringUtil.formatC('%s', pf.languageManager.getString(acBuffer)));
    // }

    OnCowboyRewardTips(value: string): void {
        this._rewardPanel.active = true;
        this._rewardPanel.setContentSize(this._rewardPanel_width, this._rewardPanel.height);
        this._rewardTips.maxWidth = 0;
        pf.UIUtil.adaptWidget(this._rewardPanel, true);
        this._rewardTips.maxWidth = this._rewardTips.node.width;

        let len = pf.StringUtil.getLengthForCN(this._rewardTips.node, value);
        if (len > 3) {
            let width = (len / 3) * this._rewardTips.node.width;
            this._rewardTips.maxWidth = width;
            this._rewardPanel.setContentSize(width + 27, this._rewardPanel.height);
            pf.UIUtil.adaptWidget(this._rewardPanel, true);
        }

        let str = pf.StringUtil.calculateAutoWrapString(this._rewardTips.node, value);
        let strArr: string[] = str.split('\n');
        len = strArr.length;

        if (len > 3) {
            let width = (len / 3) * this._rewardTips.node.width;
            this._rewardTips.maxWidth = width;
            this._rewardPanel.setContentSize(width + 27, this._rewardPanel.height);
            pf.UIUtil.adaptWidget(this._rewardPanel, true);
        }
        this._rewardTips.string = pf.StringUtil.calculateAutoWrapString(this._rewardTips.node, value);

        this._rewardPanel.stopAllActions();
        this._rewardPanel.runAction(
            cc.sequence(
                cc.delayTime(4.0),
                cc.callFunc(() => {
                    this._rewardPanel.stopAllActions();
                    this._rewardPanel.active = false;
                }, this)
            )
        );
    }

    // any reference?
    // onBtnTopBgClick(event: cc.Event) {
    //     // cv.cowboyNet.RequestTrend();
    //     this._cowboyRoom.queryTrend();
    //     this._playSoundEffect(macros.Audio.PRESS);
    // }

    _onMsgUpdateWorldServerGold(isForce?: boolean): void {
        // world服接收接口已过滤只发自己, 因此这里无需再次判断(同时没有别的需求, 所以也不用缓存下发的结构)
        const checkedIsForce = isForce === true ? true : false;
        let llCurGold = this._walletService.getWallet().totalAmount;

        // 结算阶段跳过(否则会提前知道输赢结果)
        if (this._cowboyRoom.canUpdateWorldServerGold || checkedIsForce) {
            // 更新自己金币信息
            this._cowboyRoom.selfPlayer.curCoin = this._walletService.getWallet().totalAmount;
            this.updateSelfCoin();

            // 更新其他人信息(因为自己有可能会在8人列表中)
            let bOnMainPlayerList = false;
            let otherPlayersInfo = this._cowboyRoom.otherPlayers;
            let otherInfoLen = otherPlayersInfo.length;
            for (let i = 0; i < otherInfoLen; ++i) {
                if (this._authService.currentUser.userId === otherPlayersInfo[i].uid) {
                    bOnMainPlayerList = true;
                    otherPlayersInfo[i].curCoin = llCurGold;
                }
            }
            if (bOnMainPlayerList) {
                this.updateOtherCoin();
            }
        }
    }

    protected initWayOutInfoByAreaIdx(iAreaIdx: number): void {
        if (iAreaIdx < 0 || iAreaIdx >= pf.StringUtil.getArrayLength(this._betAreas)) return;

        let panelWayOut = this._betAreas[iAreaIdx].getChildByName('panel_way_out');
        if (!panelWayOut) return;
        if (panelWayOut.getComponent(cc.Mask)) {
            panelWayOut.removeComponent(cc.Mask);
        }

        let tWayOutInfo = new CowboyWayOutInfo();
        this._mapWayOutInfo.set(iAreaIdx, tWayOutInfo);

        tWayOutInfo.iAreaIdx = iAreaIdx;
        tWayOutInfo.panelWayOut = panelWayOut;
        tWayOutInfo.panelWayOut.on(cc.Node.EventType.TOUCH_END, (): void => {
            // 点击路子入口事件
            // cv.cowboyNet.RequestTrend();
            this._cowboyRoom.queryTrend();
            this._cowboyChart.active = true;
            // cv.MessageCenter.send('on_display_page2');
            this._cowboyChartControl.onPage2();
            this._playSoundEffect(macros.Audio.PRESS);
        });

        // 路子球状图片
        do {
            let children = tWayOutInfo.panelWayOut.children;
            let count = children.length;
            for (let i_wayout_index = 0; i_wayout_index < count; ++i_wayout_index) {
                let strImgName = pf.StringUtil.formatC('img_%d', i_wayout_index);
                let img = tWayOutInfo.panelWayOut.getChildByName(strImgName);
                if (img) {
                    img.active = false;
                    tWayOutInfo.vWayOutImg.push(img);
                    tWayOutInfo.vWayOutImgSrcPos.push(img.getPosition());
                }
            }
        } while (0);

        // 文本
        do {
            let txt = tWayOutInfo.panelWayOut.getChildByName('txt_way_out');
            if (txt) {
                if (!tWayOutInfo.rtxtWayOut) tWayOutInfo.rtxtWayOut = new cc.Node().addComponent(cc.RichText);
                tWayOutInfo.rtxtWayOut.fontSize = txt.getComponent(cc.Label).fontSize;
                tWayOutInfo.rtxtWayOut.node.setAnchorPoint(txt.getAnchorPoint());
                tWayOutInfo.rtxtWayOut.node.setContentSize(txt.getContentSize());
                let pos = this._betContentBg.node.convertToNodeSpaceAR(
                    txt.getParent().convertToWorldSpaceAR(txt.getPosition())
                );
                tWayOutInfo.rtxtWayOut.node.setPosition(pos);
                tWayOutInfo.rtxtWayOut.node.active = false;
                tWayOutInfo.rtxtWayOut.handleTouchEvent = false;

                this._betContentBg.node.addChild(tWayOutInfo.rtxtWayOut.node);
                if (pf.UIUtil.isValidNode(txt)) {
                    txt.removeFromParent(true);
                    txt.destroy();
                }
            }
        } while (0);

        // 路子显示风格
        do {
            let option = this.getBetOptionByAreaIdx(iAreaIdx);
            switch (option) {
                // 牛仔胜利
                case network.BetZoneOption.EQUAL:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                    tWayOutInfo.iWayOutLoseLimitCount = 200;
                    break;

                case network.BetZoneOption.HOLE_3_TONG_SAME_SHUN:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                    break; // 顺子/同花/同花顺
                case network.BetZoneOption.FIVE_NONE_1DUI:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                    break; // 高牌/一对
                case network.BetZoneOption.FIVE_2DUI:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                    break; // 两对

                // 对子
                case network.BetZoneOption.HOLE_SAME:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO;
                    tWayOutInfo.iWayOutLoseLimitCount = 200;
                    break;

                // 对A
                case network.BetZoneOption.HOLE_A:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                    tWayOutInfo.iWayOutLoseLimitCount = 200;
                    break;

                // 三条/顺子/同花
                case network.BetZoneOption.FIVE_3_SHUN_TONG_HUA:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG;
                    break;

                // 葫芦
                case network.BetZoneOption.FIVE_3_2:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                    tWayOutInfo.iWayOutLoseLimitCount = 200;
                    break;

                // 金刚/同花顺/皇家
                case network.BetZoneOption.FIVE_KING_TONG_HUA_SHUN_4:
                    tWayOutInfo.eWayOutStyle = MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT;
                    tWayOutInfo.iWayOutLoseLimitCount = 300;
                    break;

                default:
                    break;
            }
        } while (0);
    }

    _clearWayOutInfo(): void {
        this._mapWayOutInfo.clear(); // 只是清除元素,内存并没有变化
    }

    /**
     * 路单滚动动画
     * @param iAreaIdx
     */
    _showWayOutMoveAnim(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let panelWayOut: cc.Node = this._mapWayOutInfo.get(iAreaIdx).panelWayOut;
        let vWayOutImg: cc.Node[] = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let vWayOutImgSrcPos: cc.Vec2[] = this._mapWayOutInfo.get(iAreaIdx).vWayOutImgSrcPos;
        if (!panelWayOut || pf.StringUtil.getArrayLength(vWayOutImg) <= 0) return;

        // 裁剪右移 模式
        // let tarPos: cc.Vec2 = cc.v2(cc.Vec2.ZERO);
        // for (let i = 0; i < vWayOutImg.length; ++i) {
        //     if (i === 0) {
        //         tarPos.x = vWayOutImgSrcPos[i].x - vWayOutImg[iAreaIdx].width * vWayOutImg[iAreaIdx].scaleX;
        //         tarPos.y = vWayOutImgSrcPos[i].y;
        //     }
        //     else {
        //         tarPos.x = vWayOutImgSrcPos[i - 1].x;
        //         tarPos.y = vWayOutImgSrcPos[i - 1].y;
        //     }

        //     vWayOutImg[i].runAction(cc.sequence(cc.moveTo(0.3, tarPos), cc.callFunc((): void => {
        //         if (i === vWayOutImg.length - 1) {
        //             this._updateWayOut(iAreaIdx, 0);
        //         }
        //     }, this)));
        // }

        // 缩小渐隐右移 模式
        let st: cc.ActionInterval = cc.scaleTo(0.2, 0);
        let fo: cc.ActionInterval = cc.fadeOut(0.3);
        let spawn: cc.FiniteTimeAction = cc.spawn(st, fo);
        vWayOutImg[0].runAction(
            cc.sequence(
                spawn,
                cc.callFunc((): void => {
                    vWayOutImg[0].active = false;
                    let tarPos: cc.Vec2 = cc.v2(cc.Vec2.ZERO);
                    for (let i = 0; i < vWayOutImg.length; ++i) {
                        if (i === 0) continue;

                        tarPos.x = vWayOutImgSrcPos[i - 1].x;
                        tarPos.y = vWayOutImgSrcPos[i - 1].y;
                        vWayOutImg[i].runAction(
                            cc.sequence(
                                cc.moveTo(0.5, tarPos),
                                cc.callFunc((): void => {
                                    if (i === vWayOutImg.length - 1) {
                                        this._updateWayOut(iAreaIdx, 0);
                                        vWayOutImg[0].setScale(1.0);
                                        vWayOutImg[0].opacity = 0xff;
                                        vWayOutImg[0].active = true;
                                    }
                                }, this)
                            )
                        );
                    }
                })
            )
        );
    }

    _hideWayOutMoveAnim(): void {
        for (let i = 0; i < this.AREA_SIZE; ++i) {
            if (!this._mapWayOutInfo.has(i)) continue;
            let panelWayOut = this._mapWayOutInfo.get(i).panelWayOut;
            if (!panelWayOut) continue;

            if (panelWayOut.getComponent(cc.Mask)) {
                panelWayOut.removeComponent(cc.Mask);
            }

            let vWayOutImg = this._mapWayOutInfo.get(i).vWayOutImg;
            let vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
            for (let j = 0; j < vWayOutImgLen; ++j) {
                vWayOutImg[j].stopAllActions();
                vWayOutImg[j].setPosition(this._mapWayOutInfo.get(i).vWayOutImgSrcPos[j]);
            }
        }
    }

    _showWayOutImgAnim(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
        if (vWayOutImgLen <= 0) return;

        // let mapZoneData = cb.getCowboyRoom().mapZoneData;
        // let it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._cowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        // 隐藏路单文本
        let rtxtWayOut: cc.RichText = this._mapWayOutInfo.get(iAreaIdx).rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 该区域输赢(0 - 未击中, 1 - 击中)
        // let result = it_zoneData.result;
        let result = zoneData.optionResult.result;

        let fileName = '';

        // 输
        if (result === 0) {
            fileName = 'cowboy_icon_circle_small_gray';
        }
        // 赢
        else if (result === 1) {
            if (pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN) {
                fileName = 'cowboy_icon_circle_small_red';
            } else {
                fileName = 'cowboy_icon_circle_small_red_en';
            }
        }

        // 计算空闲路子索引
        let freeIndex = vWayOutImg.length;
        for (let i = 0; i < freeIndex; ++i) {
            if (!vWayOutImg[i].active) {
                freeIndex = i;
                break;
            }
        }

        // 路子满了挤动动画(老模式)
        // this._nodeAnim.runAction(cc.sequence(cc.delayTime(0.6 * this._fActExecute_WayOut), cc.callFunc((): void => {
        //     if (freeIndex > vWayOutImgLen - 1) {
        //         this._showWayOutMoveAnim(iAreaIdx);
        //     }
        //     else {
        //         vWayOutImg[freeIndex].active = true;
        //         cb.loadSpriteTextureByPlist(this._atlasDZNZ, vWayOutImg[freeIndex].getComponent(cc.Sprite), fileName);
        //         // vWayOutImg[freeIndex].ignoreContentAdaptWithSize(true);
        //     }
        // })));

        // 路子满了挤动动画
        if (freeIndex > vWayOutImgLen - 1) {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.3 * this._fActExecute_WayOut),
                    cc.callFunc(() => {
                        this._showWayOutMoveAnim(iAreaIdx);
                    })
                )
            );
        } else {
            this._nodeAnim.runAction(
                cc.sequence(
                    cc.delayTime(0.8 * this._fActExecute_WayOut),
                    cc.callFunc(() => {
                        vWayOutImg[freeIndex].active = true;
                        // cb.loadSpriteTextureByPlist(
                        //     this._atlasDZNZ,
                        //     vWayOutImg[freeIndex].getComponent(cc.Sprite),
                        //     fileName
                        // );
                        vWayOutImg[freeIndex].getComponent(cc.Sprite).spriteFrame =
                            this._atlasDZNZ.getSpriteFrame(fileName);
                    })
                )
            );
        }
    }

    _showWayOutAnim(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let panelWayOut = this._mapWayOutInfo.get(iAreaIdx).panelWayOut;
        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
        if (!panelWayOut || vWayOutImgLen <= 0) return;

        // let mapZoneData = cb.getCowboyRoom().mapZoneData;
        // let it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._cowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        // 路子显示风格
        switch (this._mapWayOutInfo.get(iAreaIdx).eWayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(iAreaIdx, 1);
                this._showWayOutImgAnim(iAreaIdx);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(iAreaIdx);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    let bShowTxt = false;
                    // let vHistoryResults = it_zoneData.vHistoryResults;
                    let vHistoryResults = zoneData.optionResult.historyResults;
                    if (vHistoryResults.length > 0 && vHistoryResults.length > vWayOutImg.length) {
                        let bDefeat = true;
                        let vWayOutImgLen = vWayOutImg.length;
                        for (let i = 0; i <= vWayOutImgLen; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] === 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(iAreaIdx);
                    } else {
                        this._updateWayOutImg(iAreaIdx, 1);
                        this._showWayOutImgAnim(iAreaIdx);
                    }
                }
                break;

            default:
                break;
        }
    }

    _showAllWayOutAnim(): void {
        this._mapWayOutInfo.forEach((value: CowboyWayOutInfo, key: number) => {
            this._showWayOutAnim(key);
        });
    }

    _updateWayOutImg(iAreaIdx: number, reduce: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let panelWayOut = this._mapWayOutInfo.get(iAreaIdx).panelWayOut;
        panelWayOut.active = true;

        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let vWayOutImgSrcPos = this._mapWayOutInfo.get(iAreaIdx).vWayOutImgSrcPos;

        // let mapZoneData = cb.getCowboyRoom().mapZoneData;
        // let it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._cowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        // 隐藏路单文本
        let rtxtWayOut: cc.RichText = this._mapWayOutInfo.get(iAreaIdx).rtxtWayOut;
        if (rtxtWayOut) {
            rtxtWayOut.string = '';
            rtxtWayOut.node.active = false;
        }

        // 逆序取历史记录
        let fileName = '';
        // let vHistoryResults = it_zoneData.vHistoryResults;
        let vHistoryResults = zoneData.optionResult.historyResults;

        let vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
        let vHistoryResultsLen = pf.StringUtil.getArrayLength(vHistoryResults);
        let min_count = vWayOutImgLen < vHistoryResultsLen ? vWayOutImgLen : vHistoryResultsLen;
        let end_index = 0;
        let end_count = 0;

        // ui显示个数 >= 路子数据个数, 少显示 reduce 个
        if (vWayOutImgLen >= vHistoryResultsLen) {
            end_index = min_count - 1;
            end_count = min_count - reduce;
        }
        // ui显示个数 < 路子数据个数, 偏移 reduce 位数据显示
        else {
            end_index = min_count - 1 + reduce;
            end_count = min_count;
        }

        for (let i = 0; i < vWayOutImgLen; ++i) {
            // 复原位置
            vWayOutImg[i].setPosition(vWayOutImgSrcPos[i]);

            let index = end_index - i;
            if (i < end_count && index >= 0 && index < vHistoryResultsLen) {
                vWayOutImg[i].active = true;

                // 该区域输赢(0 - 未击中, 1 - 击中)
                let result = vHistoryResults[index];
                if (result === 0) {
                    fileName = 'cowboy_icon_circle_small_gray';
                } else if (result === 1) {
                    if (pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN) {
                        fileName = 'cowboy_icon_circle_small_red';
                    } else {
                        fileName = 'cowboy_icon_circle_small_red_en';
                    }
                }
                // cb.loadSpriteTextureByPlist(this._atlasDZNZ, vWayOutImg[i].getComponent(cc.Sprite), fileName);
                vWayOutImg[i].getComponent(cc.Sprite).spriteFrame = this._atlasDZNZ.getSpriteFrame(fileName);
                // vWayOutImg[i].ignoreContentAdaptWithSize(true);
            } else {
                vWayOutImg[i].active = false;
            }
        }
    }

    _updateWayOutTxt(iAreaIdx: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;
        let rtxtWayOut: cc.RichText = this._mapWayOutInfo.get(iAreaIdx).rtxtWayOut;
        if (!rtxtWayOut) return;

        // 隐藏路单球图片面板
        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        let iWayOutLoseLimitCount = this._mapWayOutInfo.get(iAreaIdx).iWayOutLoseLimitCount;
        let vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
        for (let i = 0; i < vWayOutImgLen; ++i) {
            vWayOutImg[i].active = false;
        }

        // let eCurState = cb.getCowboyRoom().curState;
        let eCurState = this._cowboyRoom.gameState.roundState;
        // let mapZoneData = cb.getCowboyRoom().mapZoneData;
        let mapZoneData = this._cowboyRoom.betZones;

        let it = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (it) {
            // 连续多少手未出现(< 0 房间刚刚开始,不需要统计; > 0 多少手; = 0 上一手出现过)
            let luckLoseHand = it.optionResult.luckLoseHand;

            if (luckLoseHand < 0) {
                rtxtWayOut.string = '';
            } else if (luckLoseHand === 0) {
                if (eCurState === network.RoundState.WAIT_NEXT_ROUND) {
                    pf.StringUtil.setRichTextString(
                        rtxtWayOut.node,
                        pf.languageManager.getString('Cowboy_game_wayout_hit_txt')
                    );
                } else {
                    pf.StringUtil.setRichTextString(
                        rtxtWayOut.node,
                        pf.languageManager.getString('Cowboy_game_wayout_hit_last_txt')
                    );
                }
            } else {
                let strCountDest = '';
                if (iWayOutLoseLimitCount !== 0 && luckLoseHand > iWayOutLoseLimitCount) {
                    strCountDest = pf.StringUtil.formatC('%d+', iWayOutLoseLimitCount);
                } else {
                    strCountDest = pf.StringUtil.formatC('%d', luckLoseHand);
                }
                pf.StringUtil.setRichTextString(
                    rtxtWayOut.node,
                    pf.StringUtil.formatC(pf.languageManager.getString('Cowboy_game_wayout_lose_txt'), strCountDest)
                );
            }

            let szParent = rtxtWayOut.node.getParent().getContentSize();
            let szTextNode = rtxtWayOut.node.getContentSize();

            rtxtWayOut.node.active = true;
            // rtxtWayOut.node.setPosition((szParent.width - szTextNode.width) / 2, (szParent.height - szTextNode.height) / 2);
        }
    }

    _updateWayOut(iAreaIdx: number, reduce: number): void {
        if (!this._mapWayOutInfo.has(iAreaIdx)) return;

        let vWayOutImg = this._mapWayOutInfo.get(iAreaIdx).vWayOutImg;
        // let mapZoneData = cb.getCowboyRoom().mapZoneData;
        // let it_zoneData = mapZoneData.get(this.getBetOptionByAreaIdx(iAreaIdx));
        // if (!it_zoneData) return;
        const zoneData = this._cowboyRoom.betZones.get(this.getBetOptionByAreaIdx(iAreaIdx));
        if (!zoneData) {
            return;
        }

        switch (this._mapWayOutInfo.get(iAreaIdx).eWayOutStyle) {
            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_NONE:
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_IMG:
                this._updateWayOutImg(iAreaIdx, reduce);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_TXT:
                this._updateWayOutTxt(iAreaIdx);
                break;

            case MiniGameCommonDef.eGameboyWayOutStyle.GWS_AUTO:
                {
                    let bShowTxt = false;
                    // let vHistoryResults = it_zoneData.vHistoryResults;
                    let vHistoryResults = zoneData.optionResult.historyResults;
                    let vWayOutImgLen = pf.StringUtil.getArrayLength(vWayOutImg);
                    let vHistoryResultsLen = pf.StringUtil.getArrayLength(vHistoryResults);
                    if (vHistoryResultsLen > 0 && vHistoryResultsLen > vWayOutImgLen) {
                        let bDefeat = true;
                        for (let i = 0; i <= vWayOutImgLen; ++i) {
                            bDefeat = bDefeat && vHistoryResults[i] === 0;
                        }
                        if (bDefeat) {
                            bShowTxt = true;
                        }
                    }

                    if (bShowTxt) {
                        this._updateWayOutTxt(iAreaIdx);
                    } else {
                        this._updateWayOutImg(iAreaIdx, reduce);
                    }
                }
                break;

            default:
                break;
        }
    }

    _updateAllWayOut(reduce?: number /* = 0 */): void {
        const checkedReduce = reduce === undefined ? 0 : reduce;
        this._mapWayOutInfo.forEach((value: CowboyWayOutInfo, key: number) => {
            this._updateWayOut(key, checkedReduce);
        });
    }

    initCowboyAnims(): void {
        this._heroBoy = cc.find('node_boy/img', this._gameContent);
        this._heroCow = cc.find('node_cow/img', this._gameContent);

        this._cowWinAnim = this.initAni(this._heroCow.getParent(), this.cow_win_prefab);
        this._cowWinAction = this._cowWinAnim.getComponent(cc.Animation);

        this._cowLoseAnim = this.initAni(this._heroCow.getParent(), this.cow_lose_prefab);
        this._cowLoseAction = this._cowLoseAnim.getComponent(cc.Animation);

        this._boyWinAnim = this.initAni(this._heroBoy.getParent(), this.boy_win_prefab);
        this._boyWinAction = this._boyWinAnim.getComponent(cc.Animation);

        this._boyLoseAnim = this.initAni(this._heroBoy.getParent(), this.boy_lose_prefab);
        this._boyLoseAction = this._boyLoseAnim.getComponent(cc.Animation);

        this._cowWinAnim.zIndex = -1;
        this._cowLoseAnim.zIndex = -1;
        this._boyWinAnim.zIndex = -1;
        this._boyLoseAnim.zIndex = -1;
    }

    getTimelineAnimSpeed(atl: cc.Animation, fExecuteTime: number): number {
        let fRet = 0;
        if (atl && fExecuteTime > 0) {
            let frameInternal = 1 / 60.0;
            fRet = (atl.defaultClip.duration * frameInternal) / fExecuteTime;
        }
        return fRet;
    }
    setTimeSpeed(ani: cc.Animation, speed: number) {
        ani.defaultClip.speed = speed;
    }
    // 牛仔输时哭的动画
    showCowboyLoseAnim(): void {
        this.showCowboyNormalAnim();
        this._playSoundEffect(macros.Audio.WIN_LOSE);

        // 0 平, 1 牛仔胜, -1 小牛胜
        if (this._cowboyRoom.roundInfo.roundResult.result === 1) {
            // 牛仔赢
            do {
                this._heroBoy.active = false;
                let pos = this._heroBoy.getPosition();

                this._boyWinAnim.active = true;
                this._boyWinAnim.setPosition(pos);
                let endIndex = this._boyWinAction.defaultClip.duration;
                // let speed = this.getTimelineAnimSpeed(this._boyWinAction, this._fActExecute_BoyWin);
                // this.setTimeSpeed(this._boyWinAction,speed);
                this.gotoFrameAndPlay(this._boyWinAction, 0, endIndex, false);
                this._boyWinAction.on('finished', (event: cc.Event): void => {
                    this._boyWinAction.node.off('finished');
                    this._boyWinAnim.active = false;
                    this._heroBoy.active = true;
                });
            } while (0);

            // 牛输
            do {
                this._heroCow.active = false;
                let pos = this._heroCow.getPosition();

                this._cowLoseAnim.active = true;
                this._cowLoseAnim.setPosition(pos);
                let endIndex = this._cowLoseAction.defaultClip.duration;
                // let speed = this.getTimelineAnimSpeed(this._cowLoseAction, this._fActExecute_CowLose);
                // this.setTimeSpeed(this._cowLoseAction,speed);
                this.gotoFrameAndPlay(this._cowLoseAction, 0, endIndex, false);
                this._cowLoseAction.on('finished', (event: cc.Event): void => {
                    this._cowLoseAction.node.off('finished');
                    this._cowLoseAnim.active = false;
                    this._heroCow.active = true;
                });
            } while (0);
        } else if (this._cowboyRoom.roundInfo.roundResult.result === -1) {
            // 牛赢
            do {
                this._heroCow.active = false;
                let pos = this._heroCow.getPosition();

                this._cowWinAnim.active = true;
                this._cowWinAnim.setPosition(pos);
                let endIndex = this._cowWinAction.defaultClip.duration;
                // let speed = this.getTimelineAnimSpeed(this._cowWinAction, this._fActExecute_CowWin);
                // this.setTimeSpeed(this._cowWinAction,speed);
                this.gotoFrameAndPlay(this._cowWinAction, 0, endIndex, false);
                this._cowWinAction.on('finished', (event: cc.Event): void => {
                    this._cowWinAction.node.off('finished');
                    this._cowWinAnim.active = false;
                    this._heroCow.active = true;
                });
            } while (0);

            // 牛仔输
            do {
                this._heroBoy.active = false;
                let pos = this._heroBoy.getPosition();

                this._boyLoseAnim.active = true;
                this._boyLoseAnim.setPosition(pos);
                let endIndex = this._boyLoseAction.defaultClip.duration;
                // let speed = this.getTimelineAnimSpeed(this._boyLoseAction, this._fActExecute_BoyLose);
                // this.setTimeSpeed(this._boyLoseAction,speed);
                this.gotoFrameAndPlay(this._boyLoseAction, 0, endIndex, false);
                this._boyLoseAction.on('finished', (event: cc.Event): void => {
                    this._boyLoseAction.node.off('finished');
                    this._boyLoseAnim.active = false;
                    this._heroBoy.active = true;
                });
            } while (0);
        }
    }

    // 牛仔恢复正常动画
    showCowboyNormalAnim(): void {
        this._heroBoy.active = true;
        this._heroCow.active = true;

        // 重置牛仔输赢动画
        do {
            this._cowWinAction.stop();
            this._cowWinAnim.active = false;

            this._cowLoseAction.stop();
            this._cowLoseAnim.active = false;

            this._boyWinAction.stop();
            this._boyWinAnim.active = false;

            this._boyLoseAction.stop();
            this._boyLoseAnim.active = false;
        } while (0);
    }

    protected showLuckButton(mode: number) {
        if (mode === pf.client.RedPacketLotteryMode.Diamond) {
            console.log('[3in1] no diamond red packet in cowboy');
            return;
        }

        if (!this._luckButton) {
            // this._luckButton = cc.instantiate(this.luckButton_prefab).getComponent(LuckTurntablesButton);
            const luckButtonPrefab = pf.addressableAssetManager.getAsset<cc.Prefab>(
                macros.Assets.LUCK_TURNTABLE_BUTTON
            );
            this._luckButton = cc.instantiate(luckButtonPrefab).getComponent(LuckTurntableButtonControl);
            this._luckButton.setLotteryMode(pf.client.RedPacketLotteryMode.Classical);
            this._btn_redpacket_festival.addChild(this._luckButton.node);
            this._luckButton.node.setPosition(0, 0);
            let pos: cc.Vec2 = cc.Vec2.ZERO;
            this._selfCoin.node.convertToWorldSpaceAR(cc.Vec2.ZERO, pos);
            this._luckButton.setViewData(pos);
        }

        if (this._luckTurntableService.isShowLuckTurntable(mode)) {
            this._btn_redpacket_festival.active = true;
            this._luckButton.updateView(true, this._btn_redpacket_festival_layer);
        } else {
            this._btn_redpacket_festival.active = false;
        }

        // "红包节"提示层是否显隐
        this._btn_redpacket_festival_layer.active = this._btn_redpacket_festival.active;

        // "红包节"状态有变化, 适配底栏按钮位置
        this.adaptiveBetBtnPanel();
    }

    private onTurntableResultNotice(userId: number, mode: number) {
        if (mode === pf.client.RedPacketLotteryMode.Diamond) {
            cc.log('[3in1] no diamond red packet in cowboy');
            return;
        }

        let list: cc.Node[] = this.getPlayerCoinNodesByUid(userId);
        // 桌面没有该玩家
        if (list.length === 0) {
            list.push(this._btnPlayerList.node);
        }
        for (const node of list) {
            // let node = list[i];
            // let pos = node.getParent().convertToWorldSpaceAR(node.getPosition());
            // this._luckButton.showGoldMoveAction(pos, puf.currency_type);

            this._luckButton.runGoldMoveAction(this._btn_redpacket_festival, node);
        }
    }

    playPointAni() {
        let points_num = this._cowboyRoom.roundInfo.changePoints;
        if (points_num < 0) return;

        if (!this.points_node) {
            // this.points_node = cc.instantiate(this.points_ani_prefab);
            // this.points_node = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.HEAD_POINTS_ANI));
            // this.node.addChild(this.points_node, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ANIM_NODE);
            // this.points_node.setPosition(
            //     this.node.convertToNodeSpaceAR(this._selfHeadBg.parent.convertToWorldSpaceAR(this._selfHeadBg.position))
            // );
            // this.points_node.getComponent(cc.Animation).on(
            //     'finished',
            //     (event: cc.Event): void => {
            //         this.resetPointAni();
            //     },
            //     this
            // );
            pf.addressableAssetManager.loadAsset(macros.Dynamic_Assets.HEAD_POINTS_ANI).then((asset: cc.Prefab) => {
                this.points_node = cc.instantiate(asset);
                this.node.addChild(this.points_node, COWBOY_LOCAL_ZORDER.COWBOY_LOCAL_ZORDER_ANIM_NODE);
                this.points_node.setPosition(
                    this.node.convertToNodeSpaceAR(
                        this._selfHeadBg.parent.convertToWorldSpaceAR(this._selfHeadBg.position)
                    )
                );
                this.points_node.getComponent(cc.Animation).on(
                    'finished',
                    (event: cc.Event): void => {
                        this.resetPointAni();
                    },
                    this
                );
                this.points_node.getComponent(HeadPointsAniControl).playPointAni(points_num);
            });
        } else {
            this.points_node.getComponent(HeadPointsAniControl).playPointAni(points_num);
        }
    }

    resetPointAni() {
        // this._cowboyRoom.roundInfo.changePoints = 0;
        if (this.points_node) {
            this.points_node.getComponent(HeadPointsAniControl).resetPointAni();
        }
    }

    setLeftAndRightList() {
        let headBgWidth = this._leftPlayerPanel.getChildByName('player_0').width;
        let bgPosY = [288, 92, -104, -300, -300];
        let headPosY = 15;
        let coinPosY = -67;
        let left_nb_flag = cc.v2(-4, 330);
        let right_nb_flag = cc.v2(-16, 333);
        let w4 = 22; // 下注面板边缘存在3个间隙
        // if (this._isIpad) {
        if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_BROAD) {
            bgPosY = [419, 205, -9, -223, -437];
            left_nb_flag = cc.v2(-4, 461);
            right_nb_flag = cc.v2(-16, 464);
            // } else if (this._isViewX) {
        } else if (this.eGameboyScreenType === MiniGameCommonDef.eGameboyScreenType.GST_SCREEN_NARROW) {
            let baseWidth = cc.winSize.width - 2338;
            w4 = 96 + baseWidth * 0.5;
        } else {
            let baseWidth = cc.winSize.width - 1920;
            w4 = baseWidth > 0 ? 22 + baseWidth * 0.5 : 22;
        }
        this._leftPlayerPanel.getComponent(cc.Widget).left = w4 + headBgWidth * 0.5 - this._leftPlayerPanel.width * 0.5;
        this._rightPlayerPanel.getComponent(cc.Widget).right =
            w4 + headBgWidth * 0.5 - this._rightPlayerPanel.width * 0.5;
        pf.UIUtil.adaptWidget(this._leftPlayerPanel);
        pf.UIUtil.adaptWidget(this._rightPlayerPanel);

        for (let i = 0; i < 5; ++i) {
            let left_img = this._leftPlayerPanel.getChildByName(pf.StringUtil.formatC('player_%d', i));
            let left_txtCoin = this._leftPlayerPanel.getChildByName(pf.StringUtil.formatC('text_coin_%d', i));

            let right_img = this._rightPlayerPanel.getChildByName(pf.StringUtil.formatC('player_%d', i));
            let right_txtCoin = this._rightPlayerPanel.getChildByName(pf.StringUtil.formatC('text_coin_%d', i));

            left_img.setPosition(cc.v2(0, bgPosY[i]));
            left_txtCoin.setPosition(cc.v2(0, bgPosY[i] + coinPosY));

            right_img.setPosition(cc.v2(0, bgPosY[i]));
            right_txtCoin.setPosition(cc.v2(0, bgPosY[i] + coinPosY));

            if (i === 0) {
                let left_imgFlag = left_img.getChildByName('nb_flag');
                let right_imgFlag = right_img.getChildByName('nb_flag');

                left_imgFlag.setPosition(
                    left_img.convertToNodeSpaceAR(left_img.parent.convertToWorldSpaceAR(left_nb_flag))
                );
                right_imgFlag.setPosition(
                    right_img.convertToNodeSpaceAR(right_img.parent.convertToWorldSpaceAR(right_nb_flag))
                );
            }
        }
        this.updateRebateFloatButtonPosition();
    }

    showSwitchTable() {
        if (this._bSwitchTable) return;
        this._bSwitchTable = true;
        // cv.TP.showMsg(
        //     pf.languageManager.getString('MiniGame_Switch_Content'),
        //     cv.Enum.ButtonStyle.TWO_BUTTON,
        //     () => {
        //         cv.MessageCenter.send('HideWebview_ShowWindows', true);
        //         cv.roomManager.setCurrentRoomID(cb.getCowboyRoom().idle_roomid);
        //         cv.GameDataManager.tRoomData.m_bIsReconnectMode = true;
        //         cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
        //         cv.roomManager.RequestJoinRoom();
        //     },
        //     () => {
        //         cv.MessageCenter.send('HideWebview_ShowWindows', true);
        //         this._backToCowboyListScene();
        //     }
        // );
        // cv.TP.setButtonText(cv.Enum.ButtonType.TWO_BUTTON_SWITCH_TABLE);
        cr.commonResourceAgent.commonDialog.showMsg(
            pf.languageManager.getString('MiniGame_Switch_Content'),
            [pf.languageManager.getString('MiniGame_Switch_Table'), pf.languageManager.getString('MiniGame_Exit')],
            async () => {
                pf.app.emit('hideWebview');
                const roomId = this._cowboyRoom.roundInfo.idleRoomId;
                const cowboyService = pf.serviceManager.get(domain.CowboyService);
                await cowboyService.login();
                await this._cowboyRoom.joinRoom(roomId);
            },
            () => {
                pf.app.emit('hideWebview');
                this._backToCowboyListScene();
            }
        );
    }

    // 冷静倒计时
    // onCalmDownShowTip(msg: any) {
    //     let CalmDownLeftSeconds = msg.CalmDownLeftSeconds;
    //     let CalmDownDeadLineTimeStamp = msg.CalmDownDeadLineTimeStamp;

    //     if (CalmDownLeftSeconds <= 0 || CalmDownDeadLineTimeStamp <= 0) {
    //         return;
    //     }

    //     if (CalmDownLeftSeconds > 0) {
    //         let _popSilence = popSilence.getSinglePrefabInst(this.popSilencePre);
    //         _popSilence.getComponent(popSilence).autoShow(cv.Enum.popSilenceType.countDownGame, msg);
    //     }
    // }

    onMsgAdvanceAutobetAdd(usedBetCount: number, autoBetCount: number) {
        console.log(`onMsgAdvanceAutobetAdd ${usedBetCount}/${autoBetCount}`);
        if (this._humanboyAdvancedAuto) {
            this._humanboyAdvancedAuto
                .getComponent(HumanboyAdvancedAutoControl)
                .adaptAdvanceAutoCountPos(this._btnBetAuto.node);
            this._humanboyAdvancedAuto.getComponent(HumanboyAdvancedAutoControl).showAdvanceAutoCount();
        }
    }

    private hideRebateActivity() {
        this._rebateActivity?.hide();
        this.GetDialogHub().processClose();
    }

    private showRebateRewardPopup(reward_amount: { [k: string]: number }) {
        const msgContent = pf.StringUtil.formatC(
            pf.languageManager.getString('minigame_rebate_reward_popup'),
            cr.RebateUtil.getRewardString(reward_amount, '#FFFF00')
        );

        this.GetDialogHub()
            .onInit((node) => {
                const controller = node.getComponent(RebateRewardPopupControl);
                if (controller) {
                    controller.init();
                    controller._playPopup();
                }
            })
            .showPopup({
                popupId: MiniGameDialogPopupId.RebateRewardPopup,
                content: msgContent,
                sureCallback: () => {
                    // cv.dialogMager.processClose();
                    this.GetDialogHub().processClose();
                }
            });
    }

    _onResponseRebateReceiveReward(data: pf.client.Rebate.IRebateNoticeMessage): void {
        // cv.MessageCenter.send("onClaimedReward");

        const list = Object.entries(data.reward_amount);
        if (list.length === 0) {
            return;
        }

        const msgContent = pf.StringUtil.formatC(
            pf.languageManager.getString('Rebate_claim_reward_success'),
            cr.RebateUtil.getRewardString(data.reward_amount,'#FFDE58')
        );

        // cv.dialogMager.showPopup({ popupId: MiniGamePopupId.ToastMsg, content: msgContent }, true);

        this._rebateClaimToast =() => {
            this.GetDialogHub().showPopup(
                {
                    popupId: MiniGameDialogPopupId.ToastMsg,
                    content: msgContent
                },
            true
            );
        };
    }

    private _updateActivityData(
        activityNode: cc.Node,
        activityId: number,
        data: pf.client.IEventStatusClient
    ): void {
        let tag = activityNode.getComponent(TagControl);
        if (tag === null) {
            tag = activityNode.addComponent(TagControl);
        }
        tag.nIdx = activityId;
        activityNode.getComponent(BettingRebateEventControl).showRebateEvent(data);
        
    }

    private getActivityTitle(activityId: number, isDaily: boolean): string {
        switch (activityId) {
            case 1:
                return pf.languageManager.getString('betting_rebate_event_title');
            case 2:
                return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_2');
            case 3:
                return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_3');
            case 4:
                if (isDaily) {
                    return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_4_daily');
                }
                return pf.languageManager.getString('minigame_cowboy_rebate_title_activity_4');
            default: // need update for more cases later
                return pf.languageManager.getString('minigame_cowboy_rebate_title');
        }
    }

    private getActivityContent(activityId: number): string {
        switch (activityId) {
            case 1:
                return pf.languageManager.getString('minigame_cowboy_rebate_content');
            case 2:
                return pf.languageManager.getString('minigame_cowboy_rebate_content_activity_2');
            case 3:
                return pf.languageManager.getString('minigame_cowboy_rebate_content_activity_3');
            case 4:
                return pf.languageManager.getString('minigame_cowboy_rebate_content_activity_4');
            default: // need update for more cases later
                return pf.languageManager.getString('minigame_cowboy_rebate_content');
        }
    }

    private onRebateClicked() {
        if (!this._rebateEventStatus) {
            return;
        }
        const currentTime = Date.now();
        if (currentTime - this._lastTimeClick > 2000) //Throttle the event status request
        {
            this._lastTimeClick = currentTime;
            this._getRebateEventStatus();
        }
     
        const eventType: number = 1;

        this.GetDialogHub()
            .onInit((node) => {
                this._rebateEventStatus.system_time = this._lastSystemTime + Math.floor((Date.now() - this._lastTimeGetRebateEventStatus)/1000);
                this._updateActivityData(node, eventType, this._rebateEventStatus);
            })
            .showPopup({
                popupId: eventType,
                title: this._getEventTitle(),
                content: this.getActivityContent(eventType),
                onAction2: () => {
                    this.GetDialogHub().processClose();
                },
                horizontalAlign: cc.Label.HorizontalAlign.LEFT
            });

    }

    protected _getEventTitle(){
        return cr.RebateUtil.getEventTitle(this._rebateEventStatus);
    }
    
    onMsgRebateActivityActive() {
        if (!cc.isValid(this.node)){
            return;
        }
        const holder = this.node.getChildByName('rebate_float_button');

        if (!this._rebateEventStatus) {
            holder.active = false;
            this.GetDialogHub().processClose();
            return;
        }
        // regenerate new floating button for new event
        if (!this._rebateActivity || this._rebateActivity.eventId !== this._rebateEventStatus.id) {
            holder.destroyAllChildren();

            this._rebateActivity = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.REBATE_FLOATING_BUTTON_NEW)
            ).getComponent(RebateFloatingButtonControl);

            this._rebateCoinsFly = cc.instantiate(
                pf.addressableAssetManager.getAsset(macros.Assets.REBATE_COINS_FLY)
            );

            holder.addChild(this._rebateActivity.node);
            this.node.addChild(this._rebateCoinsFly);         
            this._rebateActivity.init(this._rebateEventStatus.id, true);
            this._rebateActivity.setup(this.onRebateClicked.bind(this));

            this._rebateActivity.node.on('barMaxAnimationEnd', this._boundBarMaxAnimationEnd);
        }

        holder.active = true;
        this._rebateActivity.play(this._rebateEventStatus);

        this._rebateService.emit("eventStatusResult", this._rebateEventStatus);
    }

    tryLeaveRoom(type: pf.client.ExitType = pf.client.ExitType.Standard) {
        if (type === pf.client.ExitType.NoLeaveRoom) {
            console.log('[3in1] cowboy tryLeaveRoom with NoLeaveRoom');
            this.exitGame();
        } else {
            try {
                this._cowboyRoom.leaveRoom();
            } catch (err) {
                cc.warn(err);
            }
        }
    }

    exitGame() {
        cc.log('[Cowboy] exit game');
        this._cleanData();
        pf.bundleManager.exitBundle(macros.BUNDLE_NAME);
    }

    private _getBetCoinShapeByAmount(gold: number): number {
        let llRealGold: number = pf.StringUtil.clientGoldByServer(gold);
        let shape: number =
            llRealGold < this._cowboyRoom.llCoinUICritical
                ? HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_COIN
                : HumanboyBetCoinControl.eHumanboyBetCoinShape.SHAPE_BLOCK;
        return shape;
    }

    private _onPushNotification(notification: PushNotification) {
        const curLanguageContent = pf.StringUtil.getServerStrByLanguage(notification.msg);
        let content = '';
        if (notification.sourceType.length === 0) {
            content = curLanguageContent;
        } else {
            // for (let i = 0; i < notification.sourceType.length; i++) {
            for (const src of notification.sourceType) {
                if (src === pf.client.GameId.CowBoy) {
                    content = curLanguageContent;
                    break;
                }
            }
        }

        if (content.length > 0) {
            this.OnCowboyRewardTips(content);
        }
    }

    private _onRebateStatusNotify(){
        this.unschedule(this._boundGetRebateEventStatus);
        this.scheduleOnce(this._boundGetRebateEventStatus, 0.5);
    }

    private _getRebateEventStatus() {
        this._rebateService.getEventStatus().then((response) => {
            // Filter events for game Cowboy (game_id = 10)
          
            this._rebateEventStatus = cr.RebateUtil.getActiveEventByGame(pf.client.GameId.CowBoy, response.events);
            this.onMsgRebateActivityActive();
            this._lastTimeGetRebateEventStatus = Date.now();
            this._lastSystemTime = this._rebateEventStatus?.system_time || 0;
        });

        this._rebateService.getLeaderBoard().then((response) => {
            this._rebateService.emit("eventLeaderboardResult", response);
        });
    }

    private _onMsgConsumingNotify(msg: network.ILeftGameCoinNotify) {
        if (!this.consumingNotify) {
            const notifyObj = cc.instantiate(pf.addressableAssetManager.getAsset(macros.Assets.CONSUMING_PROMPT));
            this.consumingNotifyHolder.addChild(notifyObj);
            this.consumingNotifyHolder.parent.x = this.self_panel.x - this.self_panel.width / 2;
            this.consumingNotify = notifyObj.getComponent(ConsumingPromptControl);
        }
        this.consumingNotify.show(msg.lost_game_coin, msg.cur_game_coin, 2);
    }

    private _fitSafeArea() {
        const safeArea = pf.system.view.getSafeArea();
        cc.log(`[3in1] cowboy safe area x:${safeArea.x}, y:${safeArea.y}, w: ${safeArea.width}, h: ${safeArea.height}`);
        const panelGame = this._betContentBg.node;
        if (safeArea.width <= 0 || safeArea.height <= 0) {
            if (cr.CommonUtil.isFitSafeAreaNeeded()) {
                cc.log('[3in1] fit safe area fallback');
                this._leftPlayerPanel.setScale(macros.SAFE_AREA_PLAYER_LIST_SCALE, macros.SAFE_AREA_PLAYER_LIST_SCALE);
                this._leftPlayerPanel.setPosition(
                    this._leftPlayerPanel.x + macros.SAFE_AREA_PLAYER_LIST_OFFSET,
                    this._leftPlayerPanel.y
                );
                this._rightPlayerPanel.setScale(macros.SAFE_AREA_PLAYER_LIST_SCALE, macros.SAFE_AREA_PLAYER_LIST_SCALE);
                this._rightPlayerPanel.setPosition(
                    this._rightPlayerPanel.x - macros.SAFE_AREA_PLAYER_LIST_OFFSET,
                    this._rightPlayerPanel.y
                );

                panelGame.setScale(macros.SAFE_AREA_BOARD_SCALE, macros.SAFE_AREA_BOARD_SCALE);
            }
        } else {
            const safeAreaLeftBorder = safeArea.x - macros.safeAreaSurrenderDistance;
            const leftPlayerlistLeftBorder = this._leftPlayerPanel.getComponent(cc.Widget).left;
            // console.log(
            //     `[3in1] safe area left border:${safeAreaLeftBorder}, left playerlist left border:${leftPlayerlistLeftBorder}`
            // );
            if (safeAreaLeftBorder <= leftPlayerlistLeftBorder + macros.noFitBuffer) {
                cc.log('[3in1] no need to fit safe area');
            } else {
                cc.log('[3in1] need to fit safe area');
                const newLeftBorder = safeAreaLeftBorder - macros.noFitBuffer;
                const canvasNode = this.getComponent(cc.Canvas).node;
                const posWorld = canvasNode.convertToWorldSpaceAR(panelGame.position);
                // console.log('[3in1] game panel world pos:', posWorld);
                // console.log('[3in1] winSize:', cc.winSize);
                const newGamePanelLeftBorder = posWorld.x - macros.narrowGamePanelWidth * 0.5;
                const leftPlayerPanelRightBorder =
                    newGamePanelLeftBorder - macros.gapBetweenGamePanelAndPlayerlistPanel;
                const newPlayerlistWidth = leftPlayerPanelRightBorder - newLeftBorder;
                const curPlayerlistWidth = this._leftPlayerPanel.width;
                // console.log(`[3in1] game left:${newGamePanelLeftBorder}, left right:${leftPlayerPanelRightBorder}`);
                // console.log(`[3in1] left panel width cur:${curPlayerlistWidth}, new:${newPlayerlistWidth}`);

                // console.log('[3in1] panel_game w:' + panelGame.width);
                const gameBoardRatio = macros.narrowGamePanelWidth / panelGame.width;
                // console.log('[3in1] game board ratio:' + gameBoardRatio);
                panelGame.setScale(gameBoardRatio, gameBoardRatio);

                const newLeftPlayerlistX = newLeftBorder + newPlayerlistWidth * 0.5;
                const playerlistRatio = newPlayerlistWidth / curPlayerlistWidth;
                if (playerlistRatio < 1) {
                    this._leftPlayerPanel.setScale(playerlistRatio, playerlistRatio);
                }
                let posNew = canvasNode.convertToNodeSpaceAR(cc.v2(newLeftPlayerlistX, 0));
                this._leftPlayerPanel.setPosition(posNew.x, this._leftPlayerPanel.position.y);

                const newGamePanelRightBorder = posWorld.x + macros.narrowGamePanelWidth * 0.5;
                if (playerlistRatio < 1) {
                    this._rightPlayerPanel.setScale(playerlistRatio, playerlistRatio);
                }
                const rightPlayerPanelLeftBorder =
                    newGamePanelRightBorder + macros.gapBetweenGamePanelAndPlayerlistPanel;
                const newRightPlayerlistX = rightPlayerPanelLeftBorder + newPlayerlistWidth * 0.5;
                posNew = this.node.getComponent(cc.Canvas).node.convertToNodeSpaceAR(cc.v2(newRightPlayerlistX, 0));
                this._rightPlayerPanel.setPosition(posNew.x, this._rightPlayerPanel.position.y);
            }
        }
        this.updateRebateFloatButtonPosition();
    }
    private async onBarMaxAnimationEnd() {
        if (this._rebateCoinsFly && this.selfAvatar && this._rebateActivity) {
            if (this._rebateService && this._rebateService.rebateEvents.length > 0) {
                const event = this._rebateService.rebateEvents[0];
                const idx = event.id;
                const betTime = event.setting.bet_time[0];
                
                let rewardProgressIndex = 0;
                let rewardAmount = 0;
                
                if (betTime && betTime.reward_progress) {
                    for (let i = 0; i < betTime.reward_progress.length; i++) {
                        if (betTime.reward_progress[i].can_get && !betTime.reward_progress[i].got) {
                            rewardProgressIndex = i;
                            rewardAmount = betTime.reward_progress[i].reward;
                            break;
                        }
                    }
                }
                
                await this._rebateCoinsFly.getComponent(RebateCoinsFlyControl).playCoinsFlyAnimation(
                    this.selfAvatar.node, 
                    this._rebateActivity.node, 
                    rewardAmount
                );
                this._rebateClaimToast?.();
            }
        }
    }

    protected updateRebateFloatButtonPosition(): void {
        if (this._rebate_float_button) {
            this._rebate_float_button.setPosition(this._rightPlayerPanel.x, this._rebate_float_button.y);
        }
    }
}

export namespace macros {
    export const BUNDLE_NAME = 'cowboy';
    export const SCENE_NANE = 'CowboyScene';
    export const RULE_URL = 'user/article/getimage?img=';
    export const AVATAR_LOCAL_OFFSET = 16;

    // width of cowboy playerlist panel: 200
    // width of icon inside playerlist: 156
    // there is a distance of 22 pixels from edges of icon to edges of playerlist
    export const noFitBuffer = 15;
    export const narrowGamePanelWidth = 1755;
    export const gapBetweenGamePanelAndPlayerlistPanel = 0;
    export const safeAreaSurrenderDistance = 20;

    export const SAFE_AREA_PLAYER_LIST_OFFSET = 38;
    export const SAFE_AREA_PLAYER_LIST_SCALE = 0.9;
    export const SAFE_AREA_BOARD_SCALE = 0.96;

    export enum Addressable_Config_Path {
        ZH_CN = 'configs/cowboy-addressable-assets-zh_cn',
        EN_US = 'configs/cowboy-addressable-assets-en_us'
    }

    export enum Language_String_Path {
        ZH_CN = 'languages/string-zh_cn',
        EN_US = 'languages/string-en_us',
        YN_TH = 'languages/string-yn_th',
        TH_PH = 'languages/string-th_ph',
        HI_IN = 'languages/string-hi_in'
    }

    export enum Assets {
        CONSUMING_PROMPT = 'common-resource.consuming-prompt',
        REBATE_FLOATING_BUTTON = 'common-resource.rebate-floating-button',
        REBATE_FLOATING_BUTTON_NEW = 'common-resource.rebate-floating-button-new',
        REBATE_COINS_FLY = 'common-resource.rebate-coins-fly',
        HUMANBOY_ADVANCED_AUTO = 'common-resource.humanboy-advanced-auto',
        MINI_GAME_DIALOG = 'common-resource.mini-game-dialog',
        LUCK_TURNTABLE_BUTTON = 'common-resource.luck-turntables-button',
        COWBOY_LANGUAGE_ATLAS = 'common-resource.cowboy-language-atlas',
        BET_COIN = 'common-resource.bet-coin',
        CHART_ATLAS = 'common-resource.chart-atlas',
        DZNZ_ATLAS = 'common-resource.dznz-atlas',
        POKER_CARD = 'common-resource.poker-card',
        AVATAR = 'common-resource.avatar',
        WIN_FLAG = 'common-resource.win-flag',
        WIN_PLAYER_LIGHT = 'common-resource.win-player-light',
        COWBOY_TREND_ANIM_ATLAS = 'common-resource.cowboy-trend-anim-atlas',
        CARD_BACK = 'common-resource.card-back',
        SPECIAL_CARD_TYPE_ATLAS = 'common-resource.special-card-type-atlas',
        MINI_GAME_GUIDE = 'common-resource.mini-game-guide',
        MINI_GAME_MENU = 'common-resource.mini-game-menu',

        MINI_GAME_ADVANCED_AUTO = 'common-resource.mini-game-advanced-auto',
        HOLLOW = 'common-resource.hollow',
        DOT = 'common-resource.dot',
        SOLID = 'common-resource.solid',

        // followings prevent type-check from reporting errors
        BET_BUTTON_ATLAS = 'cowboy.bet-button-atlas',
        WIN_CARD_TYPE_ANIMATION = 'cowboy.win-card-type-animation',
        LOSE_CARD_TYPE_ANIMATION = 'cowboy.lose-card-type-animation',
        COWBOY_MENU = 'cowboy.cowboy-menu',
        COWBOY_GAME_GUIDE = 'cowboy.cowboy-game-guide',
        COWBOY_ADVANCED_AUTO = 'cowboy.cowboy-advanced-auto',
        COWBOY_COMMON_DIALOG = 'cowboy.cowboy-common-dialog',
        MINI_GAME_MENU_WITHOUT_EXCHANGE = 'common-resource.mini-game-menu_without-exchange'
    }

    export enum Dynamic_Assets {
        COWBOY_TIPS_BACKGROUND = 'common-resource-dynamic.tips-bg',

        COWBOY_TITLE = 'cowboy-dynamic.cowboy-title',
        BULL_TITLE = 'cowboy-dynamic.bull-title',
        COWBOY_TABLE_SPRITE = 'cowboy-dynamic.cowboy-table-sprite',
        COWBOY_TABLE_BROAD_SPRITE = 'cowboy-dynamic.cowboy-table_broad-sprite',
        COWBOY_TABLE_NARROW_SPRITE = 'cowboy-dynamic.cowboy-table_narrow-sprite',
        COWBOY_CHART_DESCRIPTION = 'common-resource-dynamic.des-img',

        MINI_GAME_EXCHANGE = 'common-resource-dynamic.mini-game-exchange',
        MINI_GAME_RULE = 'common-resource-dynamic.mini-game-rule',
        MINI_GAME_AUDIO_SETTING = 'common-resource-dynamic.mini-game-audio-setting',
        MINI_GAME_ADVANCED_SETTING = 'common-resource-dynamic.mini-game-advanced-setting',
        MINI_GAME_EXIT = 'common-resource-dynamic.mini-game-exit',
        HEAD_POINTS_ANI = 'common-resource-dynamic.head-points-ani',
        MINI_GAME_PLAYER_LIST = 'common-resource-dynamic.mini-game-player-list',

        // followings prevent type-check from reporting errors
        COWBOY_RULE = 'cowboy.cowboy-rule',
        COWBOY_AUDIO_SETTING = 'cowboy.cowboy-audio-setting',
        COWBOY_ADVANCED_SETTING = 'cowboy.cowboy-advanced-setting',
        COWBOY_EXIT = 'cowboy.cowboy-exit',
        COWBOY_PLAYER_LIST = 'cowboy.cowboy-player-list'
    }

    export enum Audio {
        BGM = 'common-resource-audio.bgm', // 1背景
        KAIPAI = 'common-resource-audio.kaipai', // 2发牌、开牌
        FAPAI = 'common-resource-audio.fapai', // 2发牌、开牌
        // START_ROUND = 'common-resource-audio.start-round', // 3出站开战时间提示
        BET = 'common-resource-audio.bet', // 4投金币
        BET_MANY = 'common-resource-audio.bet-many', // 4投金币
        WIN_LOSE = 'common-resource-audio.win-lose', // 5输赢
        GET_WIN_COIN = 'common-resource-audio.get-win-coin', // 6收金币
        PRESS = 'common-resource-audio.press',
        TIME_TICK = 'common-resource-audio.time-tick', // 时间
        BEGIN_BET = 'common-resource-audio.begin-bet', // 开始下注
        END_BET = 'common-resource-audio.end-bet', // 停止下注
        SPECIAL_CARD_TYPE_BIG = 'common-resource-audio.special-card-type-big', // 特殊牌型音效
        BUTTON_CLICK = 'common-resource-audio.button-click',
        TAB = 'common-resource-audio.tab',
        CLOSE = 'common-resource-audio.common-close'
    }

    export enum AudioSettingKeys {
        MUSIC = 'client_music_key',
        SOUND_EFFECT = 'client_sound_key'
    }
}
